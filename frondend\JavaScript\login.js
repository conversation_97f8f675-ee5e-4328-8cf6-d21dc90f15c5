document.getElementById("loginForm").addEventListener("submit", async function (e) {
  e.preventDefault();

  const email = document.getElementById("email").value;
  const password = document.getElementById("password").value;
  const errorMsg = document.getElementById("error-msg");

  try {
    const response = await fetch("../backend/login.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: `email=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}`
    });

    const result = await response.text();

    if (result === "admin") {
      window.location.href = "../frondend/admin_control.html";
    } else if (result === "faculty") {
      window.location.href = "../frondend/faculty_control.html";
    } else {
      errorMsg.textContent = result;
    }
  } catch (err) {
    errorMsg.textContent = "حدث خطأ أثناء الاتصال بالخادم.";
  }
});
