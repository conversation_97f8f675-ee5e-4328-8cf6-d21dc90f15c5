html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
}

.wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.hero-section {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #0d47a1; /* رأس كحلي داكن */
  color: white;
  z-index: 1000;
  padding: 10px 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

main {
  flex: 1; /* يجعل main يأخذ المساحة المتبقية */
   display: flex;
  justify-content: center;  /* توسيط أفقي */
  align-items: center;      /* توسيط عمودي */
  margin-top: 80px;        /* ضروري بسبب الرأس الثابت */
  padding: 20px;
  background-color: #e3f2fd;
  min-height: calc(100vh - 120px - 60px); /* ارتفاع الشاشة - الرأس - التذييل */
  box-sizing: border-box;
}

.login-container {
  background: linear-gradient(to bottom right, #42a5f5, #bbdefb); /* تدرج أزرق */
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  width: 350px;
  text-align: center;
}

h2 {
  color: #ffffff;
  margin-bottom: 20px;
}

input {
  width: 100%;
  padding: 10px;
  margin-bottom: 15px;
  border: none;
  border-radius: 5px;
  background-color: #e3f2fd;
  color: #0d47a1;
}

input::placeholder {
  color: #90caf9;
}

button {
  background-color: #1565c0;
  color: white;
  border: none;
  padding: 12px;
  width: 100%;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

button:hover {
  background-color: #1e88e5;
}

.error {
  color: #ffcdd2;
  font-size: 14px;
  margin-bottom: 10px;
}
