<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faculty Page</title>
    <!-- Linking to the external CSS file -->
    <link rel="stylesheet" href="css/header_footer.css">
    <link rel="stylesheet" href="css/Faculty.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Inline styles for specific elements, remove if handled by external CSS -->

</head>ِ
<body>

<div class="wrapper">
    <!-- استدعاء الهيدر -->
  <div id="header"></div>


    <!-- Main Content -->
    <div class="main-content">
        

        <h1>Personal Page</h1>
        <div class="container">
            <div id="ui-message-area"></div>

            <!-- Messages Section - This will be shown when "Messages" is clicked -->
            <div class="section" id="messages-section">
                <div class="iframe">
                    <!-- The iframe loads masege.html content here -->
                    <iframe id="messagesFrame" src="masege.html" title="Messages Content"></iframe>
                </div>
            </div>


            <!-- All other main sections that appear on the main page -->
            <div class="section" id="personal-info">
                <h2>Personal Information</h2>
                <div class="card">
                    <div class="profile-section">
                        <div class="profile-container">
                            <!-- Removed default image URL -->
                            <img id="personalImage" class="profile-img" src="" alt="Profile photo">
                        </div>
                    </div>
                    <div class="info">
                        <!-- Removed default values from input fields -->
                        <p><strong id="displayFullName"> </strong><input type="text" id="editFullName" class="hidden"></p>
                        <p><strong id="displaySpecialization"> </strong><input type="text" id="editSpecialization" class="hidden"></p>
                        <p><strong id="displayEmail"> </strong><input type="text" id="editEmail" class="hidden"></p>
                        <p><strong id="displayDepartment"> </strong><input type="text" id="editDepartment" class="hidden"></p>
                        <div class="button-group">

                        </div>
                    </div>
                </div>
            </div>

            <!-- New Doctor Description Section -->
            <div class="section" id="doctor-description">
                <h2>Faculty Biography</h2>
                <div class="card">
                    <div class="description-content">
                        <!-- Wrapper to manage height and prevent layout shifts -->
                        <div class="description-text-area-wrapper" id="descriptionTextAreaWrapper">
                            <!-- Display description -->
                            <p id="doctorDescriptionDisplay" class="description-display-text">

                            </p>
                            <!-- Editable textarea for description - NOW HIDDEN BY DEFAULT, removed default content -->
                            <textarea id="doctorDescriptionEdit" class="description-edit-textarea hidden" cols="60" rows="10" ></textarea>
                        </div>


                    </div>
                    <div class="description-image-wrapper">

                        <button class="secondary-btn" id="viewCertificateBtn" style="margin-top: 15px;">View Certificate</button>

                    </div>
                </div>
            </div>

            <div class="section" id="course-materials">
                <h2>Current Semester Courses</h2>
                <div class="card">
                    <div style="width: 100%;">
                        <select id="courseName" onchange="showCourseLectures()">
                            <!-- This is a necessary placeholder for an empty select box -->
                            <option value="" disabled selected>Select Course</option>

                        </select>


                        <div id="courseLectures" class="course-lectures hidden">
                            <h3 id="selectedCourseTitle"></h3>
                            <div id="lecturesList"></div>


                        </div>
                    </div>
                </div>
            </div>

            <div class="section" id="academic-research">
                <h2>Academic Research</h2>
                <div class="card">
                    <div style="width: 100%;">
                        <!-- Removed "Add New Research" button as per user request -->

                        <div id="newResearchForm" class="hidden" style="margin-top: 15px; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
                            <h4>Add New Research</h4>
                            <input type="text" id="newResearchTitle" placeholder="Research Title" required>
                            <input type="date" id="newResearchDate" placeholder="Publication Date" required>
                            <!-- Custom file input for Research File -->
                            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                <input type="file" id="newResearchFileInput" accept=".pdf,.doc,.docx" style="display: none;">
                                <button type="button" class="secondary-btn" id="chooseResearchFileBtn" style="margin-top: 0; margin-right: 5px;">Choose File</button>
                                <!-- Emptied the default text for selected file name -->
                                <span id="researchFileSelectedName" style="margin-left: 10px; color: #555;"></span>
                            </div>
                            <button class="add-btn" onclick="submitNewResearch()">Save Research</button>
                            <button class="delete-btn" onclick="cancelNewResearchForm()">Cancel</button>
                        </div>


                        <div id="researchesList" class="research-form-container" style="margin-top: 20px;">
                            <table class="research-table">
                                <thead>
                                    <tr>
                                        <th>Research Title</th>
                                        <th>Publication Date</th>
                                        <th>Download</th>
                                    </tr>
                                </thead>
                                <tbody id="researchTableBody">
                                    <!-- Removed example row - Actual rows will be populated dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Certificate Modal Structure -->
    <div id="certificateModal" class="modal-overlay">
        <div class="modal-content">
               <div class="modal-content" id="draggableModalContent">
                <span class="close-button" id="closeModalBtn">&times;</span>
                <!-- Removed default certificate image URL -->
                <img id="modalCertificateImage" src="" alt="Doctorate Certificate">
                <!-- Changed "Upload Image" button to "Close" button -->
                <button class="secondary-btn" onclick="document.getElementById('certificateModal').style.display = 'none';" style="margin-top: 15px;"> Close </button>
            </div>
        </div>

    </div>
  <!-- استدعاء الفوتر -->
 <div id="footer"></div>
 </div>
<!-- Linking to the external JavaScript file -->
  <script src="javascript/Faculty.js"></script>
  <script src="javascript/header_footer.js"></script>


</body>

</html>
