@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

        :root {
            --primary-color: #4A90E2; /* لون أزرق أكثر حيوية */
            --secondary-color: #888; /* لون رمادي ثانوي */
            --background-color: #f0f2f5; /* خلفية أفتح */
            --card-background: #ffffff; /* خلفية البطاقة (النافذة المنبثقة والحاوية) */
            --text-color: #333; /* لون النص الأساسي */
            --border-color: #e0e0e0; /* لون الحدود */
            --hover-background: #f5f5f5; /* لون الخلفية عند التحويم */
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* ظل أكثر نعومة */
        }

        body {
            font-family: 'Cairo', sans-serif; /* خط القاهرة */
            background: var(--background-color);
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--text-color);
            direction: ltr; /* اتجاه النص من اليسار إلى اليمين */
        }

        .container {
            max-width: 960px;
            margin: 40px auto;
            background: var(--card-background);
            padding: 30px;
            border-radius: 12px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        h1 {
            text-align: center;
            color: var(--text-color);
            margin-bottom: 25px;
            font-weight: 700;
        }

        /* --- Buttons --- */
        /* --- الأزرار --- */
        .buttons-container {
            text-align: center;
            margin-bottom: 30px;
            display: flex;
            justify-content: center;
            gap: 15px; /* المسافة بين الأزرار */
            flex-wrap: wrap; /* السماح للأزرار بالالتفاف على الشاشات الصغيرة */
        }

        .btn {
            padding: 10px 22px;
            border: none;
            border-radius: 8px; /* زوايا دائرية أكثر قليلاً */
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            color: #fff; /* لون النص الافتراضي للأزرار */
        }

        /* النمط الافتراضي لجميع الأزرار (رمادي افتراضيًا) */
        .btn-default {
            background: var(--secondary-color);
        }

        .btn-default:hover {
            background: #6a6a6a; /* رمادي أغمق عند التحويم */
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        /* نمط الزر النشط (أزرق عندما يكون نشطًا) */
        .btn-active {
            background: var(--primary-color);
        }

        .btn-active:hover {
            background: #3a7bd5; /* درجة أغمق من اللون الأساسي عند التحويم */
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        /* نمط زر الحذف */
        .btn-delete {
            background: #dc3545; /* لون أحمر للحذف */
        }

        .btn-delete:hover {
            background: #c82333; /* أحمر أغمق عند التحويم */
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }


        /* أنماط خاصة بأزرار نموذج النافذة المنبثقة (هذه لا تتبع منطق التنقل الرئيسي) */
        .modal-content .btn {
            width: auto; /* أزرار داخل النافذة المنبثقة لا يجب أن تكون بعرض كامل */
            margin-top: -5px; /* تم تعديل هذا لرفع الأزرار أكثر */
            padding: 10px 25px;
        }

        .modal-content .btn-secondary { /* The "Cancel" button in the modal */
            /* زر "إلغاء" في النافذة المنبثقة */
            margin-right: 10px; /* المسافة بين الأزرار */
            background: var(--secondary-color);
        }
        .modal-content .btn-secondary:hover {
            background: #6a6a6a;
        }


        /* --- أنماط الجدول --- */
        table {
            width: 100%;
            border-collapse: separate; /* للزوايا الدائرية على الخلايا */
            border-spacing: 0;
            margin-bottom: 40px;
            background-color: var(--card-background);
            border-radius: 10px;
            overflow: hidden; /* يضمن ظهور الزوايا الدائرية */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        th, td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: #f9f9f9;
            color: var(--text-color);
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        tbody tr:last-child td {
            border-bottom: none; /* لا توجد حدود للصف الأخير */
        }

        tr:hover {
            background: var(--hover-background);
            transition: background-color 0.2s ease;
        }

        /* Make message rows clickable */
        /* جعل صفوف الرسائل قابلة للنقر */
        .message-row {
            cursor: pointer;
        }


        /* جديد: أنماط الرسائل غير المقروءة */
        .message-row.unread {
            font-weight: bold; /* جعل الرسائل غير المقروءة غامقة */
        }
 
        /* جديد: أنماط الرسائل المقروءة (افتراضي، ولكن يمكن أن تكون صريحة) */
        .message-row.read {
            font-weight: normal; /* الرسائل المقروءة عادية */
        }



        /* نمط مربع الاختيار */
        input[type="checkbox"] {
            transform: scale(1.2); /* جعل مربعات الاختيار أكبر قليلاً */
            margin: 0;
            cursor: pointer;
        }

    
        /* --- النوافذ المنبثقة (نماذج منبثقة) --- */
        .modal, .message-box-overlay, .confirm-box-overlay, .view-message-overlay {
            display: none; /* مخفي افتراضيًا */
            position: fixed;
            z-index: 100; /* مؤشر z أعلى */
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.4); /* تراكب خلفية شبه شفاف */
            display: flex; /* استخدام فليكس بوكس للتوسيط */
            align-items: center;
            justify-content: center;
            opacity: 0; /* لانتقالات سلسة */
            visibility: hidden; /* لانتقالات سلسة */
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .modal.show, .message-box-overlay.show, .confirm-box-overlay.show, .view-message-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* أحجام محتوى النافذة المنبثقة المعدلة للقابلية للنقل */
        .modal-content, .view-message-content {
            background: var(--card-background);
            padding: 30px;
            border-radius: 12px;
            box-shadow: var(--shadow);
            text-align: center;
            width: 90%; /* عرض متجاوب */
            height: auto; /* تم التغيير: الارتفاع يتكيف مع المحتوى */
            max-height: 90vh; /* جديد: أقصى ارتفاع 90% من ارتفاع منفذ العرض */
            min-height: 350px; /* جديد: الحد الأدنى للارتفاع للاستخدام */
            max-width: 600px; /* الاحتفاظ بهذا للنافذة الرئيسية للرسائل ونافذة عرض الرسائل */
            box-sizing: border-box; /* يضمن أن المساحة المتروكة لا تزيد على العرض الكلي */
            display: flex; /* استخدام فليكس بوكس للتخطيط الداخلي */
            flex-direction: column; /* تكديس المحتوى عموديًا */
            justify-content: space-between; /* دفع الأزرار إلى الأسفل */
            cursor: grab; /* يشير إلى أنه قابل للسحب */
            position: fixed; /* تعيين الموضع صراحةً إلى ثابت للسحب */
            /* Removed transform and transition properties as they will be managed by JS for drag */
            /* تمت إزالة خصائص التحويل والانتقال حيث سيتم إدارتها بواسطة JavaScript للسحب */
        }

        /* Smaller sizes for message box and confirm box */
        /* أحجام أصغر لمربع الرسالة ومربع التأكيد */
        .message-box-content, .confirm-box-content {
            background: var(--card-background);
            padding: 30px;
            border-radius: 12px;
            box-shadow: var(--shadow);
            text-align: center;
            width: 90%; /* عرض متجاوب */
            max-width: 400px; /* أقصى عرض أصغر لهذه النوافذ المنبثقة المحددة */
            height: auto; /* تم التغيير: الارتفاع يتكيف مع المحتوى */
            max-height: 80vh; /* جديد: أقصى ارتفاع 80% من ارتفاع منفذ العرض */
            min-height: 180px; /* جديد: الحد الأدنى للارتفاع للنوافذ المنبثقة الأصغر */
            box-sizing: border-box; /* يضمن أن المساحة المتروكة لا تزيد على العرض الكلي */
            display: flex; /* استخدام فليكس بوكس للتخطيط الداخلي */
            flex-direction: column; /* تكديس المحتوى عموديًا */
            justify-content: space-between; /* دفع الأزرار إلى الأسفل */
            position: fixed; /* تعيين الموضع صراحةً إلى ثابت للسحب */
            /* Removed transform and transition properties as they will be managed by JS for drag */
            /* تمت إزالة خصائص التحويل والانتقال حيث سيتم إدارتها بواسطة JavaScript للسحب */
        }


        /* يتم التعامل مع هذه الآن بواسطة JavaScript للتوسيط الأولي والسحب */
        .modal.show .modal-content,
        .message-box-overlay.show .message-box-content,
        .confirm-box-overlay.show .confirm-box-content,
        .view-message-overlay.show .view-message-content {
            opacity: 1;
 
            /* تمت إزالة التحويل ويتم التعامل معه بواسطة JavaScript لتحديد الموضع */
        }

        .close {
            position: absolute;
            top: 10px;
            right: 10px;
            color: var(--secondary-color);
            font-size: 28px;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .close:hover {
            color: #333;
        }

        h2 {
            margin-top: 10px;
            margin-bottom: 20px;
            text-align: center;
            color: var(--text-color);
            font-weight: 600;
        }

        label {
            display: block;
            margin: 10px 0 5px;
            font-weight: 600;
            color: var(--text-color);
        }


        /* جديد: محاذاة عناصر النموذج في نافذة الرسالة الجديدة إلى اليسار */
        #newMessageForm {
            text-align: left; /* محاذاة محتوى النموذج إلى اليسار */
            margin-top: -15px; /* تم تعديل هذا لرفع محتوى النموذج */
        }
        /* Ensure labels and inputs within the form are explicitly left-aligned if needed */
        /* التأكد من محاذاة التسميات والمدخلات داخل النموذج صراحةً إلى اليسار إذا لزم الأمر */
        #newMessageForm label,
        #newMessageForm input,
        #newMessageForm textarea {
            text-align: left;
        }


        input[type="text"],
        input[type="email"],
        textarea {
            width: calc(100% - 20px); /* حساب المساحة المتروكة */
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            font-size: 0.95rem;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        input[type="text"]:focus,
        input[type="email"]:focus,
        textarea:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
            outline: none;
        }

        textarea {
            resize: vertical; /* السماح بتغيير الحجم عموديًا */
            min-height: 80px;
            max-height: 150px;
        }

        /* Style for read-only 'From' field */
        /* نمط حقل "من" للقراءة فقط */
        input[readonly] {
            background-color: #f0f0f0;
            cursor: not-allowed;
        }


        /* نمط إدخال الملف المخصص - هذه الأنماط لم تعد تستخدم مباشرة ولكن تم الاحتفاظ بها للسياق إذا لزم الأمر */
        .file-input-wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 5px;
            background-color: #fff;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .file-input-wrapper:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
            outline: none;
        }

        .custom-file-button {
            background-color: var(--primary-color);
            color: white;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            white-space: nowrap;
        }

        .custom-file-button:hover {
            background-color: #3a7bd5;
        }

        .file-name-display {
            flex-grow: 1;
            color: var(--secondary-color);
            font-size: 0.95rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }


        /* أنماط مربع الرسالة ومربع التأكيد */
        .message-box-content, .confirm-box-content {
            direction: ltr; /* تعيين الاتجاه لمحتوى مربع الرسالة */
        }
        .message-box-content h3, .confirm-box-content h3, .view-message-content h3 {
            margin-top: 0;
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 20px;
        }
        .confirm-box-content h3 {
            color: #dc3545; /* أحمر لعنوان التأكيد */
        }


        /* تم التعديل: حجم خط أصغر للرسائل التوضيحية */
        .message-box-content p, .confirm-box-content p {
            margin-bottom: 30px;
            font-size: 1.1rem; /* حجم خط أصغر */
            color: var(--text-color);
        }

        .message-box-content .btn, .confirm-box-content .btn, .view-message-content .btn {
            background: var(--primary-color);
            color: white;
            padding: 10px 25px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin: 0 5px; /* المسافة لأزرار التأكيد */
        }

        .message-box-content .btn:hover, .view-message-content .btn:hover {
            background: #3a7bd5;
        }
        .confirm-box-content .btn-confirm {
            background: #dc3545; /* أحمر لتأكيد الحذف */
        }
        .confirm-box-content .btn-confirm:hover {
            background: #c82333;
        }
        .confirm-box-content .btn-cancel {
            background: var(--secondary-color);
        }
        .confirm-box-content .btn-cancel:hover {
            background: #6a6a6a;
        }


        /* أنماط نافذة عرض الرسالة المنبثقة */
        .view-message-content {
            text-align: left; /* محاذاة النص إلى اليسار لسهولة القراءة */
            direction: ltr; /* اتجاه النص من اليسار إلى اليمين */
            border: 1px solid var(--border-color); /* إضافة حدود خفيفة */
            overflow-y: auto; /* السماح بالتمرير إذا تجاوز المحتوى */
            padding-top: 15px; /* تم تعديل هذا لرفع محتوى نموذج عرض تفاصيل الرسالة */
        }

        .view-message-content h3 {
            color: var(--primary-color); /* إبراز العنوان */
            border-bottom: 2px solid var(--border-color); /* فاصل تحت العنوان */
            padding-bottom: 10px; /* تم تقليل المسافة هنا */
            margin-top: -10px; /* تم تعديل هذا لرفع محتوى العنوان */
            margin-bottom: 15px; /* تم تقليل المسافة هنا */
            text-align: center; /* توسيط العنوان */
        }
        

        /* يجب أن يكون كل سطر تفاصيل عبارة عن كتلة (سطر جديد) */
        .view-message-content .message-detail {
            margin-bottom: 5px; /* تم تقليل المسافة بين الحقول هنا */
            display: flex; /* استخدام فليكس بوكس لمحاذاة التسمية والقيمة */
            align-items: flex-start; /* محاذاة العناصر إلى البداية للمحتوى متعدد الأسطر */
        }

 
        /* أنماط لـ strong (التسميات) و span (القيم) داخل تفاصيل الرسالة */
        .view-message-content .message-detail strong {
            flex-shrink: 0; /* منع التسمية من الانكماش */
            width: 90px; /* عرض متزايد قليلاً لمحاذاة التسمية المتناسقة */
            margin-right: 15px; /* مسافة متزايدة بين التسمية والقيمة */
            text-align: right; /* محاذاة نص التسمية إلى اليمين داخل مربعها */
            color: var(--text-color); /* التأكد من تناسق لون التسمية */
            font-weight: 700; /* جعل التسميات أكثر سمكًا */
        }

        .view-message-content .message-detail span {
            flex-grow: 1; /* السماح لـ span بأخذ المساحة المتبقية */
            text-align: left; /* التأكد من محاذاة نص القيمة إلى اليسار */
            direction: ltr; /* اتجاه النص من اليسار إلى اليمين للمحتوى */
            unicode-bidi: embed; /* يضمن تطبيق الاتجاه بشكل صحيح */
            word-wrap: break-word; /* التأكد من التفاف النص الطويل */
            color: var(--text-color); /* لون نص متناسق */
        }

    
        /* لأقسام المحتوى والمرفقات، نريد التسمية على سطرها الخاص */
        .view-message-content .content-detail,
        .view-message-content .attachment-detail {
            display: block; /* يضمن أن هذه الأقسام تأخذ عرضًا كاملاً وتتكدس */
            margin-top: 10px; /* تم تقليل المسافة هنا */
            margin-bottom: 5px; /* تم تقليل المسافة هنا */
            border-top: 1px dashed var(--border-color); /* إضافة خط فاصل متقطع خفيف */
            padding-top: 10px; /* تم تقليل المسافة هنا */
        }
        .view-message-content .attachment-detail {
            margin-bottom: 10px; /* تم تقليل المسافة هنا */
        }

        .view-message-content .content-detail strong,
        .view-message-content .attachment-detail strong {
            display: block; /* جعل علامة strong عنصر كتلة */
            width: auto; /* إزالة العرض الثابت */
            text-align: left; /* محاذاة التسمية إلى اليسار */
            margin-right: 0; /* إزالة الهامش الأيمن */
            margin-bottom: 5px; /* تم تقليل المسافة هنا */
            font-size: 1.1rem; /* حجم خط أكبر قليلاً لتسميات المحتوى/المرفقات */
            color: var(--primary-color); /* جعل عناوين هذه الأقسام مميزة */
        }

        .view-message-content pre {
            background-color: #f9f9f9;
            border: 1px solid var(--border-color);
            padding: 15px;
            border-radius: 8px;
            white-space: pre-wrap; /* الحفاظ على المسافات البيضاء ولف النص */
            word-wrap: break-word; /* كسر الكلمات الطويلة */
            max-height: 250px; /* ارتفاع أقصى متزايد قليلاً لمحتوى الرسالة */
            overflow-y: auto; /* التمرير إذا تجاوز المحتوى */
            direction: ltr; /* اتجاه النص من اليسار إلى اليمين لمحتوى الرسالة */
            font-size: 1rem;
            line-height: 1.7; /* تحسين قابلية قراءة محتوى الرسالة */
            color: #555; /* لون نص أكثر نعومة قليلاً للمحتوى */
        }

        .view-message-content .attachment-link {
            display: block; /* جعله عنصر كتلة ليأخذ عرضًا كاملاً */
            margin-top: 15px;
            color: var(--primary-color);
            text-decoration: none;
            text-align: left; /* محاذاة الرابط إلى اليسار */
            font-weight: 600; /* جعل رابط المرفق أكثر سمكًا */
        }
        .view-message-content .attachment-link:hover {
            text-decoration: underline;
        }

    
        /* --- الأزرار في نافذة عرض الرسالة المنبثقة --- */
        .view-message-content .btn {
            padding: 8px 18px; /* مساحة داخلية أصغر */
            font-size: 0.9rem; /* حجم خط أصغر */
            margin: 0 5px; /* الحفاظ على الهامش الأفقي */
            display: inline-block; /* جعلها تجلس بجانب بعضها البعض */
            width: auto; /* التأكد من تعديل العرض تلقائيًا */
        }

      
        /* --- التصميم المتجاوب --- */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 20px;
            }

            h1 {
                font-size: 1.8rem;
            }

            .btn {
                font-size: 0.9rem;
                padding: 9px 18px;
            }

            th, td {
                padding: 10px;
                font-size: 0.9rem;
            }

            
            /* تعديل المساحة المتروكة لمحتوى النافذة المنبثقة (رسالة جديدة/عرض رسالة) */
            .modal-content, .view-message-content {
                padding: 15px; /* تعديل المساحة المتروكة للشاشات الأصغر */
            }

            
            /* تعديل خاص للمساحة المتروكة/العرض لمربع الرسالة ومربع التأكيد لشاشات 768 بكسل */
            .message-box-content, .confirm-box-content {
                max-width: 350px; /* أقصى عرض أصغر لهذه النوافذ المنبثقة المحددة */
                padding: 20px; /* تعديل المساحة المتروكة للشاشات الأصغر */
            }

            h2 {
                font-size: 1.5rem;
            }

            label {
                font-size: 0.9rem;
            }

            input[type="text"], input[type="email"], textarea {
                padding: 8px;
                font-size: 0.9rem;
                margin-bottom: 8px;
            }

            textarea {
                min-height: 70px;
                max-height: 120px;
            }

            .modal-content .btn {
                padding: 8px 20px;
                font-size: 0.9rem;
            }

            .modal-content .btn-secondary {
                margin-right: 10px;
            }

            .file-input-wrapper {
                padding: 4px;
            }

            .custom-file-button {
                padding: 7px 12px;
                font-size: 0.9rem;
            }

            .file-name-display {
                font-size: 0.9rem;
            }

            
            /* تعديل حجم الخط لفقرة مربع الرسالة في الشاشات الأصغر */
            .message-box-content p, .confirm-box-content p {
                font-size: 1.1rem; /* تم تقليله أكثر لشاشات 768 بكسل */
            }

            
            /* تعديلات على محتوى عرض الرسالة في الشاشات الأصغر */
            .view-message-content .message-detail strong {
                width: 70px; /* تقليل عرض التسمية أكثر على الشاشات الأصغر */
                margin-right: 10px;
            }
            .view-message-content .message-detail span {
                max-width: calc(100% - 80px); /* تعديل أقصى عرض لـ span */
            }
            .view-message-content pre {
                max-height: 150px; /* تقليل ارتفاع pre على الشاشات الأصغر */
            }
        }

        @media (max-width: 480px) {
            .buttons-container {
                flex-direction: column;
                gap: 10px;
            }

            .btn {
                width: 100%; /* أزرار بعرض كامل على الشاشات الصغيرة جدًا */
            }

            /* تعديل المساحة المتروكة لمحتوى النافذة المنبثقة (رسالة جديدة/عرض رسالة) */
            .modal-content, .view-message-content {
                padding: 10px; /* تعديل المساحة المتروكة للشاشات الصغيرة جدًا */
            }

            /* تعديل خاص للمساحة المتروكة/العرض لمربع الرسالة ومربع التأكيد لشاشات 480 بكسل */
            .message-box-content, .confirm-box-content {
                max-width: 300px; /* أقصى عرض أصغر لهذه النوافذ المنبثقة المحددة */
                padding: 15px; /* تعديل المساحة المتروكة للشاشات الصغيرة جدًا */
            }

            h2 {
                font-size: 1.3rem;
                margin-bottom: 15px;
            }

            label {
                font-size: 0.85rem;
                margin: 8px 0 4px;
            }

            input[type="text"], input[type="email"], textarea {
                padding: 7px;
                font-size: 0.85rem;
                margin-bottom: 7px;
            }

            textarea {
                min-height: 60px;
                max-height: 100px;
            }

            .modal-content .btn {
                width: 100%;
                margin-top: 8px;
                padding: 7px 15px;
                font-size: 0.85rem;
            }

            .modal-content .btn-secondary {
                margin-right: 0;
            }

            .file-input-wrapper {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            .custom-file-button {
                width: 100%;
                text-align: center;
            }
            .file-name-display {
                width: 100%;
            }

            /* تعديل حجم الخط لفقرة مربع الرسالة في الشاشات الصغيرة جدًا */
            .message-box-content p, .confirm-box-content p {
                font-size: 1.1rem; /* تم تقليله أكثر لشاشات 480 بكسل */
            }

            /* تعديلات إضافية على محتوى عرض الرسالة في الشاشات الصغيرة جدًا */
            .view-message-content .message-detail strong {
                width: 60px; /* عرض تسمية أصغر حتى */
                margin-right: 8px;
            }
            .view-message-content .message-detail span {
                max-width: calc(100% - 70px); /* تعديل أقصى عرض لـ span */
            }
            .view-message-content pre {
                max-height: 150px; /* تقليل ارتفاع pre على الشاشات الأصغر */
            }
        }
