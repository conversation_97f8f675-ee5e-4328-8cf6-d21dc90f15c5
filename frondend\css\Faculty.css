/* CSS Styles for the sidebar and main content */
body {
    font-family: 'Arial', sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
    color: #000;
    display: flex; /* Ensures sidebar and main content are side-by-side */
    min-height: 100vh; /* Ensures body takes full viewport height */
}

/* Sidebar Styles - Updated (Resized and Lighter) */
/* Modified: Completely hide the sidebar as per request 5 */
.sidebar {
    display: none !important; /* Force hide the sidebar */
    width: 200px; /* Smaller width for the sidebar */
    background: linear-gradient(135deg, #4a6275 0%, #3d5060 100%); /* Lighter soft gradient */
    color: white;
    height: 100vh;
    position: fixed; /* Keeps sidebar fixed on scroll */
    left: 0;
    top: 0;
    padding: 20px 0;
    box-shadow: 4px 0 15px rgba(0,0,0,0.2); /* More prominent shadow */
    transition: transform 0.3s ease-in-out; /* Smooth slide-in/out transition */
    z-index: 1000; /* Ensures sidebar is above other content */
    border-top-right-radius: 15px; /* Rounded corner */
    border-bottom-right-radius: 15px; /* Rounded corner */
    overflow-y: auto; /* Enable scrolling for long content */
    flex-direction: column; /* Kept for structure if display were to be changed */
}

.sidebar-header {
    text-align: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1); /* Lighter border */
    margin-bottom: 20px;
    font-size: 24px; /* Larger header */
    font-weight: bold;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
}

.sidebar-nav {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    list-style: none;
    padding: 0 15px;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 8px; /* More space between items */
}

.sidebar-nav a {
    color: #bdc3c7; /* Gray text color for inactive links */
    text-decoration: none;
    display: flex; /* Use flex for alignment */
    align-items: center;
    padding: 12px 15px;
    border-radius: 8px; /* Rounded links */
    transition: all 0.3s ease;
    position: relative; /* For the active indicator */
}

.sidebar-nav a:hover {
    background-color: rgba(255,255,255,0.15); /* Subtle hover effect */
    transform: translateX(5px); /* Slight slide on hover */
    color: #ecf0f1; /* Lighter gray on hover */
}

.sidebar-nav a.active {
    background-color: #3498db; /* Vibrant blue active color */
    font-weight: bold;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* Shadow for active link */
    color: white; /* White text for active link */
    transform: translateX(0); /* Reset transform for active state */
    /* Add a small indicator to the right */
    border-right: 5px solid #2980b9; /* Darker blue highlight */
}

/* Main Content Styles */
/* Adjusted to take full width when sidebar is hidden */
.main-content {
    flex: 1; /* Allows main content to take remaining space */
    margin-left: 0 !important; /* Ensure no left margin */
    width: 100% !important; /* Ensure full width */
    padding: 20px;
    transition: margin-left 0.3s ease-in-out; /* Smooth transition for margin adjustment */
}

h1, h2 {
    text-align: center;
    color: #000;
    margin-bottom: 20px;
}

.container {
    max-width: 800px;
    margin: auto;
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    color: #000;
}

.section {
    margin-bottom: 30px;
    scroll-margin-top: 20px;
}

.card {
    background: #e9ecef;
    padding: 20px;
    margin: 10px 0;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 20px;
    color: #000;
}

.profile-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 150px;
}

.profile-container {
    width: 120px; /* Adjusted width for image */
    height: 120px; /* Adjusted height for image */
    border: 3px solid #007bff;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f8f9fa;
}

.profile-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.info {
    flex: 1;
    text-align: left;
}

.info p {
    margin: 6px 0; /* Reduced margin here for less spacing */
    font-size: 16px; /* Font size for better balance */
    line-height: 1.6;
    color: #000;
}

.info strong {
    color: #000;
    font-weight: bold;
}

input, select {
    width: calc(100% - 22px);
    padding: 10px;
    margin: 8px 0;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    color: #000;
}

.info input[type="text"] {
    width: calc(100% - 22px);
    padding: 8px;
    font-size: 16px; /* Adjusted input font size as well */
    margin-bottom: 5px;
}

button {
    background: #1e88e5;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
    margin-top: 10px;
    margin-right: 5px;
}

button:hover {
    background: #1565c0;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* Specific button styling for the profile section */
.profile-section button {
    padding: 8px 14px; /* Adjusted padding for consistent button size */
    font-size: 13px; /* Adjusted font size for consistent button size */
}

/* Specific button styling for the info section within personal-info */
.personal-info .info .button-group button {
    padding: 8px 14px; /* Adjusted padding for consistent button size */
    font-size: 13px; /* Adjusted font size for consistent button size */
}

/* Styling for the new description and certificate section */
.doctor-description .card {
    flex-direction: row-reverse; /* Modified to keep image in place */
    align-items: flex-start;
    text-align: right; /* Align text right for Arabic */
}

.doctor-description .description-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-end; /* Align description content to the right */
    padding: 20px;
    min-height: 250px; /* Ensure minimum height */
}

/* Container to manage height and prevent layout shifts */
.description-text-area-wrapper {
    width: 100%;
    min-height: 200px; /* Increased minimum height */
    position: relative;
    overflow: hidden; /* Hide overflow during transitions */
    /* لا يوجد انتقال للارتفاع هنا لأن الارتفاع ثابت بالحد الأدنى */
    font-family: 'Arial', sans-serif;
    font-size: 16;
    line-height: 2;
}

/* Styles for the text display and textarea */
.doctor-description .description-display-text,
.doctor-description .description-edit-textarea {
    margin: 0;
    font-size: 14px; /* Increased font size for better readability */
    line-height: 1.5;
    color: #333;
    text-align:left;
    box-sizing: border-box; /* Include padding/border in dimensions */
    width: 100%; /* Changed from 200% to 100% to prevent overflow */
    position: absolute;
    height: 100%;
    min-height: 200px; /* Increased minimum height */
    padding: 15px; /* Added padding for better appearance */
    border: 2px solid transparent; /* Added for consistent layout */
    /* الانتقال هنا لخاصية الشفافية فقط */
    transition: all 0.3s ease; 
}

/* Default state: display text visible, textarea hidden */
.doctor-description .description-display-text {
    display: block; /* افتراض مرئي */
    opacity: 1; /* افتراض شفافية كاملة */
    background: #f9f9f9;
    border: 2px solid #eee;
    font-size: 16px;
    line-height: 1.5;
    border-radius: 8px;
}
.doctor-description .description-edit-textarea {
    display: none; /* مخفي مبدئي */
    opacity: 0; /* شفافية صفرية مبدئي */
    min-height: 200px; /* Ensure a minimum height for textarea */
    border: 2px solid #1e88e5; /* More prominent border */
    border-radius: 8px;
    padding: 15px; /* Specific padding for textarea */
    resize: vertical; /* Allow vertical resizing */
    box-shadow: 0 4px 15px rgba(30, 136, 229, 0.2); /* Added shadow for depth */
    font-size: 16px; /* Larger font for editing */
    line-height: 1.5;
    text-align: left;
    background: #fff;
}

.doctor-description .description-edit-textarea:focus {
    border: 2px solid #007bff; /* More prominent border when focused */
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.4); /* Add subtle shadow for better look */
    outline: none; /* Remove default focus outline */
}

/* تعديل بسيط لحاوية الصورة */
.doctor-description .description-image-wrapper {
    width: 200px; /* Changed from 200% to 40% for better layout */
    min-width: 200px; /* Ensure a minimum width for the image wrapper */
    max-width: 300px; /* Ensure a maximum width for the image wrapper */
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* تعديل حاوية الشهادة */
.doctor-description .certificate-container {
    width: 100px; /* حجم ثابت للحاوية */
    height: 100px;     /* حجم ثابت للحاوية */
    max-width: 100px; /* لضمان عدم تجاوز هذا الحجم */
    max-height: 100px; /* لضمان عدم تجاوز هذا الحجم */
    border: 2px solid #007bff;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: white;
    margin: 0 auto; /* توسيط الحاوية داخل الـ 40% المخصصة لها */
}

/* تعديل صورة الشهادة */
.doctor-description .certificate-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain; /* تأكد من احتواء الصورة داخل الحاوية مع الحفاظ على الأبعاد */
    display: block; /* للتأكد من عدم وجود مسافات إضافية */
}

/* إضافة تأثير تكبير عند التحويم */
.doctor-description .certificate-container:hover .certificate-img {
    transform: scale(1.05);
}

/* تعديل زر تحميل الصورة */
.doctor-description .description-image-wrapper .secondary-btn {
    width: 100%; /* عرض كامل */
    padding: 12px 0; /* تباعد عمودي فقط */
    font-size: 15px;
    text-align: center;
}

.doctor-description .button-group {
    display: flex;
    gap: 10px;
    margin-top: 15px; /* Adjust margin-top if needed to push down */
    justify-content: flex-end; /* Align buttons to the right */
    width: 100%; /* Take full width of parent column */
}

/* تعديلات للشاشات الصغيرة */
@media (max-width: 768px) {
    /* Modified: Hamburger menu, sidebar and overlay are hidden */
    .menu-toggle, .sidebar, .sidebar-overlay {
        display: none !important;
    }
    
    /* Ensure main content takes full width on small screens too */
    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }
    
    .doctor-description .card {
        flex-direction: column; /* ترتيب عمودي على الشاشات الصغيرة */
    }
    
    .doctor-description .description-content,
    .doctor-description .description-image-wrapper {
        flex: 1 1 100%; /* تأخذ عرض كامل */
        width: 100%;
    }
    
    .doctor-description .certificate-container {
        width: 80px; /* حجم أصغر للحاوية على الشاشات الصغيرة */
        height: 80px; /* ليتناسب مع العرض الجديد */
        max-width: 80px; /* لضمان عدم تجاوز هذا الحجم */
        max-height: 80px; /* لضمان عدم تجاوز هذا الحجم */
        margin: 0 auto;
    }
}

.delete-btn {
    background: #dc3545;
}

.delete-btn:hover {
    background: #c82333;
}

.primary-btn { background: #1976d2; }
.secondary-btn { background: #42a5f5; }
.add-btn { background: #2196f3; }
.edit-btn { background: #64b5f6; }
.cancel-btn { background: #6c757d; }
.cancel-btn:hover { background: #5a6268; }

ul {
    list-style-type: none;
    padding: 0;
}

li {
    padding: 15px;
    background: #fff;
    margin: 10px 0;
    border-radius: 5px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
    position: relative;
    color: #000;
}

.lecture-item {
    margin-bottom: 10px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.lecture-item span {
    flex-grow: 1;
    font-weight: bold;
}

.lecture-item-title {
    font-weight: bold;
    flex-grow: 1;
    margin: 0;
}

.lecture-item-link {
    color: #1e88e5;
    text-decoration: none;
    margin: 0 10px;
    white-space: nowrap;
}

.lecture-item-link:hover {
    text-decoration: underline;
}

.lecture-actions {
    display: flex;
    gap: 5px;
}

.lecture-actions button,
.lecture-actions a {
    padding: 5px 10px;
    margin: 0;
    font-size: 12px;
}

.course-lectures, .research-form-container {
    width: 100%;
    margin-top: 15px;
}

.hidden {
    display: none;
}

a {
    color: #000;
    text-decoration: underline;
}

a:hover {
    color: #0056b3;
}

.research-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.research-table th {
    background-color: #e9ecef;
    padding: 12px;
    text-align: left;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
}

.research-table td {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.research-table tr:last-child td {
    border-bottom: none;
}

.research-table tr:hover {
    background-color: #f8f9fa;
}

.research-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.download-btn { /* New style for the download button in table */
    background: #64B5F6; /* Light blue background */
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 13px;
    text-decoration: none; /* Remove underline for button-like link */
    display: inline-block; /* Allow padding and margin */
}

.download-btn:hover {
    background: #42A5F5; /* Darker blue on hover */
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.research-table button {
    margin: 0;
    padding: 6px 12px;
    font-size: 13px;
}

#ui-message-area {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 25px;
    border-radius: 8px;
    z-index: 1000;
    box-shadow: 0 3px 15px rgba(0,0,0,0.15);
    font-size: 15px;
    text-align: center;
    display: none; /* Hidden by default */
}

#customConfirmModal {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

#customConfirmModal > div {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
    min-width: 300px;
    color: #000;
}

#customConfirmModal p {
    margin-bottom: 20px;
    font-size: 16px;
}

#customConfirmModal button {
    margin: 0 5px;
}

.button-group {
    margin-top: 15px;
}

/* Hamburger menu button */
/* Modified: Hide the hamburger menu button as per request 5 */
.menu-toggle {
    display: none !important; /* Force hide the hamburger menu */
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001; /* Above sidebar and overlay */
    cursor: pointer;
    font-size: 32px; /* Larger icon for better touch target */
    color: #2c3e50;
    background-color: white;
    padding: 5px 10px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    line-height: 1; /* Adjust line height for icon centering */
}

/* Overlay to dim background when sidebar is open on mobile */
/* Modified: Hide the sidebar overlay as per request 5 */
.sidebar-overlay {
    display: none !important; /* Force hide the sidebar overlay */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999; /* Below sidebar, above main content */
}

/* Styles for messages section - hidden by default */
#messages-section {
    display: none; /* Hidden by default, will be controlled by JS */
}

/* Specific styles for the iframe within the messages section */
/* أنماط خاصة لقسم الرسائل */
#messages-section {
    display: none;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    padding: 0;
    overflow: hidden;
}


#messagesFrame {
    width: 100%;
    height: 80%;
    min-height: 600px;
    border: none;
    background: white;
    border-radius: 0 0 10px 10px;
}

/* Media query for smaller screens */
@media (max-width: 768px) {
    /* Adjust body display for smaller screens */
    body {
        flex-direction: column;
        position: relative;
    }

    /* Modified: Force hide sidebar, menu toggle, and overlay on smaller screens too */
    .sidebar {
        transform: translateX(-100%); /* Kept transform for consistency, but display: none overrides */
        box-shadow: none;
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important; /* Ensure no left margin */
        width: 100% !important; /* Ensure full width */
    }

    .menu-toggle {
        display: none !important; /* Force hide menu toggle */
    }

    /* When sidebar is open on smaller screens */
    body.sidebar-open .sidebar {
        transform: translateX(0);
        box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    }

    body.sidebar-open .sidebar-overlay {
        display: none !important; /* Force hide overlay */
    }

    /* Prevent background scrolling when sidebar is open */
    body.sidebar-open {
        overflow: hidden;
    }

    /* Adjust description content for mobile */
    .doctor-description .card {
        flex-direction: column; /* Stack image and text vertically on mobile */
        align-items: center; /* Center items */
        text-align: center; /* Center text */
    }

    .doctor-description .description-content {
        align-items: center; /* Center content */
    }

    .doctor-description .description-content p {
        text-align: center; /* Center text */
    }

    .doctor-description .button-group {
        justify-content: center; /* Center buttons */
    }
}

/* أنماط خاصة لزر الرسائل في القائمة الجانبية */
.sidebar-link[data-section="messages-section"] {
    border: 2px solid #007bff; /* حدود زرقاء */
    border-radius: 8px; /* زوايا مستديرة */
    background-color: rgba(0, 123, 255, 0.1); /* خلفية زرقاء شفافة */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* ظل خفيف */
    transition: all 0.3s ease; /* تأثير انتقالي سلس */
}

/* تأثير التحويم على زر الرسائل */
.sidebar-link[data-section="messages-section"]:hover {
    background-color: rgba(0, 123, 255, 0.2); /* خلفية أغمق عند التحويم */
    border-color: #0056b3; /* لون حدود أغمق عند التحويم */
    transform: translateY(-2px); /* تأثير ارتفاع طفيف */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* ظل أكبر */
}

/* تأثير النشاط على زر الرسائل */
.sidebar-link[data-section="messages-section"].active {
    background-color: #007bff; /* خلفية زرقاء كاملة عند النشاط */
    color: white; /* لون النص أبيض */
    border-color: #0056b3; /* لون حدود أغمق */
    box-shadow: 0 4px 10px rgba(0, 123, 255, 0.3); /* ظل أزرق */
}

/* أنماط زر تسجيل الخروج */
.logout-item {
    margin-top: auto; /* دفع العنصر إلى أسفل القائمة */
    padding-top: 20px; /* إضافة مسافة فوق زر تسجيل الخروج */
    margin-bottom: 20px; /* إضافة مسافة أسفل زر تسجيل الخروج */
    border-top: 1px solid rgba(255, 255, 255, 0.1); /* خط فاصل فوق الزر */
    border-bottom: 2px solid rgba(255, 255, 255, 0.1); /* خط فاصل تحت الزر */
}

.logout-link {
    color: #fd2626 !important; /* لون أحمر فاتح للنص */
    background-color: rgba(255, 107, 107, 0.1); /* خلفية حمراء شفافة */
    border: 1px solid rgba(255, 107, 107, 0.3); /* حدود حمراء شفافة */
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.logout-link:hover {
    background-color: rgba(255, 107, 107, 0.2); /* خلفية أغمق عند التحويم */
    color: #ff5252 !important; /* لون أحمر أغمق عند التحويم */
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 107, 107, 0.2);
}

/* إضافة أيقونة لزر تسجيل الخروج (اختياري) */
.logout-link::before {
    content: "⟲"; /* رمز تسجيل الخروج */
    margin-right: 8px;
    font-size: 16px;
}

/* تعديل الشريط الجانبي ليدعم وضع زر تسجيل الخروج في الأسفل */
.sidebar {
    display: flex; /* This will be overridden by display: none !important above */
    flex-direction: column;
    height: 100vh;
}

.sidebar-nav {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    list-style: none;
    padding: 0 15px;
    margin: 0;
}
/* Add basic hidden class if not already defined in external CSS */
        .hidden {
            display: none !important;
        }

        .delete-btn {
            background-color: #dc3545; /* Red color for delete action */
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
            margin-top: 15px; /* Spacing from other elements */
        }

        .delete-btn:hover {
            background-color: #c82333;
        }

        /* Styles for delete button inside lecture list item */
        .lecture-item .delete-btn {
            margin-top: 0; /* No extra top margin within lecture item */
            margin-left: 10px; /* Spacing from download button */
            padding: 5px 10px; /* Smaller size */
            font-size: 0.9em;
        }
        
        .lecture-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        
        .lecture-item span {
            flex-grow: 1;
            font-weight: bold;
        }

        /* General form input/button styles */
        /* These rules are kept for general styling but apply to forms that are now removed in HTML */
        #addCourseForm input,
        #addLectureForm input,
        #newResearchForm input,
        #editResearchForm input,
        #addCourseForm button,
        #addLectureForm button,
        #newResearchForm button,
        #editResearchForm button {
            width: calc(100% - 20px); /* Adjust width to account for padding */
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box; /* Include padding and border in the element's total width and height */
        }
        
        #addCourseForm button,
        #addLectureForm button,
        #newResearchForm button,
        #editResearchForm button {
            width: auto; /* Reset width for buttons */
            padding: 10px 20px;
            margin-right: 10px;
        }

        /* Modal Styles */
        .modal-overlay {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 1000; /* Sit on top */
            left: 0;
            top: 0;
            width: 100%; /* Full width */
            height: 100%; /* Full height */
            overflow: auto; /* Enable scroll if needed */
            overflow: hidden; /* Hide overflow on the overlay to prevent scrolling while dragging */
            background-color: rgba(0,0,0,0.7); /* Black w/ opacity */
            /* Removed 'display: flex;' from here so 'display: none;' takes effect initially */
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .modal-content {
            background-color: #fefefe;
            margin: auto; /* Removed margin: auto; as we'll handle positioning with top/left */
            padding: 20px;
            border-radius: 8px;
            position: relative;
            position: fixed; /* Changed to fixed for dragging relative to viewport */
            max-width: 90%;
            max-height: 90%;
            width: 700px;/* Fixed width for the modal */
            height: 500px;/* Fixed height for the modal */
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow: hidden; /* Hide overflow to prevent image from breaking out */
            cursor: grab; /* Indicates that the element is draggable */
            /* Initial centering properties */
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1001; /* Ensure it's above the overlay */
        }

        /* Responsive adjustments for modal on smaller screens */
        @media (max-width: 768px) {
            .modal-content {
                width: 90vw; /* Use viewport width for responsiveness */
                height: 80vh; /* Use viewport height for responsiveness */
                padding: 15px;
            }
            .modal-content img {
                max-height: calc(100% - 50px); /* Adjust for smaller button/padding */
            }
        }

        .modal-content img {
            max-width: 100%;
            max-height: 80vh; /* Max height relative to viewport height */
            max-height: calc(100% - 60px); /* Max height relative to modal content, accounting for button */
            object-fit: contain; /* Ensure the image fits within the container without cropping */
            display: block;
            margin-bottom: 15px; /* Spacing for the close button */
        }

        .close-button {
            color: #aaa;
            position: absolute;
            top: 10px;
            right: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close-button:hover,
        .close-button:focus {
            color: #333;
            text-decoration: none;
        }
