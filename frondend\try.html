<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messages</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4A90E2; /* لون أزرق أكثر حيوية */
            --secondary-color: #888; /* لون رمادي ثانوي */
            --background-color: #f0f2f5; /* خلفية أفتح */
            --card-background: #ffffff; /* خلفية البطاقة (النافذة المنبثقة والحاوية) */
            --text-color: #333; /* لون النص الأساسي */
            --border-color: #e0e0e0; /* لون الحدود */
            --hover-background: #f5f5f5; /* لون الخلفية عند التحويم */
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* ظل أكثر نعومة */
        }

        body {
            font-family: 'Cairo', sans-serif; /* خط القاهرة */
            background: var(--background-color);
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--text-color);
            direction: ltr; /* اتجاه النص من اليسار إلى اليمين */
        }

        .container {
            max-width: 960px;
            margin: 40px auto;
            background: var(--card-background);
            padding: 30px;
            border-radius: 12px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        h1 {
            text-align: center;
            color: var(--text-color);
            margin-bottom: 25px;
            font-weight: 700;
        }

        /* --- Buttons --- */
        /* --- الأزرار --- */
        .buttons-container {
            text-align: center;
            margin-bottom: 30px;
            display: flex;
            justify-content: center;
            gap: 15px; /* المسافة بين الأزرار */
            flex-wrap: wrap; /* السماح للأزرار بالالتفاف على الشاشات الصغيرة */
        }

        .btn {
            padding: 10px 22px;
            border: none;
            border-radius: 8px; /* زوايا دائرية أكثر قليلاً */
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            color: #fff; /* لون النص الافتراضي للأزرار */
        }

        /* النمط الافتراضي لجميع الأزرار (رمادي افتراضيًا) */
        .btn-default {
            background: var(--secondary-color);
        }

        .btn-default:hover {
            background: #6a6a6a; /* رمادي أغمق عند التحويم */
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        /* نمط الزر النشط (أزرق عندما يكون نشطًا) */
        .btn-active {
            background: var(--primary-color);
        }

        .btn-active:hover {
            background: #3a7bd5; /* درجة أغمق من اللون الأساسي عند التحويم */
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        /* نمط زر الحذف */
        .btn-delete {
            background: #dc3545; /* لون أحمر للحذف */
        }

        .btn-delete:hover {
            background: #c82333; /* أحمر أغمق عند التحويم */
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }


         /* أنماط خاصة بأزرار نموذج النافذة المنبثقة (هذه لا تتبع منطق التنقل الرئيسي) */
        .modal-content .btn {
            width: auto; /* أزرار داخل النافذة المنبثقة لا يجب أن تكون بعرض كامل */
            margin-top: -5px; /* تم تعديل هذا لرفع الأزرار أكثر */
            padding: 10px 25px;
        }

        .modal-content .btn-secondary { /* The "Cancel" button in the modal */
            /* زر "إلغاء" في النافذة المنبثقة */
            margin-right: 10px; /* المسافة بين الأزرار */
            background: var(--secondary-color);
        }
        .modal-content .btn-secondary:hover {
            background: #6a6a6a;
        }


        /* --- أنماط الجدول --- */
        table {
            width: 100%;
            border-collapse: separate; /* للزوايا الدائرية على الخلايا */
            border-spacing: 0;
            margin-bottom: 40px;
            background-color: var(--card-background);
            border-radius: 10px;
            overflow: hidden; /* يضمن ظهور الزوايا الدائرية */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        th, td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: #f9f9f9;
            color: var(--text-color);
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        tbody tr:last-child td {
            border-bottom: none; /* لا توجد حدود للصف الأخير */
        }

        tr:hover {
            background: var(--hover-background);
            transition: background-color 0.2s ease;
        }

        /* Make message rows clickable */
        /* جعل صفوف الرسائل قابلة للنقر */
        .message-row {
            cursor: pointer;
        }


        /* جديد: أنماط الرسائل غير المقروءة */
        .message-row.unread {
            font-weight: bold; /* جعل الرسائل غير المقروءة غامقة */
        }
 
        /* جديد: أنماط الرسائل المقروءة (افتراضي، ولكن يمكن أن تكون صريحة) */
        .message-row.read {
            font-weight: normal; /* الرسائل المقروءة عادية */
        }



        /* نمط مربع الاختيار */
        input[type="checkbox"] {
            transform: scale(1.2); /* جعل مربعات الاختيار أكبر قليلاً */
            margin: 0;
            cursor: pointer;
        }

   
        /* --- النوافذ المنبثقة (نماذج منبثقة) --- */
        .modal, .message-box-overlay, .confirm-box-overlay, .view-message-overlay {
            display: none; /* مخفي افتراضيًا */
            position: fixed;
            z-index: 100; /* مؤشر z أعلى */
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.4); /* تراكب خلفية شبه شفاف */
            display: flex; /* استخدام فليكس بوكس للتوسيط */
            align-items: center;
            justify-content: center;
            opacity: 0; /* لانتقالات سلسة */
            visibility: hidden; /* لانتقالات سلسة */
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .modal.show, .message-box-overlay.show, .confirm-box-overlay.show, .view-message-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* أحجام محتوى النافذة المنبثقة المعدلة للقابلية للنقل */
        .modal-content, .view-message-content {
            background: var(--card-background);
            padding: 30px;
            border-radius: 12px;
            box-shadow: var(--shadow);
            text-align: center;
            width: 90%; /* عرض متجاوب */
            height: auto; /* تم التغيير: الارتفاع يتكيف مع المحتوى */
            max-height: 90vh; /* جديد: أقصى ارتفاع 90% من ارتفاع منفذ العرض */
            min-height: 350px; /* جديد: الحد الأدنى للارتفاع للاستخدام */
            max-width: 600px; /* الاحتفاظ بهذا للنافذة الرئيسية للرسائل ونافذة عرض الرسائل */
            box-sizing: border-box; /* يضمن أن المساحة المتروكة لا تزيد على العرض الكلي */
            display: flex; /* استخدام فليكس بوكس للتخطيط الداخلي */
            flex-direction: column; /* تكديس المحتوى عموديًا */
            justify-content: space-between; /* دفع الأزرار إلى الأسفل */
            cursor: grab; /* يشير إلى أنه قابل للسحب */
            position: fixed; /* تعيين الموضع صراحةً إلى ثابت للسحب */
            /* Removed transform and transition properties as they will be managed by JS for drag */
            /* تمت إزالة خصائص التحويل والانتقال حيث سيتم إدارتها بواسطة JavaScript للسحب */
        }

        /* Smaller sizes for message box and confirm box */
        /* أحجام أصغر لمربع الرسالة ومربع التأكيد */
        .message-box-content, .confirm-box-content {
            background: var(--card-background);
            padding: 30px;
            border-radius: 12px;
            box-shadow: var(--shadow);
            text-align: center;
            width: 90%; /* عرض متجاوب */
            max-width: 400px; /* أقصى عرض أصغر لهذه النوافذ المنبثقة المحددة */
            height: auto; /* تم التغيير: الارتفاع يتكيف مع المحتوى */
            max-height: 80vh; /* جديد: أقصى ارتفاع 80% من ارتفاع منفذ العرض */
            min-height: 180px; /* جديد: الحد الأدنى للارتفاع للنوافذ المنبثقة الأصغر */
            box-sizing: border-box; /* يضمن أن المساحة المتروكة لا تزيد على العرض الكلي */
            display: flex; /* استخدام فليكس بوكس للتخطيط الداخلي */
            flex-direction: column; /* تكديس المحتوى عموديًا */
            justify-content: space-between; /* دفع الأزرار إلى الأسفل */
            position: fixed; /* تعيين الموضع صراحةً إلى ثابت للسحب */
            /* Removed transform and transition properties as they will be managed by JS for drag */
            /* تمت إزالة خصائص التحويل والانتقال حيث سيتم إدارتها بواسطة JavaScript للسحب */
        }


        /* يتم التعامل مع هذه الآن بواسطة JavaScript للتوسيط الأولي والسحب */
        .modal.show .modal-content,
        .message-box-overlay.show .message-box-content,
        .confirm-box-overlay.show .confirm-box-content,
        .view-message-overlay.show .view-message-content {
            opacity: 1;
 
            /* تمت إزالة التحويل ويتم التعامل معه بواسطة JavaScript لتحديد الموضع */
        }

        .close {
            position: absolute;
            top: 10px;
            right: 10px;
            color: var(--secondary-color);
            font-size: 28px;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .close:hover {
            color: #333;
        }

        h2 {
            margin-top: 10px;
            margin-bottom: 20px;
            text-align: center;
            color: var(--text-color);
            font-weight: 600;
        }

        label {
            display: block;
            margin: 10px 0 5px;
            font-weight: 600;
            color: var(--text-color);
        }


        /* جديد: محاذاة عناصر النموذج في نافذة الرسالة الجديدة إلى اليسار */
        #newMessageForm {
            text-align: left; /* محاذاة محتوى النموذج إلى اليسار */
            margin-top: -15px; /* تم تعديل هذا لرفع محتوى النموذج */
        }
        /* Ensure labels and inputs within the form are explicitly left-aligned if needed */
        /* التأكد من محاذاة التسميات والمدخلات داخل النموذج صراحةً إلى اليسار إذا لزم الأمر */
        #newMessageForm label,
        #newMessageForm input,
        #newMessageForm textarea {
            text-align: left;
        }


        input[type="text"],
        input[type="email"],
        textarea {
            width: calc(100% - 20px); /* حساب المساحة المتروكة */
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            font-size: 0.95rem;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        input[type="text"]:focus,
        input[type="email"]:focus,
        textarea:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
            outline: none;
        }

        textarea {
            resize: vertical; /* السماح بتغيير الحجم عموديًا */
            min-height: 80px;
            max-height: 150px;
        }

        /* Style for read-only 'From' field */
        /* نمط حقل "من" للقراءة فقط */
        input[readonly] {
            background-color: #f0f0f0;
            cursor: not-allowed;
        }


        /* نمط إدخال الملف المخصص - هذه الأنماط لم تعد تستخدم مباشرة ولكن تم الاحتفاظ بها للسياق إذا لزم الأمر */
        .file-input-wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 5px;
            background-color: #fff;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .file-input-wrapper:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
            outline: none;
        }

        .custom-file-button {
            background-color: var(--primary-color);
            color: white;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            white-space: nowrap;
        }

        .custom-file-button:hover {
            background-color: #3a7bd5;
        }

        .file-name-display {
            flex-grow: 1;
            color: var(--secondary-color);
            font-size: 0.95rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }


        /* أنماط مربع الرسالة ومربع التأكيد */
        .message-box-content, .confirm-box-content {
            direction: ltr; /* تعيين الاتجاه لمحتوى مربع الرسالة */
        }
        .message-box-content h3, .confirm-box-content h3, .view-message-content h3 {
            margin-top: 0;
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 20px;
        }
        .confirm-box-content h3 {
            color: #dc3545; /* أحمر لعنوان التأكيد */
        }


        /* تم التعديل: حجم خط أصغر للرسائل التوضيحية */
        .message-box-content p, .confirm-box-content p {
            margin-bottom: 30px;
            font-size: 1.1rem; /* حجم خط أصغر */
            color: var(--text-color);
        }

        .message-box-content .btn, .confirm-box-content .btn, .view-message-content .btn {
            background: var(--primary-color);
            color: white;
            padding: 10px 25px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin: 0 5px; /* المسافة لأزرار التأكيد */
        }

        .message-box-content .btn:hover, .view-message-content .btn:hover {
            background: #3a7bd5;
        }
        .confirm-box-content .btn-confirm {
            background: #dc3545; /* أحمر لتأكيد الحذف */
        }
        .confirm-box-content .btn-confirm:hover {
            background: #c82333;
        }
        .confirm-box-content .btn-cancel {
            background: var(--secondary-color);
        }
        .confirm-box-content .btn-cancel:hover {
            background: #6a6a6a;
        }


        /* أنماط نافذة عرض الرسالة المنبثقة */
        .view-message-content {
            text-align: left; /* محاذاة النص إلى اليسار لسهولة القراءة */
            direction: ltr; /* اتجاه النص من اليسار إلى اليمين */
            border: 1px solid var(--border-color); /* إضافة حدود خفيفة */
            overflow-y: auto; /* السماح بالتمرير إذا تجاوز المحتوى */
            padding-top: 15px; /* تم تعديل هذا لرفع محتوى نموذج عرض تفاصيل الرسالة */
        }

        .view-message-content h3 {
            color: var(--primary-color); /* إبراز العنوان */
            border-bottom: 2px solid var(--border-color); /* فاصل تحت العنوان */
            padding-bottom: 10px; /* تم تقليل المسافة هنا */
            margin-top: -10px; /* تم تعديل هذا لرفع محتوى العنوان */
            margin-bottom: 15px; /* تم تقليل المسافة هنا */
            text-align: center; /* توسيط العنوان */
        }
        

        /* يجب أن يكون كل سطر تفاصيل عبارة عن كتلة (سطر جديد) */
        .view-message-content .message-detail {
            margin-bottom: 5px; /* تم تقليل المسافة بين الحقول هنا */
            display: flex; /* استخدام فليكس بوكس لمحاذاة التسمية والقيمة */
            align-items: flex-start; /* محاذاة العناصر إلى البداية للمحتوى متعدد الأسطر */
        }

 
        /* أنماط لـ strong (التسميات) و span (القيم) داخل تفاصيل الرسالة */
        .view-message-content .message-detail strong {
            flex-shrink: 0; /* منع التسمية من الانكماش */
            width: 90px; /* عرض متزايد قليلاً لمحاذاة التسمية المتناسقة */
            margin-right: 15px; /* مسافة متزايدة بين التسمية والقيمة */
            text-align: right; /* محاذاة نص التسمية إلى اليمين داخل مربعها */
            color: var(--text-color); /* التأكد من تناسق لون التسمية */
            font-weight: 700; /* جعل التسميات أكثر سمكًا */
        }

        .view-message-content .message-detail span {
            flex-grow: 1; /* السماح لـ span بأخذ المساحة المتبقية */
            text-align: left; /* التأكد من محاذاة نص القيمة إلى اليسار */
            direction: ltr; /* اتجاه النص من اليسار إلى اليمين للمحتوى */
            unicode-bidi: embed; /* يضمن تطبيق الاتجاه بشكل صحيح */
            word-wrap: break-word; /* التأكد من التفاف النص الطويل */
            color: var(--text-color); /* لون نص متناسق */
        }

   
        /* لأقسام المحتوى والمرفقات، نريد التسمية على سطرها الخاص */
        .view-message-content .content-detail,
        .view-message-content .attachment-detail {
            display: block; /* يضمن أن هذه الأقسام تأخذ عرضًا كاملاً وتتكدس */
            margin-top: 10px; /* تم تقليل المسافة هنا */
            margin-bottom: 5px; /* تم تقليل المسافة هنا */
            border-top: 1px dashed var(--border-color); /* إضافة خط فاصل متقطع خفيف */
            padding-top: 10px; /* تم تقليل المسافة هنا */
        }
        .view-message-content .attachment-detail {
            margin-bottom: 10px; /* تم تقليل المسافة هنا */
        }

        .view-message-content .content-detail strong,
        .view-message-content .attachment-detail strong {
            display: block; /* جعل علامة strong عنصر كتلة */
            width: auto; /* إزالة العرض الثابت */
            text-align: left; /* محاذاة التسمية إلى اليسار */
            margin-right: 0; /* إزالة الهامش الأيمن */
            margin-bottom: 5px; /* تم تقليل المسافة هنا */
            font-size: 1.1rem; /* حجم خط أكبر قليلاً لتسميات المحتوى/المرفقات */
            color: var(--primary-color); /* جعل عناوين هذه الأقسام مميزة */
        }

        .view-message-content pre {
            background-color: #f9f9f9;
            border: 1px solid var(--border-color);
            padding: 15px;
            border-radius: 8px;
            white-space: pre-wrap; /* الحفاظ على المسافات البيضاء ولف النص */
            word-wrap: break-word; /* كسر الكلمات الطويلة */
            max-height: 250px; /* ارتفاع أقصى متزايد قليلاً لمحتوى الرسالة */
            overflow-y: auto; /* التمرير إذا تجاوز المحتوى */
            direction: ltr; /* اتجاه النص من اليسار إلى اليمين لمحتوى الرسالة */
            font-size: 1rem;
            line-height: 1.7; /* تحسين قابلية قراءة محتوى الرسالة */
            color: #555; /* لون نص أكثر نعومة قليلاً للمحتوى */
        }

        .view-message-content .attachment-link {
            display: block; /* جعله عنصر كتلة ليأخذ عرضًا كاملاً */
            margin-top: 15px;
            color: var(--primary-color);
            text-decoration: none;
            text-align: left; /* محاذاة الرابط إلى اليسار */
            font-weight: 600; /* جعل رابط المرفق أكثر سمكًا */
        }
        .view-message-content .attachment-link:hover {
            text-decoration: underline;
        }

   
        /* --- الأزرار في نافذة عرض الرسالة المنبثقة --- */
        .view-message-content .btn {
            padding: 8px 18px; /* مساحة داخلية أصغر */
            font-size: 0.9rem; /* حجم خط أصغر */
            margin: 0 5px; /* الحفاظ على الهامش الأفقي */
            display: inline-block; /* جعلها تجلس بجانب بعضها البعض */
            width: auto; /* التأكد من تعديل العرض تلقائيًا */
        }

      
        /* --- التصميم المتجاوب --- */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 20px;
            }

            h1 {
                font-size: 1.8rem;
            }

            .btn {
                font-size: 0.9rem;
                padding: 9px 18px;
            }

            th, td {
                padding: 10px;
                font-size: 0.9rem;
            }

            
            /* تعديل المساحة المتروكة لمحتوى النافذة المنبثقة (رسالة جديدة/عرض رسالة) */
            .modal-content, .view-message-content {
                padding: 15px; /* تعديل المساحة المتروكة للشاشات الأصغر */
            }

           
            /* تعديل خاص للمساحة المتروكة/العرض لمربع الرسالة ومربع التأكيد لشاشات 768 بكسل */
            .message-box-content, .confirm-box-content {
                max-width: 350px; /* أقصى عرض أصغر لهذه النوافذ المنبثقة المحددة */
                padding: 20px; /* تعديل المساحة المتروكة للشاشات الأصغر */
            }

            h2 {
                font-size: 1.5rem;
            }

            label {
                font-size: 0.9rem;
            }

            input[type="text"], input[type="email"], textarea {
                padding: 8px;
                font-size: 0.9rem;
                margin-bottom: 8px;
            }

            textarea {
                min-height: 70px;
                max-height: 120px;
            }

            .modal-content .btn {
                padding: 8px 20px;
                font-size: 0.9rem;
            }

            .modal-content .btn-secondary {
                margin-right: 10px;
            }

            .file-input-wrapper {
                padding: 4px;
            }

            .custom-file-button {
                padding: 7px 12px;
                font-size: 0.9rem;
            }

            .file-name-display {
                font-size: 0.9rem;
            }

            
            /* تعديل حجم الخط لفقرة مربع الرسالة في الشاشات الأصغر */
            .message-box-content p, .confirm-box-content p {
                font-size: 1.1rem; /* تم تقليله أكثر لشاشات 768 بكسل */
            }

            
            /* تعديلات على محتوى عرض الرسالة في الشاشات الأصغر */
            .view-message-content .message-detail strong {
                width: 70px; /* تقليل عرض التسمية أكثر على الشاشات الأصغر */
                margin-right: 10px;
            }
            .view-message-content .message-detail span {
                max-width: calc(100% - 80px); /* تعديل أقصى عرض لـ span */
            }
            .view-message-content pre {
                max-height: 150px; /* تقليل ارتفاع pre على الشاشات الأصغر */
            }
        }

        @media (max-width: 480px) {
            .buttons-container {
                flex-direction: column;
                gap: 10px;
            }

            .btn {
                width: 100%; /* أزرار بعرض كامل على الشاشات الصغيرة جدًا */
            }

            /* تعديل المساحة المتروكة لمحتوى النافذة المنبثقة (رسالة جديدة/عرض رسالة) */
            .modal-content, .view-message-content {
                padding: 10px; /* تعديل المساحة المتروكة للشاشات الصغيرة جدًا */
            }

            /* تعديل خاص للمساحة المتروكة/العرض لمربع الرسالة ومربع التأكيد لشاشات 480 بكسل */
            .message-box-content, .confirm-box-content {
                max-width: 300px; /* أقصى عرض أصغر لهذه النوافذ المنبثقة المحددة */
                padding: 15px; /* تعديل المساحة المتروكة للشاشات الصغيرة جدًا */
            }

            h2 {
                font-size: 1.3rem;
                margin-bottom: 15px;
            }

            label {
                font-size: 0.85rem;
                margin: 8px 0 4px;
            }

            input[type="text"], input[type="email"], textarea {
                padding: 7px;
                font-size: 0.85rem;
                margin-bottom: 7px;
            }

            textarea {
                min-height: 60px;
                max-height: 100px;
            }

            .modal-content .btn {
                width: 100%;
                margin-top: 8px;
                padding: 7px 15px;
                font-size: 0.85rem;
            }

            .modal-content .btn-secondary {
                margin-right: 0;
            }

            .file-input-wrapper {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            .custom-file-button {
                width: 100%;
                text-align: center;
            }
            .file-name-display {
                width: 100%;
            }

            /* تعديل حجم الخط لفقرة مربع الرسالة في الشاشات الصغيرة جدًا */
            .message-box-content p, .confirm-box-content p {
                font-size: 1.1rem; /* تم تقليله أكثر لشاشات 480 بكسل */
            }

            /* تعديلات إضافية على محتوى عرض الرسالة في الشاشات الصغيرة جدًا */
            .view-message-content .message-detail strong {
                width: 60px; /* عرض تسمية أصغر حتى */
                margin-right: 8px;
            }
            .view-message-content .message-detail span {
                max-width: calc(100% - 70px); /* تعديل أقصى عرض لـ span */
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Messages</h1>

        <div class="buttons-container">
            <button class="btn" id="newMessageBtn" onclick="setActiveButton(this); openModal();">New Message</button>
            <button class="btn" id="inboxBtn" onclick="setActiveButton(this); toggleView('inbox');">Inbox</button>
            <button class="btn" id="sentBtn" onclick="setActiveButton(this); toggleView('sent');">Sent Messages</button>
            <button class="btn btn-delete" id="deleteSelectedBtn" style="display: block;" onclick="confirmDeleteSelected()">Delete Selected</button>
        </div>

        <table id="messagesTable">
            <thead>
                <tr>
                    <th><input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll(this)"></th>
                    <th>#</th>
                    <th>Name/To</th>
                    <th>Email</th>
                    <th>Subject</th>
                    <th>Date Sent</th>
                </tr>
            </thead>
            <tbody>
            </tbody>
        </table>
    </div>

    <!-- New Message Modal -->
    <!-- نافذة الرسالة الجديدة المنبثقة -->
    <div id="messageModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>Send New Message</h2>
            <form id="newMessageForm" onsubmit="sendMessage(event)">
                <label for="fromEmail">From:</label>
                <input type="email" id="fromEmail" value="<EMAIL>" readonly />

                <label for="toEmail">To:</label>
                <input type="email" id="toEmail" required />

                <label for="subject">Subject:</label>
                <input type="text" id="subject" required />

                <label for="messageContent">Message Content:</label>
                <textarea id="messageContent" rows="4" required></textarea>
                
                <button type="submit" class="btn btn-active">Send</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
            </form>
        </div>
    </div>


    <!-- نافذة عرض الرسالة المنبثقة -->
    <div id="viewMessageModal" class="view-message-overlay">
        <div class="view-message-content">
            <span class="close" onclick="closeViewMessageModal()">&times;</span>
            <h3>Message Details</h3>
            <div class="message-detail">
                <strong>From:</strong> <span id="viewFromName"></span>
            </div>
            <div class="message-detail">
                <strong>Email:</strong> <span id="viewFromEmail"></span>
            </div>
            <div class="message-detail">
                <strong>Subject:</strong> <span id="viewSubject"></span>
            </div>
            <div class="message-detail">
                <strong>Date:</strong> <span id="viewDate"></span>
            </div>
            <div class="message-detail content-detail">
                <strong>Content:</strong>
                <pre id="viewContent"></pre>
            </div>
            <div style="text-align: center; margin-top: 20px;"> <!-- NEW: Container for buttons -->
                <!-- جديد: حاوية للأزرار -->
                <button class="btn" onclick="replyToMessage(currentViewingMessage)">Reply</button>
                <button class="btn" onclick="closeViewMessageModal()">Close</button>
            </div>
        </div>
    </div>

    <!-- مربع الرسائل المخصص (للتنبيهات) -->
    <div id="customMessageBox" class="message-box-overlay">
        <div class="message-box-content">
            <h3 id="messageBoxTitle"></h3>
            <p id="messageBoxContent"></p>
            <button class="btn" onclick="closeMessageBox()">OK</button>
        </div>
    </div>


    <!-- مربع تأكيد الحذف -->
    <div id="confirmDeleteBox" class="confirm-box-overlay">
        <div class="confirm-box-content">
            <h3>Confirm Deletion</h3>
            <p>Are you sure you want to delete the selected message(s)? This action cannot be undone.</p>
            <button class="btn btn-confirm" onclick="deleteSelectedMessages()">Delete</button>
            <button type="button" class="btn btn-cancel" onclick="closeConfirmDeleteBox()">Cancel</button>
        </div>
    </div>

    <script>
        /**
         * هذا الكود JavaScript يدير واجهة تطبيق رسائل بسيطة.
         * يتضمن وظائف لعرض الرسائل (البريد الوارد والمرسلة)،
         * إرسال رسائل جديدة، عرض تفاصيل الرسالة، حذف الرسائل،
         * والرد على الرسائل. كما يتميز بنافذة منبثقة قابلة للسحب
         * لمربعات الحوار.
         */

        // Sample data for inbox messages
        // بيانات رسائل البريد الوارد (كمثال)
        let inboxMessages = [
            // الرسائل الواردة
            {id: 1, from: "<EMAIL>", to: "<EMAIL>", subject: "Inquiry", content: "Dear user, I am writing to inquire about the registration process and the available courses for the upcoming semester. Could you please provide more details on how to apply and any prerequisites?\n\nThank you for your time and assistance.\n\nSincerely,\nAhmed", date: "2025-05-30", attachment: null, isRead: false}, // isRead: حالة قراءة الرسالة (لم تقرأ بعد)
            {id: 2, from: "<EMAIL>", to: "<EMAIL>", subject: "Information Request", content: "Hello,\nI would like to request additional information regarding the admission requirements for the Master's program in Computer Science. Specifically, I'm interested in the GPA requirements and the procedure for submitting recommendation letters.\n\nAny guidance would be greatly appreciated.\n\nBest regards,\nSara", date: "2025-05-29", attachment: null, isRead: true}, // isRead: حالة قراءة الرسالة (تمت قراءتها)
            {id: 3, from: "<EMAIL>", to: "<EMAIL>", subject: "Application Deadlines", content: "Hi team,\nCould you please clarify the application deadlines for international students? I am particularly concerned about the final submission date for all documents and the notification date for admissions decisions.\n\nThanks,\nKhalid", date: "2025-05-28", attachment: null, isRead: false}, // isRead: حالة قراءة الرسالة (لم تقرأ بعد)
            {id: 4, from: "<EMAIL>", to: "<EMAIL>", subject: "Improvement Suggestion", content: "To whom it may concern,\nI wanted to suggest an improvement for your website's user interface. It would be beneficial to have a clearer navigation menu and perhaps a search bar for easier access to information.\n\nThank you for considering my feedback.\n\nRegards,\nLayla", date: "2025-05-27", attachment: null, isRead: true}, // isRead: حالة قراءة الرسالة (تمت قراءتها)
        ];

        // Sample data for sent messages
        // بيانات الرسائل المرسلة (كمثال)
        let sentMessages = [
            // الرسائل المرسلة (تعتبر مقروءة افتراضيا)
            {id: 1, from: "<EMAIL>", to: "<EMAIL>", subject: "Reply to your inquiry", content: "Dear Ahmed,\nThank you for your inquiry. We have received your message and will get back to you with the registration details and course information within 2 business days.\n\nSincerely,\nSupport Team", date: "2025-05-30", attachment: null, isRead: true}, // isRead: حالة قراءة الرسالة (تمت قراءتها)
            {id: 2, from: "<EMAIL>", to: "<EMAIL>", subject: "Details sent", content: "Dear Sara,\nThe required details regarding admission requirements for the Master's program in Computer Science, including GPA and recommendation letter procedures, have been sent to your email address. Please check your inbox.\n\nBest regards,\nAdmissions Office", date: "2025-05-29", attachment: null, isRead: true}, // isRead: حالة قراءة الرسالة (تمت قراءتها)
        ];

   
        // متغير لتحديد نوع العرض الحالي (الافتراضي: 'inbox')
        let currentView = 'inbox'; // يمكن أن يكون 'inbox', 'sent', أو 'new' (لزر الرسالة الجديدة)
        let currentViewingMessage = null; // متغير جديد لتخزين الرسالة التي يتم عرضها حاليًا

  
        // الحصول على جميع الأزرار ذات الصلة
        const newMessageBtn = document.getElementById('newMessageBtn');
        const inboxBtn = document.getElementById('inboxBtn');
        const sentBtn = document.getElementById('sentBtn');
        const deleteSelectedBtn = document.getElementById('deleteSelectedBtn'); // زر الحذف الجديد
        const allMainButtons = [newMessageBtn, inboxBtn, sentBtn]; // قائمة بجميع الأزرار الرئيسية (يتم التعامل مع الحذف بشكل منفصل)

  
        // تعيين الزر النشط الأولي
        window.onload = function() {
            setActiveButton(inboxBtn); // تعيين البريد الوارد كزر نشط افتراضيًا
            renderMessages(); // عرض رسائل البريد الوارد عند التحميل
        };

       
        // دالة لتعيين الزر النشط وتغيير الألوان
        function setActiveButton(clickedButton) {
            allMainButtons.forEach(button => {
                button.classList.remove('btn-active');
                button.classList.add('btn-default');
            });
            clickedButton.classList.add('btn-active');
            clickedButton.classList.remove('btn-default');
        }

        /**
         * Shows a custom message box instead of native alert().
         * تعرض مربع رسائل مخصص بدلاً من دالة alert() الأصلية.
         * @param {string} title - The title of the message box. عنوان مربع الرسائل.
         * @param {string} message - The content of the message. محتوى الرسالة.
         */
        function showMessageBox(title, message) {
            const messageBox = document.getElementById("customMessageBox");
            document.getElementById("messageBoxTitle").textContent = title;
            document.getElementById("messageBoxContent").textContent = message;
            messageBox.classList.add("show");
        }

        /* تغلق مربع الرسائل المخصص. */
        function closeMessageBox() {
            const messageBox = document.getElementById("customMessageBox");
            messageBox.classList.remove("show");
        }

        /* تعرض مربع تأكيد الحذف */
        function confirmDeleteSelected() {
            const selectedCheckboxes = document.querySelectorAll('#messagesTable tbody input[type="checkbox"]:checked');
            if (selectedCheckboxes.length === 0) {
                showMessageBox("No Selection", "Please select at least one message to delete."); // لا يوجد اختيار، الرجاء اختيار رسالة واحدة على الأقل للحذف
                return;
            }
            document.getElementById("confirmDeleteBox").classList.add("show");
        }

        /* تغلق مربع تأكيد الحذف*/
        function closeConfirmDeleteBox() {
            document.getElementById("confirmDeleteBox").classList.remove("show");
        }

        /* تبديل عرض الرسائل بين البريد الوارد والمرسل    */
        function toggleView(viewType) {
            currentView = viewType;
            renderMessages();
           
        }

        /* تعرض الرسائل في الجدول بناءً على العرض الحالي.*/
        function renderMessages() {
            const messagesTableBody = document.querySelector("#messagesTable tbody");
            messagesTableBody.innerHTML = ""; // مسح الرسائل الموجودة
            const messagesToDisplay = currentView === 'inbox' ? inboxMessages : sentMessages;

            if (messagesToDisplay.length === 0) {
                messagesTableBody.innerHTML = `<tr><td colspan="6">No messages to display.</td></tr>`; // لا توجد رسائل لعرضها
                document.getElementById('selectAllCheckbox').checked = false; // إلغاء تحديد الكل
                
                return;
            }

            messagesToDisplay.forEach((message, index) => {
                const row = messagesTableBody.insertRow();
                row.classList.add('message-row');
                row.classList.add(message.isRead ? 'read' : 'unread'); // إضافة فئة مقروء/غير مقروء

                
                // مربع اختيار التحديد
                const checkboxCell = row.insertCell(0);
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.className = 'message-checkbox';
                checkbox.value = message.id; // استخدام معرف الرسالة كقيمة
                checkbox.onchange = updateDeleteButtonVisibility; // تحديث الزر عند تغيير مربع الاختيار
                checkboxCell.appendChild(checkbox);


                // رقم الرسالة
                const numCell = row.insertCell(1);
                numCell.textContent = index + 1; // عرض الفهرس القائم على 1

             
                // الاسم/إلى (المرسل للوارد، المستلم للمرسل)
                const nameCell = row.insertCell(2);
                nameCell.textContent = currentView === 'inbox' ? message.from.split('@')[0] : message.to.split('@')[0];

                // البريد الإلكتروني (المرسل للوارد، المستلم للمرسل)
                const emailCell = row.insertCell(3);
                emailCell.textContent = currentView === 'inbox' ? message.from : message.to;

                // الموضوع
                const subjectCell = row.insertCell(4);
                subjectCell.textContent = message.subject;

                 // تاريخ الإرسال
                const dateCell = row.insertCell(5);
                dateCell.textContent = message.date;

                  // جعل الصف قابلاً للنقر لعرض تفاصيل الرسالة، باستثناء خلية مربع الاختيار
                row.onclick = (event) => {
                
                    // منع فتح النافذة المنبثقة إذا تم النقر على مربع الاختيار أو خليته الأصل
                    if (event.target.type === 'checkbox' || event.target.closest('td') === checkboxCell) {
                        return;
                    }
                    openViewMessageModal(message);
                    if (currentView === 'inbox' && !message.isRead) {
                        message.isRead = true; // وضع علامة كمقروء عند العرض من البريد الوارد
                        row.classList.remove('unread');
                        row.classList.add('read');
                    }
                };
            });
           
            // الفحص الأولي لرؤية زر الحذف - تمت الإزالة لأن الزر مرئي دائمًا
            document.getElementById('selectAllCheckbox').checked = false; // إعادة تعيين مربع اختيار تحديد الكل
        }


        /*عامل مع مربع الاختيار "تحديد الكل".
         * @param {HTMLInputElement} selectAllCheckbox - The "Select All" checkbox element.
         * مربع الاختيار "تحديد الكل".
         */
        function toggleSelectAll(selectAllCheckbox) {
            const checkboxes = document.querySelectorAll('#messagesTable tbody input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        
        }

        /*
         تحدث رؤية زر "حذف المحدد".
          ملاحظة: هذه الدالة أصبحت الآن زائدة عن الحاجة حيث أن زر الحذف مرئي دائمًا.
         */
        function updateDeleteButtonVisibility() {
         
        }


        /*
         * تحذف الرسائل المحددة من العرض الحالي.
         * @param {number[]} selectedIds - An array of IDs of messages to delete.
         * مصفوفة من معرفات الرسائل المراد حذفها.
         */
        function deleteSelectedMessages() {
            const selectedCheckboxes = document.querySelectorAll('#messagesTable tbody input[type="checkbox"]:checked');
            const selectedIds = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));

            if (selectedIds.length === 0) {
                showMessageBox("No Selection", "Please select at least one message to delete."); // لا يوجد اختيار، الرجاء اختيار رسالة واحدة على الأقل للحذف
                return;
            }

            if (currentView === 'inbox') {
                inboxMessages = inboxMessages.filter(msg => !selectedIds.includes(msg.id));
            } else if (currentView === 'sent') {
                sentMessages = sentMessages.filter(msg => !selectedIds.includes(msg.id));
            }
            closeConfirmDeleteBox();
            renderMessages(); // إعادة عرض الجدول بعد الحذف
            showMessageBox("Success", "Selected messages have been deleted."); // تم حذف الرسائل المحددة
            document.getElementById('selectAllCheckbox').checked = false; // إلغاء تحديد الكل بعد الحذف
        }


        /* تفتح نافذة الرسالة الجديدة. */
        function openModal() {
            document.getElementById("messageModal").classList.add("show");
           
            // تعيين البريد الإلكتروني الافتراضي "من" إذا لزم الأمر، أو الحصول على بريد المستخدم ديناميكيًا
            document.getElementById("fromEmail").value = "<EMAIL>";
            document.getElementById("newMessageForm").reset(); // مسح النموذج
        }

        /*  غلق نافذة الرسالة الجديدة.*/
        function closeModal() {
            document.getElementById("messageModal").classList.remove("show");
        }

        /*
         * تتعامل مع إرسال رسالة جديدة.
         * @param {Event} event - The form submission event. حدث إرسال النموذج.
         */
        function sendMessage(event) {
            event.preventDefault(); // منع الإرسال الافتراضي للنموذج

            const fromEmail = document.getElementById("fromEmail").value;
            const toEmail = document.getElementById("toEmail").value;
            const subject = document.getElementById("subject").value;
            const content = document.getElementById("messageContent").value;
            const date = new Date().toISOString().split('T')[0]; // التاريخ الحالي

            const newMessage = {
                id: sentMessages.length > 0 ? Math.max(...sentMessages.map(m => m.id)) + 1 : 1, // إنشاء معرف بسيط
                from: fromEmail,
                to: toEmail,
                subject: subject,
                content: content,
                date: date,
                attachment: null, // لا يوجد معالجة للمرفقات في هذا الإصدار
                isRead: true // الرسائل المرسلة تعتبر مقروءة
            };

            sentMessages.push(newMessage);
            showMessageBox("Success", "Your message has been sent!"); // تم إرسال رسالتك!
            closeModal(); // إغلاق النافذة المنبثقة
            toggleView('sent'); // التبديل إلى عرض الرسائل المرسلة بعد الإرسال
        }

        /**
         * Opens the view message modal and populates it with message details.
         * تفتح نافذة عرض الرسالة وتملأها بتفاصيل الرسالة.
         * @param {Object} message - The message object to display. كائن الرسالة لعرضه.
         */
        function openViewMessageModal(message) {
            currentViewingMessage = message; // تخزين الرسالة التي يتم عرضها
            document.getElementById("viewFromName").textContent = currentView === 'inbox' ? message.from.split('@')[0] : message.to.split('@')[0];
            document.getElementById("viewFromEmail").textContent = currentView === 'inbox' ? message.from : message.to;
            document.getElementById("viewSubject").textContent = message.subject;
            document.getElementById("viewDate").textContent = message.date;
            document.getElementById("viewContent").textContent = message.content;
            

            document.getElementById("viewMessageModal").classList.add("show");
        }

        /* تغلق نافذة عرض الرسالة. */
        function closeViewMessageModal() {
            document.getElementById("viewMessageModal").classList.remove("show");
            currentViewingMessage = null; // مسح الرسالة المخزنة
        }

        /*
         * تفتح نافذة الرسالة الجديدة مملوءة مسبقًا للرد.
         * @param {Object} originalMessage - The message to reply to. الرسالة الأصلية للرد عليها.
         */
        function replyToMessage(originalMessage) {
            closeViewMessageModal(); // إغلاق نافذة عرض الرسالة
            openModal(); // فتح نافذة الرسالة الجديدة
            
            // Pre-fill the form fields for reply
            // تعبئة حقول النموذج مسبقًا للرد
            document.getElementById("toEmail").value = originalMessage.from; // الرد على المرسل
            document.getElementById("subject").value = "Re: " + originalMessage.subject; // إضافة "Re: " إلى الموضوع
        }

        /**

         * تجعل عنصر المودال المحدد قابلاً للسحب.
         * @param {HTMLElement} modalElement - The modal content element to make draggable.
         * عنصر محتوى النافذة المنبثقة لجعله قابلاً للسحب.
         */
        function makeMovable(modalElement) {
            let isDragging = false;
            let initialPointerX, initialPointerY; // إحداثيات المؤشر عند بدء السحب
            let initialElementX, initialElementY; // إحداثيات أعلى/يسار العنصر عند بدء السحب

            // Function to set the modal to its initial centered position
            // دالة لمركزة المودال في الموضع الأولي
            function centerModal() {
                // قم بالمركزة فقط إذا كان المودال مرئيًا حاليًا
                if (!modalElement.closest('.modal.show') && !modalElement.closest('.view-message-overlay.show')) {
                    return;
                }
                
                modalElement.style.position = 'fixed'; // تأكد من أن الموضع ثابت للتحديد الدقيق
                
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                const modalWidth = modalElement.offsetWidth;
                const modalHeight = modalElement.offsetHeight;

                let newLeft = (viewportWidth - modalWidth) / 2;
                let newTop = (viewportHeight - modalHeight) / 2;

                // منع المودال من الخروج عن الشاشة في البداية
                newLeft = Math.max(0, newLeft);
                newTop = Math.max(0, newTop);

                modalElement.style.left = newLeft + 'px';
                modalElement.style.top = newTop + 'px';
            }

            // راقب متى يتم إضافة الفئة 'show' لإعادة مركزة المودال
            const observer = new MutationObserver((mutationsList) => {
                for (let mutation of mutationsList) {
                    if (mutation.attributeName === 'class') {
                       
                        // تحقق مما إذا كان التراكب الأصل يحتوي على الفئة 'show'
                        const parentOverlay = modalElement.closest('.modal') || modalElement.closest('.view-message-overlay');
                        if (parentOverlay && parentOverlay.classList.contains('show')) {
                            // استخدم مهلة زمنية قصيرة للسماح للمودال بالرسم قبل المركزية
                            requestAnimationFrame(() => { // استخدم requestAnimationFrame للتأكد من تحديث التخطيط
                                centerModal();
                            });
                            break;
                        }
                    }
                }
            });
            observer.observe(modalElement.parentElement, { attributes: true, subtree: false }); // مراقبة التراكب الأصل

            // إعادة المركزية عند تغيير حجم النافذة
            window.addEventListener('resize', centerModal);

            modalElement.addEventListener("mousedown", dragStart);
            modalElement.addEventListener("touchstart", dragStart, { passive: false }); // { passive: false } للسماح بـ preventDefault

            document.addEventListener("mouseup", dragEnd);
            document.addEventListener("touchend", dragEnd);

            document.addEventListener("mousemove", drag);
            document.addEventListener("touchmove", drag, { passive: false }); // { passive: false } للسماح بـ preventDefault

            function dragStart(e) {
                // تجاهل السحب إذا تم النقر على زر الإغلاق
                if (e.target.classList.contains('close')) {
                    return;
                }

                isDragging = true;
                modalElement.style.cursor = "grabbing"; // تغيير المؤشر إلى يد السحب
                modalElement.style.transition = "none"; // تعطيل الانتقال أثناء السحب
                document.body.style.userSelect = 'none'; // منع تحديد النص في الجسم
                document.body.style.webkitUserSelect = 'none'; /* Safari */
                document.body.style.msUserSelect = 'none'; /* IE 10+ */

             
                // الحصول على إحداثيات المؤشر الأولية بناءً على نوع الحدث
                if (e.type === 'touchstart') {
                    initialPointerX = e.touches[0].clientX;
                    initialPointerY = e.touches[0].clientY;
                } else { // mousedown
                    initialPointerX = e.clientX;
                    initialPointerY = e.clientY;
                }

                // الحصول على الموضع الحالي للعنصر المودال
                initialElementX = modalElement.offsetLeft;
                initialElementY = modalElement.offsetTop;
            }

            function dragEnd(e) {
                isDragging = false;
                modalElement.style.cursor = "grab"; // إعادة المؤشر إلى الوضع العادي
                modalElement.style.transition = ""; // إعادة تمكين الانتقالات بعد السحب
                document.body.style.userSelect = ''; // إعادة تمكين تحديد النص
                document.body.style.webkitUserSelect = '';
                document.body.style.msUserSelect = '';
            }

            function drag(e) {
                if (!isDragging) return;

                e.preventDefault(); // منع سلوك المتصفح الافتراضي (مثل التمرير على اللمس)

              
                // الحصول على إحداثيات المؤشر الحالية بناءً على نوع الحدث
                let currentPointerX;
                let currentPointerY;

                if (e.type === 'touchmove') {
                    currentPointerX = e.touches[0].clientX;
                    currentPointerY = e.touches[0].clientY;
                } else { // mousemove
                    currentPointerX = e.clientX;
                    currentPointerY = e.clientY;
                }
                
                // حساب التغيير في الموضع
                let deltaX = currentPointerX - initialPointerX;
                let deltaY = currentPointerY - initialPointerY;

                // حساب الموضع الجديد
                let newX = initialElementX + deltaX;
                let newY = initialElementY + deltaY;

                // تثبيت الموضع ضمن حدود نافذة العرض
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                const modalWidth = modalElement.offsetWidth;
                const modalHeight = modalElement.offsetHeight;

                newX = Math.max(0, Math.min(newX, viewportWidth - modalWidth));
                newY = Math.max(0, Math.min(newY, viewportHeight - modalHeight));

                // تعيين الموضع الجديد للمودال
                modalElement.style.left = newX + 'px';
                modalElement.style.top = newY + 'px';
            }
        }

        // تطبيق قابلية التحريك على مودال الرسالة ومودال عرض الرسالة
        makeMovable(document.querySelector("#messageModal .modal-content"));
        makeMovable(document.querySelector("#viewMessageModal .view-message-content"));

    </script>
</body>
</html>
