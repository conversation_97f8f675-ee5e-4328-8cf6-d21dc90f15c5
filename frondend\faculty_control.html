<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faculty Page</title>
    <link rel="stylesheet" href="css/header_footer.css">
    <!-- Linking to the external CSS files -->
    <link rel="stylesheet" href="css/facultycontrol.css">
    <!-- إصلاح CSS -->
    <link rel="stylesheet" href="css/fix.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>

    <!-- استدعاء الهيدر -->
    <div id="header"></div>

    <!-- Hamburger menu button for mobile -->
    <div id="menuToggle" class="menu-toggle">☰</div>

    <!-- Overlay for mobile sidebar -->
    <div id="sidebarOverlay" class="sidebar-overlay"></div>

    <!-- Sidebar Navigation -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>Control panel</h3>
        </div>
        <ul class="sidebar-nav">
            <li><a href="#personal-info" class="sidebar-link active" data-target-section="personal-info">Personal Information</a></li>
            <li><a href="#doctor-description" class="sidebar-link" data-target-section="doctor-description">Faculty Biography</a></li>
            <li><a href="#course-materials" class="sidebar-link" data-target-section="course-materials">Lectures and Materials</a></li>
            <li><a href="#academic-research" class="sidebar-link" data-target-section="academic-research">Research</a></li>
            <li><a href="#messages-section" class="sidebar-link" data-target-section="messages-section">Messages</a></li>

        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <h1> Personal Page</h1>
        <div class="container">
            <div id="ui-message-area"></div>

            <!-- Messages Section - This will be shown when "Messages" is clicked -->
            <div class="section" id="messages-section">
                <div class="iframe">
                    <!-- The iframe loads masege.html content here -->
                    <iframe id="messagesFrame" src="masege.html" title="Messages Content"></iframe>

                </div>
            </div>

            <!-- All other main sections that appear on the main page -->
            <div class="section" id="personal-info">
                <h2>Personal Information</h2>
                <div class="card">
                    <div class="profile-section">
                        <div class="profile-container">
                            <!-- Removed default image URL -->
                            <img id="personalImage" class="profile-img" src="" alt="Profile photo">
                        </div>
                        <!-- Removed onchange, event listener is in JS -->
                        <!-- The profile image input is already hidden via inline style -->
                        <input type="file" id="profileImageInput" accept="image/*" style="display: none;">
                        <!-- Added id="changePhotoBtn" to the existing button -->
                        <button class="secondary-btn" id="changePhotoBtn">Change Photo</button>
                        <!-- NEW BUTTONS: Save Photo and Cancel Photo Change -->
                        <button id="saveProfileImageBtn" class="add-btn hidden">Save Photo</button>
                        <button id="cancelProfileImageBtn" class="cancel-btn hidden">Cancel Photo Change</button>
                    </div>
                    <div class="info">
                        <!-- Removed default values from input fields -->
                        <p><strong id="displayFullName"> </strong><input type="text" id="editFullName" class="hidden"></p>
                        <p><strong id="displaySpecialization"> </strong><input type="text" id="editSpecialization" class="hidden"></p>
                        <p><strong id="displayEmail"> </strong><input type="text" id="editEmail" class="hidden"></p>
                        <!-- CHANGED: Replaced input type="text" with select for department -->
                        <p>
                            <strong id="displayDepartment"> </strong>
                            <select id="editDepartment" class="hidden"></select>
                        </p>
                        <div class="button-group">
                            <!-- Removed onclick, event listener is in JS -->
                            <button id="editInfoBtn" class="primary-btn">Edit Information</button>
                            <!-- Removed onclick, event listener is in JS -->
                            <button id="saveInfoBtn" class="add-btn hidden">Save Changes</button>
                            <!-- Removed onclick, event listener is in JS -->
                            <button id="cancelInfoBtn" class="cancel-btn hidden">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- New Doctor Description Section -->
            <div class="section" id="doctor-description">
                <h2>Faculty Biography</h2>
                <div class="card">
                    <div class="description-content">
                        <!-- Wrapper to manage height and prevent layout shifts -->
                        <div class="description-text-area-wrapper" id="descriptionTextAreaWrapper">
                            <!-- Display description -->
                            <p id="doctorDescriptionDisplay" class="description-display-text"></p>
                            <!-- Editable textarea for description - NOW HIDDEN BY DEFAULT -->
                            <textarea id="doctorDescriptionEdit" class="description-edit-textarea hidden" cols="60" rows="10"></textarea>
                        </div>

                        <!-- Moved button-group to the bottom of description-content -->
                        <div class="button-group">
                            <!-- Removed onclick, event listener is in JS -->
                            <button id="editDescriptionBtn" class="primary-btn">Edit Biography</button>
                            <!-- Removed onclick, event listener is in JS -->
                            <button id="saveDescriptionBtn" class="add-btn hidden">Save Changes</button>
                            <!-- Removed onclick, event listener is in JS -->
                            <button id="cancelDescriptionBtn" class="cancel-btn hidden">Cancel</button>
                        </div>
                    </div>
                    <div class="description-image-wrapper">
                        <!-- Button to show the certificate modal -->
                        <button class="secondary-btn" id="viewCertificateBtn" style="margin-top: 15px;">View Certificate</button>
                        <!-- Custom file input interface for certificate -->
                        <div class="file-input-container hidden" style="margin-top: 15px;">
                            <!-- MODIFIED: Added inline style to hide the native file input -->
                            <input type="file" id="certificateImageInput" accept="image/*" style="display: none;">
                            <button type="button" class="secondary-btn" id="uploadCertificateBtn">Upload Image</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section" id="course-materials">
                <h2>Current Semester Courses</h2>
                <div class="card">
                    <div style="width: 100%;">
                        <!-- Removed onchange, event listener is in JS -->
                        <select id="courseName">
                            <option value="" disabled selected>Select Course</option>
                            <!-- Options will be populated dynamically -->
                        </select>

                        <!-- Removed onclick, event listener is in JS -->
                        <button id="showAddCourseFormBtn" class="add-btn" style="margin-top: 15px;">Add New Course</button>

                        <div id="addCourseForm" class="hidden" style="margin-top: 15px; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
                            <h4>Add New Course</h4>
                            <!-- MODIFIED: Replaced input text with a select dropdown for course material -->
                            <select id="newCourseMaterialSelect" required style="margin-bottom: 10px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 100%; box-sizing: border-box;">
                                <option value="" disabled selected>Select Course Material</option>
                                <!-- Options will be populated dynamically by JS -->
                            </select>
                            <input type="text" id="newCourseSemester" placeholder="Semester (e.g., Autumn 2024)" required>
                            <!-- Removed onclick, event listener is in JS -->
                            <button class="add-btn" id="addCourseBtn">Save Course</button>
                            <!-- Removed onclick, event listener is in JS -->
                            <button class="cancel-btn" id="cancelAddCourseBtn">Cancel</button>
                        </div>

                        <!-- NEW: Edit Lecture Form -->
                        <div id="editLectureForm" class="hidden" style="margin-top: 15px; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
                            <h4>Edit Lecture</h4>
                            <input type="text" id="editLectureTitle" placeholder="Lecture Title" required>
                            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                <div class="file-input-container hidden"> <!-- Added hidden class -->
                                    <input type="file" id="editLectureFileInput" accept=".pdf,.doc,.docx" style="display: none;">
                                    <button type="button" class="secondary-btn" id="chooseEditLectureFileBtn" style="margin-top: 0; margin-right: 5px;">Choose New File</button>
                                </div>
                                <span id="editFileSelectedName" style="margin-left: 10px; color: #555;">No new file chosen. Existing file will be used.</span>
                            </div>
                            <button class="add-btn" id="updateLectureBtn">Save Changes</button>
                            <button class="delete-btn" id="cancelEditLectureBtn">Cancel</button>
                        </div>

                        <div id="courseLectures" class="course-lectures hidden">
                            <h3 id="selectedCourseTitle"></h3>
                            <div id="lecturesList"></div>

                            <div id="addLectureForm" class="hidden" style="margin-top: 15px; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
                                <h4>Add New Lecture</h4>
                                <input type="text" id="lectureTitle" placeholder="Lecture Title" required>
                                <!-- Custom file input interface -->
                                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                    <div class="file-input-container hidden"> <!-- Added hidden class -->
                                        <input type="file" id="lectureFileInput" accept=".pdf,.doc,.docx" style="display: none;">
                                        <button type="button" class="secondary-btn" id="chooseFileBtn" style="margin-top: 0; margin-right: 5px;">Choose File</button>
                                    </div>
                                    <span id="fileSelectedName" style="margin-left: 10px; color: #555;"></span>
                                </div>
                                <!-- Removed onclick, event listener is in JS -->
                                <button class="add-btn" id="addLectureBtn">Save Lecture</button>
                                <!-- Removed onclick, event listener is in JS -->
                                <button class="delete-btn" id="cancelAddLectureBtn">Cancel</button>
                            </div>

                            <!-- Removed onclick, event listener is in JS -->
                            <button id="showAddLectureFormBtn" class="add-btn">Add New Lecture</button>
                            <!-- NEW DELETE COURSE BUTTON HERE -->
                            <!-- Removed onclick, event listener is in JS -->
                            <button id="deleteSelectedCourseBtn" class="delete-btn hidden" style="margin-left: 10px;">Delete Course</button>
                            <!-- NEW ARCHIVE COURSE BUTTON HERE -->
                            <button id="archiveCourseBtn" class="hidden primary-btn" data-material-id="" data-is-archived="">Archive Course</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section" id="academic-research">
                <h2>Academic Research</h2>
                <div class="card">
                    <div style="width: 100%;">
                        <!-- Removed onclick, event listener is in JS -->
                        <button id="toggleResearchFormBtn" class="add-btn">Add New Research</button>

                        <div id="newResearchForm" class="hidden" style="margin-top: 15px; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
                            <h4>Add New Research</h4>
                            <input type="text" id="newResearchTitle" placeholder="Research Title" required>
                            <input type="date" id="newResearchDate" placeholder="Publication Date" required>
                            <!-- Custom file input for Research File -->
                            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                <div class="file-input-container hidden"> <!-- Added hidden class -->
                                    <input type="file" id="newResearchFileInput" accept=".pdf,.doc,.docx" style="display: none;">
                                    <button type="button" class="secondary-btn" id="chooseResearchFileBtn" style="margin-top: 0; margin-right: 5px;">Choose File</button>
                                </div>
                                <span id="researchFileSelectedName" style="margin-left: 10px; color: #555;"></span>
                            </div>
                            <!-- Removed onclick, event listener is in JS -->
                            <button class="add-btn" id="submitNewResearchBtn">Save Research</button>
                            <!-- Removed onclick, event listener is in JS -->
                            <button class="delete-btn" id="cancelNewResearchBtn">Cancel</button>
                        </div>

                        <div id="editResearchForm" class="hidden" style="margin-top: 15px; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
                            <h4>Edit Research</h4>
                            <input type="text" id="editResearchTitle" placeholder="Research Title" required>
                            <input type="date" id="editResearchDate" placeholder="Publication Date" required>
                            <!-- Custom file input for Edit Research File -->
                            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                <div class="file-input-container hidden"> <!-- Added hidden class -->
                                    <input type="file" id="editResearchFileInput" accept=".pdf,.doc,.docx" style="display: none;">
                                    <button type="button" class="secondary-btn" id="chooseEditResearchFileBtn" style="margin-top: 0; margin-right: 5px;">Choose New File</button>
                                </div>
                                <span id="editResearchFileSelectedName" style="margin-left: 10px; color: #555;"></span>
                            </div>
                            <!-- Removed onclick, event listener is in JS -->
                            <button class="add-btn" id="updateResearchBtn">Save Changes</button>
                            <!-- Removed onclick, event listener is in JS -->
                            <button class="delete-btn" id="cancelEditResearchBtn">Cancel</button>
                        </div>

                        <div id="researchesList" class="research-form-container" style="margin-top: 20px;">
                            <table class="research-table">
                                <thead>
                                    <tr>
                                        <th>Research Title</th>
                                        <th>Publication Date</th>
                                        <th>Research File</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="researchTableBody">
                                    <!-- Research items will be rendered here by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Certificate Modal Structure -->
    <div id="certificateModal" class="modal-overlay hidden">
        <div class="modal-content" id="draggableModalContent">
            <span class="close-button" id="closeModalBtn">&times;</span>
            <!-- Removed default certificate image URL -->
            <img id="modalCertificateImage" src="" alt="Doctorate Certificate">
            <!-- The save action will now be handled by saveDescriptionBtn on the main page -->
            <div class="button-group" style="margin-top: 15px;">
                <button class="secondary-btn" id="closeCertificateModalBtn"> Close </button>
            </div>
        </div>
    </div>

    <!-- استدعاء الفوتر -->
    <div id="footer"></div>

    <!-- Linking to the external JavaScript file -->
       
    <script src="javascript/FacultyControl.js"></script>
     <script src="javascript/header_footer.js"></script>

</body>
</html>
