// Global references for sections
const mainPageSections = ['personal-info', 'doctor-description', 'course-materials', 'academic-research']; // الأقسام الرئيسية في الصفحة
const messagesSectionId = 'messages-section'; // معرف قسم الرسائل

// coursesData الآن تخزن كائنات تحتوي على اسم المادة والفصل الدراسي والمحاضرات
// تهيئة مصفوفة coursesData (بيانات مثال، عادةً ما يتم تحميلها من الواجهة الخلفية/التخزين)
// Will now store data fetched from backend including assignmentid, materialname, semester
let coursesData = [];

// عنصر نائب للملف المختار حاليًا للمحاضرة/البحث (سيحتوي على كائن File)
let currentLectureFile = null;
let currentResearchFileObject = null; // Stores the File object for research, to be converted to Base64 later

// عنصر نائب لمعرف البحث الذي يتم تعديله
let currentEditResearchId = null;
// NEW: Placeholder for the lecture ID currently being edited
let currentEditLectureId = null; // معرف المحاضرة الذي يتم تعديله حالياً

// بيانات البحث الأولية (بيانات مثال) - ستُملأ من الواجهة الخلفية
let researches = [];    

// بيانات عضو هيئة التدريس المحملة من الخادم
let facultyData = {};
// معرف عضو هيئة التدريس (للتجربة، في تطبيق حقيقي يأتي من نظام المصادقة)
const facultyId = 1; // <--- تأكد أن هذا يتطابق مع معرف الأستاذ في قاعدة البيانات لديك

// ملف الصورة الشخصية المختار حاليا (لإرساله عند الحفظ)
let currentProfileImageFile = null;
// ملف صورة الشهادة المختار حاليا (لإرساله عند الحفظ)
let currentCertificateFile = null; // Changed from currentCertificateImageFile for clarity

// خريطة لتخزين الأقسام (departmentid إلى departmentname)
let departmentsMap = {};


/**
 * تتحكم هذه الدالة في رؤية أقسام المحتوى في المنطقة الرئيسية.
 * عندما يكون `sectionIdToShow` هو 'messages-section'، يتم عرض الرسائل فقط.
 * بخلاف ذلك، يتم عرض جميع الأقسام الرئيسية، وتنتقل الصفحة إلى الهدف.
 * @param {string} sectionIdToShow - معرف القسم الذي سيتم عرضه أو التركيز عليه بشكل أساسي.
 */
function showContentSection(sectionIdToShow) {
    // إخفاء جميع الأقسام أولاً
    document.querySelectorAll('.section').forEach(section => {
        section.style.display = 'none';
        // Reset any inline styles that might interfere
        section.style.position = '';
        section.style.top = '';
        section.style.left = '';
        section.style.width = '';
        section.style.height = '';
    });

    if (sectionIdToShow === messagesSectionId) {
        // إذا كان القسم المستهدف هو الرسائل، قم بإظهار قسم الرسائل فقط
        const messagesSection = document.getElementById(messagesSectionId);
        messagesSection.style.display = 'block'; // إظهار قسم الرسائل
        // Position messagesSection absolutely within its parent (.main-content)
        messagesSection.style.position = 'absolute';
        messagesSection.style.top = '0';
        messagesSection.style.left = '0';
        messagesSection.style.width = '100%';
        messagesSection.style.height = '100%';

        // تحديث iframe لإعادة تحميل المحتوى باستخدام مسار نسبي
        document.getElementById('messagesFrame').src = './masege.html';

    } else {
        // إذا كان القسم المستهدف هو أحد الأقسام الرئيسية، قم بإظهار جميع الأقسام الرئيسية
        mainPageSections.forEach(id => {
            const section = document.getElementById(id);
            if (section) section.style.display = 'block';
        });
        const targetElement = document.getElementById(sectionIdToShow);
        if (targetElement) {
            targetElement.scrollIntoView({ behavior: 'smooth' }); // التمرير بسلاسة إلى العنصر الهدف
        }
    }

    updateSidebarActiveLink(sectionIdToShow); // تحديث الرابط النشط في الشريط الجانبي
}

/**
 * تقوم بتحديث الفئة 'active' على روابط الشريط الجانبي.
 * @param {string} activeId - معرف القسم الذي يجب تحديده كنشط.
 */
function updateSidebarActiveLink(activeId) {
    const links = document.querySelectorAll('.sidebar-nav .sidebar-link');
    links.forEach(link => {
        link.classList.remove('active'); // إزالة الفئة 'active' من جميع الروابط
        if (link.dataset.targetSection === activeId) {
            link.classList.add('active'); // إضافة الفئة 'active' إلى الرابط الهدف
        }
    });
}

/**
 * تقوم بإعداد مستمعي أحداث النقر لروابط التنقل في الشريط الجانبي.
 */
function setupSidebarNavigation() {
    console.log('setupSidebarNavigation: Setting up sidebar event listeners.');
    const links = document.querySelectorAll('.sidebar-nav .sidebar-link');

    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault(); // منع سلوك المتصفح الافتراضي لروابط الهاش

            const targetSectionId = this.dataset.targetSection; // الحصول على معرف القسم الهدف من سمة البيانات
            showContentSection(targetSectionId); // استدعاء الدالة المركزية لإدارة رؤية القسم

            // إغلاق الشريط الجانبي بعد النقر على رابط على الشاشات الصغيرة (لاستجابة الجوال)
            if (window.innerWidth <= 768) {
                toggleSidebar();
            }
        });
    });

    // إضافة مستمع حدث التمرير لتسليط الضوء على القسم النشط في الصفحة الرئيسية
    window.addEventListener('scroll', function() {
        // تحديث الرابط النشط بناءً على التمرير فقط إذا كان قسم الرسائل مخفيًا
        if (document.getElementById(messagesSectionId).style.display === 'none') {
            let currentSectionId = '';

            // تحديد القسم الرئيسي الذي يظهر حاليًا
            mainPageSections.forEach(id => {
                const section = document.getElementById(id);
                if (section && section.style.display !== 'none') { // التحقق مما إذا كان القسم مرئيًا
                    const rect = section.getBoundingClientRect();
                    // يعتبر القسم "نشطًا" إذا كانت حافته العلوية ضمن نطاق معين من حافة عرض الإطار
                    if (rect.top <= 150 && rect.bottom >= 150) { // ضبط تعويض 150 بكسل حسب الحاجة لتخطيطك
                        currentSectionId = section.getAttribute('id');
                    }
                }
            });

            // إذا تم التمرير إلى الأعلى ولم يتم تمييز قسم معين، فليكن personal-info هو الافتراضي
            if (!currentSectionId && window.scrollY < 200) { // ضبط عتبة 200 بكسل بناءً على تخطيطك
                currentSectionId = 'personal-info';
            }

            if (currentSectionId) {
                updateSidebarActiveLink(currentSectionId);
            }
        }
    });
}

// دالة لعرض رسائل واجهة المستخدم المخصصة (محتفظ بها من الإصدارات السابقة)
function showUIMessage(message, type = 'info') { // الأنواع: info, success, error
    console.log(`showUIMessage called: Message: "${message}", Type: "${type}"`); // Debug log for messages
    let messageArea = document.getElementById('ui-message-area');
    if (!messageArea) {
        messageArea = document.createElement('div');
        messageArea.id = 'ui-message-area';
        document.querySelector('.container').prepend(messageArea); // إضافة العنصر في بداية الحاوية
    }

    messageArea.textContent = message; // تعيين نص الرسالة
    // ضبط أنماط الخلفية واللون والحد بناءً على نوع الرسالة
    if (type === 'error') {
        messageArea.style.backgroundColor = '#f8d7da';
        messageArea.style.color = '#721c24';
        messageArea.style.border = '1px solid #f5c6cb';
    } else if (type === 'success') {
        messageArea.style.backgroundColor = '#d4edda';
        messageArea.style.color = '#155724';
        messageArea.style.border = '1px solid #c3e6cb';
    } else {
        messageArea.style.backgroundColor = '#e2e3e5';
        messageArea.style.color = '#383d41';
        messageArea.style.border = '1px solid #d6d8db';
    }
    messageArea.style.display = 'block'; // إظهار منطقة الرسالة

    // مسح أي مهلة سابقة لمنع التداخل
    if (messageArea.timeoutId) {
        clearTimeout(messageArea.timeoutId);
    }

    // إخفاء الرسالة بعد 3 ثوانٍ
    messageArea.timeoutId = setTimeout(() => {
        messageArea.style.display = 'none';
    }, 3000);
}

// دالة لعرض مربع حوار تأكيد مخصص (محتفظ بها من الإصدارات السابقة)
function showCustomConfirm(message, callback) {
    const existingModal = document.getElementById('customConfirmModal');
    if (existingModal) {
        existingModal.remove(); // إزالة أي مربع حوار موجود لمنع التكرار
    }

    const confirmModal = document.createElement('div');
    confirmModal.id = 'customConfirmModal';
    confirmModal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5); /* خلفية شبه شفافة */
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000; /* ضمان ظهور المودال فوق العناصر الأخرى */
    `;

    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background-color: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); /* ظل للمودال */
        text-align: center;
        max-width: 400px;
        width: 90%;
        box-sizing: border-box; /* تضمين الحشوة والحدود في العرض الكلي للعنصر */
    `;

    const messageP = document.createElement('p');
    messageP.textContent = message; // تعيين نص الرسالة
    messageP.style.cssText = `
        font-size: 1.1em;
        margin-bottom: 20px;
        color: #333;
    `;

    modalContent.appendChild(messageP);

    const buttonContainer = document.createElement('div');
    buttonContainer.style.cssText = `
        display: flex;
        justify-content: center;
        gap: 15px; /* زيادة المسافة بين الأزرار */
    `;

    const yesButton = document.createElement('button');
    yesButton.textContent = 'Yes'; // نص زر التأكيد
    yesButton.className = 'add-btn';
    yesButton.style.cssText = `
        padding: 10px 25px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 1em;
        border: none;
        background-color: #28a745; /* لون أخضر للتأكيد */
        color: white;
        transition: background-color 0.3s ease;
    `;
    yesButton.onmouseover = () => yesButton.style.backgroundColor = '#218838'; // تغيير اللون عند التمرير
    yesButton.onmouseout = () => yesButton.style.backgroundColor = '#28a745'; // إعادة اللون الأصلي عند إزالة التمرير


    const noButton = document.createElement('button');
    noButton.textContent = 'No'; // نص زر الإلغاء
    noButton.className = 'delete-btn';
    noButton.style.cssText = `
        padding: 10px 25px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 1em;
        border: none;
        background-color: #dc3545; /* لون أحمر للإلغاء */
        color: white;
        transition: background-color 0.3s ease;
    `;
    noButton.onmouseover = () => noButton.style.backgroundColor = '#c82333'; // تغيير اللون عند التمرير
    noButton.onmouseout = () => noButton.style.backgroundColor = '#dc3545'; // إعادة اللون الأصلي عند إزالة التمرير

    yesButton.onclick = () => {
        confirmModal.remove(); // إزالة المودال
        callback(true); // استدعاء رد الاتصال بقيمة true
    };
    noButton.onclick = () => {
        confirmModal.remove(); // إزالة المودال
        callback(false); // استدعاء رد الاتصال بقيمة false
    };

    buttonContainer.appendChild(yesButton);
    buttonContainer.appendChild(noButton);
    modalContent.appendChild(buttonContainer);
    confirmModal.appendChild(modalContent);
    document.body.appendChild(confirmModal); // إضافة المودال إلى جسم المستند
}

/**
 * دالة لجلب جميع الأقسام من الخادم وملء departmentsMap.
 */
async function fetchDepartments() {
    console.log('fetchDepartments: Attempting to fetch departments...');
    try {
        const response = await fetch('/faculty_web/backend/faculty_control.php?action=get_departments');
        console.log('fetchDepartments: Response status:', response.status); // NEW LOG
        if (!response.ok) { // Check if HTTP status is 200-299
            const errorText = await response.text();
            console.error(`fetchDepartments: HTTP error! Status: ${response.status}, Response: ${errorText}`);
            throw new Error(`Server responded with status ${response.status}`);
        }
        const data = await response.json();
        console.log('fetchDepartments: Response data:', data); // NEW LOG: Log the parsed JSON

        if (data.status === 'success' && Array.isArray(data.data)) {
            departmentsMap = {}; // مسح الخريطة الحالية
            data.data.forEach(dept => {
                departmentsMap[dept.departmentid] = dept.departmentname;
            });
            console.log('Departments loaded:', departmentsMap);
        } else {
            console.error('Error loading departments:', data.message);
            showUIMessage(`Error loading departments: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('Network error during departments fetch:', error);
        showUIMessage(`Network error during departments fetch: ${error.message}`, 'error');
    }
}


/**
 * دالة لجلب بيانات عضو هيئة التدريس من الخادم وملء الواجهة.
 */
async function fetchFacultyInfo() {
    console.log('fetchFacultyInfo: Attempting to fetch faculty info...');
    try {
        const url = `/faculty_web/backend/faculty_control.php?action=get_faculty_info&facultyid=${facultyId}`;
        console.log('fetchFacultyInfo: Fetching URL:', url); // NEW LOG: URL being fetched
        const response = await fetch(url);
        console.log('fetchFacultyInfo: Response status for faculty info:', response.status); // NEW LOG
        if (!response.ok) { // Check if HTTP status is 200-299
            const errorText = await response.text();
            console.error(`fetchFacultyInfo: HTTP error! Status: ${response.status}, Response: ${errorText}`);
            showUIMessage(`Error loading profile data: Server responded with status ${response.status}.`, 'error');
            throw new Error(`Server responded with status ${response.status}`);
        }
        const data = await response.json();
        console.log('fetchFacultyInfo: Raw response data:', data); // NEW LOG: Log the raw response data

        if (data.status === 'success') {
            facultyData = data.data; // تخزين البيانات التي تم جلبها عالميًا
            console.log('fetchFacultyInfo: Faculty data received successfully:', facultyData); // NEW LOG

            // ملء عناصر العرض
            document.getElementById('displayFullName').textContent = facultyData.fullname || 'N/A';
            document.getElementById('displaySpecialization').textContent = facultyData.specialization || 'N/A';
            document.getElementById('displayEmail').textContent = facultyData.email || 'N/A';
            document.getElementById('displayDepartment').textContent = facultyData.departmentname || 'N/A';
            document.getElementById('doctorDescriptionDisplay').textContent = facultyData.description || 'No description provided.';
            console.log('fetchFacultyInfo: Display elements updated.');

            // ملء حقول الإدخال للتعديل
            document.getElementById('editFullName').value = facultyData.fullname || '';
            document.getElementById('editSpecialization').value = facultyData.specialization || '';
            document.getElementById('editEmail').value = facultyData.email || '';
            document.getElementById('doctorDescriptionEdit').value = facultyData.description || '';
            // حقل القسم سيتم ملؤه بواسطة populateDepartmentDropdown() عند enableEditing()
            console.log('fetchFacultyInfo: Edit fields updated.');


            // تحديث صورة الملف الشخصي
            const personalImage = document.getElementById('personalImage');
            console.log('fetchFacultyInfo: Processing profile image. Data length:', facultyData.imagepath ? facultyData.imagepath.length : '0'); // NEW LOG
            if (facultyData.imagepath && typeof facultyData.imagepath === 'string' && facultyData.imagepath.length > 0) {
                personalImage.src = `data:image/jpeg;base64,${facultyData.imagepath}`; // Assuming JPEG
            } else {
                personalImage.src = 'https://placehold.co/150x150/cccccc/000000?text=No+Image'; // صورة افتراضية
            }
            personalImage.alt = "Profile Photo"; // إضافة alt text

            // تحديث صورة الشهادة (إذا كانت موجودة)
            const modalCertificateImage = document.getElementById('modalCertificateImage');
            console.log('fetchFacultyInfo: Processing certificate image. Data length:', facultyData.certificateImage ? facultyData.certificateImage.length : '0'); // NEW LOG
            if (modalCertificateImage && facultyData.certificateImage && typeof facultyData.certificateImage === 'string' && facultyData.certificateImage.length > 0) {
                modalCertificateImage.src = `data:image/jpeg;base64,${facultyData.certificateImage}`; // Assuming JPEG
            } else if (modalCertificateImage) {
                modalCertificateImage.src = 'https://placehold.co/150x150/cccccc/000000?text=No+Certificate'; // صورة افتراضية
            }
            modalCertificateImage.alt = "Doctorate Certificate"; // إضافة alt text
            console.log('fetchFacultyInfo: Images processed.');

            showUIMessage('Profile data loaded successfully!', 'success');
        } else {
            console.error('fetchFacultyInfo: Error status from server:', data.message); // NEW LOG
            showUIMessage(`Error loading profile data: ${data.message}`, 'error');
        }
    }
    catch (error) {
        console.error('fetchFacultyInfo: Network error or JavaScript error during profile data fetch:', error); // NEW LOG
        showUIMessage(`Network error during profile data fetch: ${error.message}`, 'error');
    }
}


/**
 * دالة لحفظ تغييرات البيانات الشخصية (الاسم، البريد الإلكتروني، التخصص، القسم) إلى الخادم.
 * يتم استدعاؤها من زر "Save Changes" في قسم المعلومات الشخصية.
 */
async function saveProfileData() {
    console.log('saveProfileData: Attempting to save profile data.'); // NEW LOG
    // جمع البيانات من حقول الإدخال
    const fullname = document.getElementById('editFullName').value;
    const email = document.getElementById('editEmail').value;
    const specialization = document.getElementById('editSpecialization').value;
    const departmentid = document.getElementById('editDepartment').value;

    // التحقق من صحة departmentid قبل الإرسال
    if (!departmentid || departmentid === "") {
        showUIMessage('Please select a department.', 'error');
        console.warn('saveProfileData: Department not selected.'); // NEW LOG
        return;
    }

    const formData = new FormData();
    formData.append('facultyid', facultyId);
    formData.append('action', 'update_personal_details'); // تحديد الإجراء لـ PHP
    formData.append('fullname', fullname);
    formData.append('email', email);
    formData.append('specialization', specialization);
    formData.append('departmentid', departmentid);
    console.log('saveProfileData: FormData prepared:', Object.fromEntries(formData.entries())); // NEW LOG

    try {
        const response = await fetch('/faculty_web/backend/faculty_control.php', {
            method: 'POST',
            body: formData
        });
        const data = await response.json();
        console.log('saveProfileData: Backend response:', data); // NEW LOG

        if (data.status === 'success') {
            showUIMessage('Personal details updated successfully!', 'success');
            await fetchFacultyInfo(); // إعادة جلب البيانات لتحديث الواجهة
            cancelEditing(); // العودة إلى وضع العرض للمعلومات الشخصية
        } else {
            showUIMessage(`Error updating personal details: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('saveProfileData: Network error during personal details update:', error); // NEW LOG
        showUIMessage(`Network error during personal details update: ${error.message}`, 'error');
    }
}

/**
 * دالة لحفظ تغييرات الوصف (السيرة الذاتية لأعضاء هيئة التدريس) إلى الخادم.
 * يتم استدعاؤها من زر "Save Changes" في قسم السيرة الذاتية.
 */
async function saveDescriptionData() {
    console.log('saveDescriptionData: Attempting to save description and\/or certificate.'); // NEW LOG
    const description = document.getElementById('doctorDescriptionEdit').value;
    const descriptionChanged = facultyData.description !== description;
    const certificateImageSelected = currentCertificateFile !== null;

    if (!descriptionChanged && !certificateImageSelected) {
        showUIMessage('No changes to save for biography or certificate.', 'info');
        resetDescriptionDisplayMode(); // Use the new reset function
        console.log('saveDescriptionData: No changes detected.'); // NEW LOG
        return;
    }

    let overallSuccess = true;
    let message = [];

    // --- Save Description (if changed) ---
    if (descriptionChanged) {
        console.log('saveDescriptionData: Description changed, attempting to save...'); // NEW LOG
        const formDataDesc = new FormData();
        formDataDesc.append('facultyid', facultyId);
        formDataDesc.append('action', 'update_description');
        formDataDesc.append('description', description);
        try {
            const response = await fetch('/faculty_web/backend/faculty_control.php', {
                method: 'POST',
                body: formDataDesc
            });
            const data = await response.json();
            console.log('saveDescriptionData: Description save backend response:', data); // NEW LOG
            if (data.status === 'success') {
                message.push('Biography description updated successfully!');
            } else {
                message.push(`Error updating biography description: ${data.message}`);
                overallSuccess = false;
            }
        } catch (error) {
            console.error('saveDescriptionData: Network error updating biography description:', error); // NEW LOG
            message.push(`Network error updating biography description: ${error.message}`);
            overallSuccess = false;
        }
    }

    // --- Save Certificate Image (if selected) ---
    if (certificateImageSelected) {
        console.log('saveDescriptionData: Certificate image selected. File name:', currentCertificateFile.name, 'Size:', currentCertificateFile.size); // Added log
        // Convert file to Base64 before sending
        const reader = new FileReader();
        const fileConversionPromise = new Promise((resolve, reject) => {
            reader.onload = () => resolve(reader.result.split(',')[1]);
            reader.onerror = reject;
            reader.readAsDataURL(currentCertificateFile);
        });

        try {
            const fileBase64 = await fileConversionPromise;
            const formDataCert = new FormData();
            formDataCert.append('facultyid', facultyId);
            formDataCert.append('action', 'update_certificate_image');
            formDataCert.append('certificateImage', fileBase64); // Send Base64 string
            console.log('saveDescriptionData: Certificate FormData prepared:', { action: 'update_certificate_image', facultyid: facultyId, certificateImage_length: fileBase64.length }); // NEW LOG

            const response = await fetch('/faculty_web/backend/faculty_control.php', {
                method: 'POST',
                body: formDataCert
            });
            const data = await response.json();
            console.log('saveDescriptionData: Certificate save backend response:', data); // NEW LOG
            if (data.status === 'success') {
                message.push('Certificate image updated successfully!');
                currentCertificateFile = null; // Clear file only on success
                document.getElementById('certificateImageInput').value = ''; // Clear file input
            } else {
                message.push(`Error updating certificate image: ${data.message}`); // Removed 'error' type here, will apply overall
                overallSuccess = false;
            }
        } catch (error) {
            console.error('saveDescriptionData: Network error updating certificate image:', error); // NEW LOG
            message.push(`Network error updating certificate image: ${error.message}`); // Removed 'error' type here
            overallSuccess = false;
        }
    }

    // Determine final message type
    let messageType = 'info';
    if (overallSuccess && (descriptionChanged || certificateImageSelected)) {
        messageType = 'success';
    } else if (!overallSuccess && (descriptionChanged || certificateImageSelected)) {
        messageType = 'error';
    } else {
        messageType = 'info'; // Fallback for no changes or other cases
    }

    showUIMessage(message.join(' '), messageType);

    await fetchFacultyInfo(); // Re-fetch all data to update the UI
    resetDescriptionDisplayMode(); // Use the new reset function
}


/**
 * دالة لحفظ الصورة الشخصية فقط بشكل منفصل.
 */
async function saveProfileImage() {
    console.log('saveProfileImage: Attempting to save profile image.'); // NEW LOG
    if (!currentProfileImageFile) {
        showUIMessage('Please select an image to save first.', 'info');
        console.warn('saveProfileImage: No file selected.'); // NEW LOG
        return;
    }

    // Convert file to Base64 before sending
    const reader = new FileReader();
    const fileConversionPromise = new Promise((resolve, reject) => {
        reader.onload = () => resolve(reader.result.split(',')[1]);
        reader.onerror = reject;
        reader.readAsDataURL(currentProfileImageFile);
    });

    try {
        const fileBase64 = await fileConversionPromise;
        const formData = new FormData();
        formData.append('facultyid', facultyId); // PHP needs to know the faculty ID
        formData.append('action', 'update_image'); // Tell PHP this is an image-only update
        formData.append('imagepath', fileBase64); // Send Base64 string
        console.log('saveProfileImage: FormData prepared:', { action: 'update_image', facultyid: facultyId, imagepath_length: fileBase64.length }); // NEW LOG

        const response = await fetch('/faculty_web/backend/faculty_control.php', {
            method: 'POST',
            body: formData
        });
        const data = await response.json();
        console.log('saveProfileImage: Backend response:', data); // NEW LOG

        if (data.status === 'success') {
            showUIMessage('Profile image updated successfully!', 'success');
            await fetchFacultyInfo(); // إعادة جلب البيانات لتحديث الصورة المعروضة
            cancelImageChange(false); // إخفاء الأزرار والعودة بالواجهة إلى الحالة الأصلية، بدون رسالة "إلغاء"
        } else {
            showUIMessage(`Error updating profile image: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('saveProfileImage: Network error during profile image update:', error); // NEW LOG
        showUIMessage(`Network error during profile image update: ${error.message}`, 'error');
    }
}

/**
 * دالة لإلغاء تغيير الصورة واستعادة الصورة الأصلية (أو النائبة).
 * @param {boolean} showMessage - إذا كانت صحيحة، تعرض رسالة "Image change canceled.".
 */
function cancelImageChange(showMessage = true) {
    console.log('cancelImageChange: Cancelling image change.'); // NEW LOG
    currentProfileImageFile = null;
    document.getElementById('profileImageInput').value = ''; // مسح حقل إدخال الملف

    // استعادة الصورة الأصلية أو النائبة من facultyData
    const personalImage = document.getElementById('personalImage');
    if (facultyData.imagepath && typeof facultyData.imagepath === 'string' && facultyData.imagepath.length > 0) {
        personalImage.src = `data:image/jpeg;base64,${facultyData.imagepath}`;
    } else {
        personalImage.src = 'https://placehold.co/150x150/cccccc/000000?text=No+Image';
    }

    // إخفاء أزرار حفظ/إلغاء الصورة
    document.getElementById('saveProfileImageBtn').classList.add('hidden');
    document.getElementById('cancelProfileImageBtn').classList.add('hidden'); // Ensure cancel is hidden if Save is not pressed quickly after selection.
    // إظهار زر "Change Photo" الأصلي
    document.getElementById('changePhotoBtn').classList.remove('hidden');

    if (showMessage) {
        showUIMessage('Image change canceled.', 'info');
    }
}

/**
 * دالة لملء القائمة المنسدلة للأقسام وتحديد القسم الحالي.
 * @param {number|null} currentDepartmentId - المعرف العددي للقسم المحدد حاليًا.
 */
function populateDepartmentDropdown(currentDepartmentId = null) {
    console.log('populateDepartmentDropdown: Populating course dropdown. Current ID:', currentDepartmentId); // NEW LOG
    const departmentSelect = document.getElementById('editDepartment');
    departmentSelect.innerHTML = '<option value="" disabled selected>Select Department</option>'; // مسح الخيارات الموجودة

    // إضافة الخيارات من departmentsMap
    for (const id in departmentsMap) {
        const option = document.createElement('option');
        option.value = id;
        option.textContent = departmentsMap[id];
        if (parseInt(id) === parseInt(currentDepartmentId)) {
            option.selected = true;
        }
        departmentSelect.appendChild(option);
    }
    console.log('populateDepartmentDropdown: Dropdown populated with', Object.keys(departmentsMap).length, 'departments.'); // NEW LOG
}


// وظائف قسم المعلومات الشخصية
function enableEditing() {
    console.log('enableEditing: Enabling personal info editing.'); // NEW LOG
    // إخفاء عناصر العرض النصية وإظهار حقول الإدخال للتعديل
    document.getElementById('displayFullName').classList.add('hidden');
    document.getElementById('editFullName').classList.remove('hidden');
    document.getElementById('displaySpecialization').classList.add('hidden');
    document.getElementById('editSpecialization').classList.remove('hidden');
    document.getElementById('displayEmail').classList.add('hidden');
    document.getElementById('editEmail').classList.remove('hidden');
    document.getElementById('displayDepartment').classList.add('hidden');
    // عنصر القائمة المنسدلة للقسم
    document.getElementById('editDepartment').classList.remove('hidden');

    // ملء القائمة المنسدلة للقسم وتحديد القسم الحالي
    populateDepartmentDropdown(facultyData.departmentid);


    // إظهار أزرار الحفظ/الإلغاء وإخفاء زر التعديل
    document.getElementById('editInfoBtn').classList.add('hidden');
    document.getElementById('saveInfoBtn').classList.remove('hidden');
    document.getElementById('cancelInfoBtn').classList.remove('hidden');
}

function cancelEditing() {
    console.log('cancelEditing: Cancelling personal info editing.'); // NEW LOG
    // إعادة تعيين قيم حقول الإدخال إلى القيم المعروضة الأصلية
    document.getElementById('editFullName').value = facultyData.fullname || '';
    document.getElementById('editSpecialization').value = facultyData.specialization || '';
    document.getElementById('editEmail').value = facultyData.email || '';
    // بالنسبة للقسم، لا يلزم إعادة تعيين القيمة هنا لأننا سنعيد إخفاء القائمة المنسدلة وعرض النص.

    // إخفاء حقول الإدخال وإظهار عناصر العرض النصية
    document.getElementById('displayFullName').classList.remove('hidden');
    document.getElementById('editFullName').classList.add('hidden');
    document.getElementById('displaySpecialization').classList.remove('hidden');
    document.getElementById('editSpecialization').classList.add('hidden');
    document.getElementById('displayEmail').classList.remove('hidden');
    document.getElementById('editEmail').classList.add('hidden');
    document.getElementById('displayDepartment').classList.add('hidden');
    // إخفاء القائمة المنسدلة للقسم
    document.getElementById('editDepartment').classList.add('hidden');

    // إخفاء أزرار الحفظ/الإلغاء وإظهار زر التعديل
    document.getElementById('editInfoBtn').classList.remove('hidden');
    document.getElementById('saveInfoBtn').classList.add('hidden');
    document.getElementById('cancelInfoBtn').classList.add('hidden');

    showUIMessage('Editing canceled.', 'info'); // رسالة إلغاء التعديل
}

function updateImage() {
    console.log('updateImage: Profile image input changed.'); // NEW LOG
    const fileInput = document.getElementById('profileImageInput');
    if (fileInput.files.length > 0) {
        const file = fileInput.files[0];
        if (file.size > 2 * 1024 * 1024) { // 2MB limit
            showUIMessage('Image size must be less than 2MB.', 'error');
            fileInput.value = ''; // Clear the input
            currentProfileImageFile = null;
            return;
        }
        currentProfileImageFile = file;

        // Preview the image
        const reader = new FileReader();
        reader.onload = (e) => {
            document.getElementById('personalImage').src = e.target.result;
        };
        reader.readAsDataURL(file);

        // Show Save/Cancel buttons
        document.getElementById('saveProfileImageBtn').classList.remove('hidden');
        document.getElementById('cancelProfileImageBtn').classList.remove('hidden');
        document.getElementById('changePhotoBtn').classList.add('hidden'); // Hide original change button
    } else {
        currentProfileImageFile = null;
        // If no file selected (e.g., user opened dialog and closed without selection), revert to original state
        cancelImageChange(false); // Don't show "canceled" message, just reset UI
    }
}

// وظائف قسم السيرة الذاتية (Doctor Description)
function enableDescriptionEditing() {
    console.log('enableDescriptionEditing: Enabling description editing.'); // NEW LOG
    document.getElementById('doctorDescriptionDisplay').classList.add('hidden');
    document.getElementById('doctorDescriptionEdit').classList.remove('hidden');
    document.getElementById('editDescriptionBtn').classList.add('hidden');
    document.getElementById('saveDescriptionBtn').classList.remove('hidden');
    document.getElementById('cancelDescriptionBtn').classList.remove('hidden');
    
    // Show the certificate file input container explicitly
    document.querySelector('#doctor-description .file-input-container').classList.remove('hidden');
}

/**
 * NEW: Resets the UI for the description section to display mode.
 * This is called after saving or cancelling, without implying "canceled" if it was a save.
 */
function resetDescriptionDisplayMode() {
    console.log('resetDescriptionDisplayMode: Resetting description section to display mode.');
    document.getElementById('doctorDescriptionEdit').value = facultyData.description || ''; // إعادة تعيين القيمة الأصلية
    
    currentCertificateFile = null; // Clear the selected File object
    const certificateFileInput = document.getElementById('certificateImageInput');
    if (certificateFileInput) {
        certificateFileInput.value = ''; // Clear the file input element's value
        console.log('resetDescriptionDisplayMode: Certificate file input cleared.');
    } else {
        console.warn('resetDescriptionDisplayMode: certificateImageInput element not found.');
    }

    document.getElementById('doctorDescriptionDisplay').classList.remove('hidden');
    document.getElementById('doctorDescriptionEdit').classList.add('hidden');
    document.getElementById('editDescriptionBtn').classList.remove('hidden');
    document.getElementById('saveDescriptionBtn').classList.add('hidden');
    document.getElementById('cancelDescriptionBtn').classList.add('hidden');
    
    // Hide the certificate file input container explicitly
    document.querySelector('#doctor-description .file-input-container').classList.add('hidden');
    console.log('resetDescriptionDisplayMode: Hidden certificate file input container.');
}

// Modified cancelDescriptionEditing to use resetDescriptionDisplayMode
function cancelDescriptionEditing() {
    console.log('cancelDescriptionEditing: Cancelling description editing. Showing "canceled" message.'); // Explicit log
    resetDescriptionDisplayMode(); // Call the new reset function
    showUIMessage('Biography editing canceled.', 'info');
}

// =====================================================================
// وظائف قسم المواد الدراسية (Current Semester Courses)
// =====================================================================

/**
 * دالة لجلب المواد الدراسية المخصصة للأستاذ من الخادم وملء القائمة المنسدلة.
 * MODIFIED: Now fetches materialid and Is_archived status.
 */
async function fetchFacultyCourses() {
    console.log('fetchFacultyCourses: Attempting to fetch faculty courses...');
    try {
        const url = `/faculty_web/backend/faculty_control.php?action=get_faculty_courses&facultyid=${facultyId}`;
        const response = await fetch(url);
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`fetchFacultyCourses: HTTP error! Status: ${response.status}, Response: ${errorText}`);
            throw new Error(`Server responded with status ${response.status}`);
        }
        const data = await response.json();
        console.log('fetchFacultyCourses: Received courses data:', data);

        const courseNameSelect = document.getElementById('courseName');
        courseNameSelect.innerHTML = '<option value="" disabled selected>Select Course</option>'; // Clear existing options

        if (data.status === 'success' && Array.isArray(data.data)) {
            // Map the data to ensure materialid is always parsed as an integer or null
            coursesData = data.data.map(course => {
                let parsedMaterialId = parseInt(course.materialid);
                if (isNaN(parsedMaterialId)) {
                    console.warn(`Invalid materialid '${course.materialid}' found for course '${course.materialname}'. Setting to null for internal use.`);
                    parsedMaterialId = null; // Set to null if invalid, indicating absence
                }
                return {
                    ...course,
                    materialid: parsedMaterialId // Store the parsed (or null) materialid
                };
            });

            coursesData.forEach(course => {
                const option = document.createElement('option');
                option.value = course.assignmentid;
                
                // Set data attributes as strings for HTML elements
                option.dataset.materialId = course.materialid !== null ? String(course.materialid) : '';
                option.dataset.isArchived = course.Is_archived ? '1' : '0';
                option.textContent = `${course.materialname} (${course.semester}) ${course.Is_archived ? '[Archived]' : ''}`;
                courseNameSelect.appendChild(option);
            });
            showUIMessage('Faculty courses loaded.', 'success');
        } else {
            console.error('fetchFacultyCourses: Error loading courses:', data.message);
            showUIMessage(`Error loading courses: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('fetchFacultyCourses: Network error during courses fetch:', error);
        showUIMessage(`Network error loading courses: ${error.message}`, 'error');
    }
}

/**
 * NEW: Function to toggle the archive status of a course material.
 * @param {number} materialId The ID of the material to toggle.
 * @param {boolean} isArchived The current archived status (true if archived, false otherwise).
 */
async function toggleArchiveCourse(materialId, isArchived) {
    console.log(`toggleArchiveCourse: Toggling archive status for Material ID: ${materialId}. Current status: ${isArchived}`);
    const newStatus = isArchived ? 0 : 1; // 0 for unarchive, 1 for archive

    const formData = new FormData();
    formData.append('action', 'toggle_archive_material');
    formData.append('facultyid', facultyId); // Always send facultyid
    formData.append('materialid', materialId);
    formData.append('new_status', newStatus);

    try {
        const response = await fetch('/faculty_web/backend/faculty_control.php', {
            method: 'POST',
            body: formData
        });
        const data = await response.json();

        if (data.status === 'success') {
            showUIMessage(`Course material successfully ${newStatus === 1 ? 'archived' : 'unarchived'}.`, 'success');
            await fetchFacultyCourses(); // Re-fetch courses to update dropdown and button
            // After re-fetching, re-select the course to update its associated buttons
            const selectedAssignmentId = document.getElementById('courseName').value;
            const selectedCourse = coursesData.find(c => c.assignmentid == selectedAssignmentId);
            if (selectedCourse) {
                // Ensure the dropdown shows the correct course name with (Archived) tag
                // No need to re-show lectures here unless the user wants lectures to disappear/reappear based on archive status
                // For now, just update the button.
                updateArchiveButtonState(selectedCourse.materialid, selectedCourse.Is_archived);
            } else {
                // If no course is selected or selected course disappears (e.g. if archived items are filtered out)
                document.getElementById('archiveCourseBtn').classList.add('hidden');
            }
        } else {
            showUIMessage(`Error toggling archive status: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('toggleArchiveCourse: Network error during archive toggle:', error);
        showUIMessage(`Network error during archive toggle: ${error.message}`, 'error');
    }
}

/**
 * Helper function to update the text and visibility of the archive button.
 * @param {number} materialId The ID of the material.
 * @param {boolean} isArchived The current archive status of the material.
 */
function updateArchiveButtonState(materialId, isArchived) {
    const archiveBtn = document.getElementById('archiveCourseBtn');
    // Important: check if the button exists before trying to manipulate it
    if (!archiveBtn) {
        console.error('Archive button element not found! Please ensure it exists in your HTML with id="archiveCourseBtn".');
        return;
    }
    // Ensure materialId is explicitly converted to string for dataset
    archiveBtn.dataset.materialId = String(materialId); 
    archiveBtn.dataset.isArchived = isArchived ? '1' : '0'; // Set archived status on the button

    if (isArchived) {
        archiveBtn.textContent = 'Unarchive Course';
        archiveBtn.classList.remove('primary-btn');
        archiveBtn.classList.add('secondary-btn');
    } else {
        archiveBtn.textContent = 'Archive Course';
        archiveBtn.classList.remove('secondary-btn');
        archiveBtn.classList.add('primary-btn');
    }
    archiveBtn.classList.remove('hidden'); // Ensure button is visible
}


/**
 * NEW: Fetches materials (courses) for the faculty's department and populates the dropdown.
 */
async function fetchDepartmentMaterialsAndPopulateDropdown() {
    console.log('fetchDepartmentMaterialsAndPopulateDropdown: Fetching materials for department:', facultyData.departmentid);
    const newCourseMaterialSelect = document.getElementById('newCourseMaterialSelect');
    newCourseMaterialSelect.innerHTML = '<option value="" disabled selected>Select Course Material</option>'; // Clear and add default

    if (!facultyData.departmentid) {
        console.warn('fetchDepartmentMaterialsAndPopulateDropdown: No department ID available for faculty.');
        showUIMessage('Cannot load course materials: Faculty department not set.', 'error');
        return;
    }

    try {
        // This query now explicitly filters for non-archived materials
        const url = `/faculty_web/backend/faculty_control.php?action=get_department_materials&departmentid=${facultyData.departmentid}`;
        const response = await fetch(url);
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`fetchDepartmentMaterialsAndPopulateDropdown: HTTP error! Status: ${response.status}, Response: ${errorText}`);
            throw new Error(`Server responded with status ${response.status}`);
        }
        const data = await response.json();
        console.log('fetchDepartmentMaterialsAndPopulateDropdown: Received materials data:', data);

        if (data.status === 'success' && Array.isArray(data.data)) {
            data.data.forEach(material => {
                const option = document.createElement('option');
                option.value = material.materialid;
                option.textContent = material.materialname;
                newCourseMaterialSelect.appendChild(option);
            });
            showUIMessage('Course materials loaded.', 'info');
        } else {
            console.error('fetchDepartmentMaterialsAndPopulateDropdown: Error loading materials:', data.message);
            showUIMessage(`Error loading course materials: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('fetchDepartmentMaterialsAndPopulateDropdown: Network error during materials fetch:', error);
        showUIMessage(`Network error loading course materials: ${error.message}`, 'error');
    }
}

/**
 * دالة لعرض محاضرات الدورة المحددة.
 * @param {number} assignmentId - معرف التخصيص للمادة (الدورة).
 * @param {string} courseName - اسم الدورة لعرضها في العنوان.
 */
async function showCourseLectures(assignmentId, courseName) {
    console.log(`showCourseLectures: Showing lectures for Assignment ID: ${assignmentId}, Course: ${courseName}`);
    document.getElementById('selectedCourseTitle').textContent = `Lectures for: ${courseName}`;
    document.getElementById('courseLectures').classList.remove('hidden');
    document.getElementById('showAddLectureFormBtn').classList.remove('hidden'); // Show add lecture button
    document.getElementById('deleteSelectedCourseBtn').classList.remove('hidden'); // Show delete course button
    
    // Find the selected course in coursesData to get its materialId and archive status
    const selectedCourse = coursesData.find(c => c.assignmentid == assignmentId);
    
    // Explicitly check for valid materialId before updating the archive button
    if (selectedCourse && selectedCourse.materialid !== null) { // Check for non-null materialid after parsing
        updateArchiveButtonState(selectedCourse.materialid, selectedCourse.Is_archived);
    } else {
        console.warn('showCourseLectures: Selected course or its materialId is missing/invalid. Assignment ID:', assignmentId, 'Selected Course:', selectedCourse);
        const archiveBtn = document.getElementById('archiveCourseBtn');
        if (archiveBtn) { archiveBtn.classList.add('hidden'); } // Hide if course or materialId is missing
        showUIMessage('Error: Material ID not found for archiving.', 'error'); // Keep the user's reported error message
    }

    const lecturesList = document.getElementById('lecturesList');
    lecturesList.innerHTML = '<p>Loading lectures...</p>'; // Loading message

    try {
        const url = `/faculty_web/backend/faculty_control.php?action=get_lectures_by_assignment&assignmentid=${assignmentId}`;
        const response = await fetch(url);
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`showCourseLectures: HTTP error! Status: ${response.status}, Response: ${errorText}`);
            throw new Error(`Server responded with status ${response.status}`);
        }
        const data = await response.json();
        console.log('showCourseLectures: Received lectures data:', data);

        if (data.status === 'success' && Array.isArray(data.data)) {
            lecturesList.innerHTML = ''; // Clear loading message

            if (data.data.length === 0) {
                lecturesList.innerHTML = '<p>No lectures available for this course yet.</p>';
            } else {
                data.data.forEach(lecture => {
                    const uploadDate = new Date(lecture.uploaddate);
                    // Format date as d/m/y
                    const formattedDate = `${uploadDate.getDate()}/${uploadDate.getMonth() + 1}/${uploadDate.getFullYear()}`;

                    const lectureDiv = document.createElement('div');
                    lectureDiv.className = 'lecture-item';
                    lectureDiv.innerHTML = `
                        <span>${lecture.title} (${formattedDate})</span>
                        <div class="lecture-actions"> <!-- ADDED lecture-actions class here -->
                            <button class="secondary-btn download-lecture-btn" data-lecture-id="${lecture.lectureid}" data-lecture-title="${lecture.title}">Download</button>
                            <button class="primary-btn edit-lecture-btn" data-lecture-id="${lecture.lectureid}" data-lecture-title="${lecture.title}" data-file-base64="${lecture.file || ''}">Edit</button>
                            <button class="delete-btn delete-lecture-btn" data-lecture-id="${lecture.lectureid}">Delete</button>
                        </div>
                    `;
                    lecturesList.appendChild(lectureDiv);
                });

                // Attach event listeners to new buttons
                lecturesList.querySelectorAll('.download-lecture-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const lectureId = this.dataset.lectureId;
                        const lectureTitle = this.dataset.lectureTitle;
                        downloadLectureFile(lectureId, lectureTitle);
                    });
                });
                lecturesList.querySelectorAll('.edit-lecture-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        currentEditLectureId = this.dataset.lectureId;
                        const title = this.dataset.lectureTitle;
                        // const fileBase64 = this.dataset.fileBase64; // Don't use this directly, fetch from backend if needed
                        editLecture(currentEditLectureId, title);
                    });
                });
                lecturesList.querySelectorAll('.delete-lecture-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const lectureId = this.dataset.lectureId;
                        showCustomConfirm('Are you sure you want to delete this lecture?', (confirm) => {
                            if (confirm) deleteLecture(lectureId);
                        });
                    });
                });
            }
            showUIMessage('Lectures loaded successfully!', 'success');
        } else {
            lecturesList.innerHTML = '<p>Error loading lectures.</p>';
            console.error('showCourseLectures: Error from server:', data.message);
            showUIMessage(`Error loading lectures: ${data.message}`, 'error');
        }
    } catch (error) {
        lecturesList.innerHTML = '<p>Error loading lectures.</p>';
        console.error('showCourseLectures: Network error during lectures fetch:', error);
        showUIMessage(`Network error loading lectures: ${error.message}`, 'error');
    }
}

/**
 * Downloads a lecture file by fetching its Base64 content and creating a download link.
 * @param {string} lectureId The ID of the lecture to download.
 * @param {string} lectureTitle The title of the lecture for the filename.
 */
async function downloadLectureFile(lectureId, lectureTitle) {
    console.log(`downloadLectureFile: Attempting to download lecture ID: ${lectureId}`);
    try {
        const url = `/faculty_web/backend/faculty_control.php?action=get_lectures_by_assignment&assignmentid=${document.getElementById('courseName').value}`; // Re-fetch the specific lecture's data
        const response = await fetch(url);
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`downloadLectureFile: HTTP error! Status: ${response.status}, Response: ${errorText}`);
            throw new Error(`Server responded with status ${response.status}`);
        }
        const data = await response.json();
        
        if (data.status === 'success' && Array.isArray(data.data)) {
            const lecture = data.data.find(l => l.lectureid == lectureId); // Find the specific lecture
            if (lecture && lecture.file) {
                // Determine file type (simple heuristic, can be improved)
                const fileType = lecture.file.startsWith('JVBERi0') ? 'pdf' : (lecture.file.startsWith('UEsDBBQ') ? 'docx' : 'bin'); // PDF starts with %PDF (hex 25504446), DOCX is a ZIP file
                const mimeType = fileType === 'pdf' ? 'application/pdf' : (fileType === 'docx' ? 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' : 'application/octet-stream');
                
                const link = document.createElement('a');
                link.href = `data:${mimeType};base64,${lecture.file}`;
                link.download = `${lectureTitle}.${fileType}`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                showUIMessage('Lecture downloaded successfully!', 'success');
            } else {
                showUIMessage('Lecture file not found or empty.', 'error');
                console.error('downloadLectureFile: Lecture file data missing or invalid.');
            }
        } else {
            showUIMessage(`Error fetching lecture for download: ${data.message}`, 'error');
            console.error('downloadLectureFile: Server error or no data:', data.message);
        }
    } catch (error) {
        console.error('downloadLectureFile: Network error during lecture download:', error);
        showUIMessage(`Network error during lecture download: ${error.message}`, 'error');
    }
}


/**
 * دالة لتحرير محاضرة موجودة. تملأ النموذج ببيانات المحاضرة.
 * @param {number} lectureId - معرف المحاضرة المراد تحريرها.
 * @param {string} title - العنوان الحالي للمحاضرة.
 */
function editLecture(lectureId, title) {
    console.log(`editLecture: Editing lecture ID: ${lectureId}, Title: ${title}`);
    currentEditLectureId = lectureId; // Store the ID of the lecture being edited

    const editForm = document.getElementById('editLectureForm');
    document.getElementById('editLectureTitle').value = title;
    document.getElementById('editLectureFileInput').value = ''; // Clear previous file selection
    document.getElementById('editFileSelectedName').textContent = 'No new file chosen. Existing file will be used.'; // Reset file name display

    editForm.classList.remove('hidden'); // Show the edit form
    document.getElementById('addLectureForm').classList.add('hidden'); // Hide add form
    document.getElementById('showAddLectureFormBtn').classList.add('hidden'); // Hide "Add New Lecture" button

    // Show the file input container for edit lecture
    document.querySelector('#editLectureForm .file-input-container').classList.remove('hidden');
}

/**
 * دالة لحفظ تغييرات المحاضرة (التحديث).
 */
async function updateLectureData() {
    console.log('updateLectureData: Attempting to update lecture.');
    const lectureId = currentEditLectureId;
    const title = document.getElementById('editLectureTitle').value.trim();
    const fileInput = document.getElementById('editLectureFileInput');
    const newFile = fileInput.files[0];

    if (!lectureId || !title) {
        showUIMessage('Lecture ID and title are required.', 'error');
        return;
    }

    let fileBase64 = null;
    if (newFile) {
        if (newFile.size > 20 * 1024 * 1024) { // 20MB limit for lecture files
            showUIMessage('Lecture file size must be less than 20MB.', 'error');
            return;
        }
        const reader = new FileReader();
        fileBase64 = await new Promise((resolve, reject) => {
            reader.onload = () => resolve(reader.result.split(',')[1]);
            reader.onerror = reject;
            reader.readAsDataURL(newFile);
        });
        console.log('updateLectureData: New file selected and converted to Base64.');
    }

    const formData = new FormData();
    formData.append('action', 'update_lecture');
    formData.append('facultyid', facultyId); // إضافة facultyid
    formData.append('lectureid', lectureId);
    formData.append('title', title);
    if (fileBase64) {
        formData.append('file', fileBase64);
    }
    console.log('updateLectureData: FormData prepared.');

    try {
        const response = await fetch('/faculty_web/backend/faculty_control.php', {
            method: 'POST',
            body: formData
        });
        const data = await response.json();

        if (data.status === 'success') {
            showUIMessage('Lecture updated successfully!', 'success');
            document.getElementById('editLectureForm').classList.add('hidden'); // Hide edit form
            document.getElementById('showAddLectureFormBtn').classList.remove('hidden'); // Show add lecture button
            // Hide the file input container for edit lecture after saving
            document.querySelector('#editLectureForm .file-input-container').classList.add('hidden');
            // Re-fetch lectures for the current course to update the list
            const currentCourseId = document.getElementById('courseName').value;
            const selectedCourse = coursesData.find(c => c.assignmentid == currentCourseId);
            if (selectedCourse) {
                showCourseLectures(selectedCourse.assignmentid, selectedCourse.materialname);
            }
        } else {
            showUIMessage(`Error updating lecture: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('updateLectureData: Network error during lecture update:', error);
        showUIMessage(`Network error during lecture update: ${error.message}`, 'error');
    }
}

/**
 * دالة لإلغاء تحرير المحاضرة.
 */
function cancelEditLecture() {
    console.log('cancelEditLecture: Cancelling lecture editing.');
    currentEditLectureId = null;
    document.getElementById('editLectureTitle').value = '';
    document.getElementById('editLectureFileInput').value = '';
    document.getElementById('editFileSelectedName').textContent = 'No new file chosen. Existing file will be used.';

    document.getElementById('editLectureForm').classList.add('hidden'); // Hide edit form
    document.getElementById('showAddLectureFormBtn').classList.remove('hidden'); // Show add lecture button
    // Hide the file input container for edit lecture after canceling
    document.querySelector('#editLectureForm .file-input-container').classList.add('hidden');
    showUIMessage('Lecture editing canceled.', 'info');
}

/**
 * دالة لحذف محاضرة.
 * @param {number} lectureId - معرف المحاضرة المراد حذفها.
 */
async function deleteLecture(lectureId) {
    console.log(`deleteLecture: Attempting to delete lecture ID: ${lectureId}`);
    const formData = new FormData();
    formData.append('action', 'delete_lecture');
    formData.append('facultyid', facultyId); // إضافة facultyid
    formData.append('lectureid', lectureId);

    try {
        const response = await fetch('/faculty_web/backend/faculty_control.php', {
            method: 'POST',
            body: formData
        });
        const data = await response.json();

        if (data.status === 'success') {
            showUIMessage('Lecture deleted successfully!', 'success');
            // Re-fetch lectures for the current course to update the list
            const currentCourseId = document.getElementById('courseName').value;
            const selectedCourse = coursesData.find(c => c.assignmentid == currentCourseId);
            if (selectedCourse) {
                showCourseLectures(selectedCourse.assignmentid, selectedCourse.materialname);
            }
        } else {
            showUIMessage(`Error deleting lecture: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('deleteLecture: Network error during lecture deletion:', error);
        showUIMessage(`Network error during lecture deletion: ${error.message}`, 'error');
    }
}


/**
 * دالة لحذف الدورة المحددة وجميع محاضراتها.
 */
async function deleteSelectedCourse() {
    console.log('deleteSelectedCourse: Attempting to delete selected course.');
    const selectedAssignmentId = document.getElementById('courseName').value;
    const selectedCourseOption = document.getElementById('courseName').querySelector(`option[value="${selectedAssignmentId}"]`);
    const courseName = selectedCourseOption ? selectedCourseOption.textContent : 'Selected Course';

    if (!selectedAssignmentId) {
        showUIMessage('Please select a course to delete.', 'info');
        return;
    }

    showCustomConfirm(`Are you sure you want to delete the course "${courseName}" and all its lectures? This action cannot be undone.`, async (confirm) => {
        if (confirm) {
            const formData = new FormData();
            formData.append('action', 'delete_course_assignment');
            formData.append('facultyid', facultyId); // إضافة facultyid
            formData.append('assignmentid', selectedAssignmentId);

            try {
                const response = await fetch('/faculty_web/backend/faculty_control.php', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();

                if (data.status === 'success') {
                    showUIMessage('Course and all lectures deleted successfully!', 'success');
                    document.getElementById('courseLectures').classList.add('hidden'); // Hide lectures section
                    document.getElementById('deleteSelectedCourseBtn').classList.add('hidden'); // Hide delete course button
                    const archiveBtn = document.getElementById('archiveCourseBtn');
                    if (archiveBtn) { archiveBtn.classList.add('hidden'); } // Hide archive button as well
                    await fetchFacultyCourses(); // Refresh the course dropdown
                } else {
                    showUIMessage(`Error deleting course: ${data.message}`, 'error');
                }
            } catch (error) {
                console.error('deleteSelectedCourse: Network error during course deletion:', error);
                showUIMessage(`Network error during course deletion: ${error.message}`, 'error');
            }
        } else {
            showUIMessage('Course deletion canceled.', 'info');
        }
    });
}


// =====================================================================
// وظائف قسم البحث الأكاديمي (Academic Research)
// =====================================================================

/**
 * دالة لجلب الأبحاث الأكاديمية للأستاذ من الخادم وعرضها.
 */
async function fetchFacultyResearch() {
    console.log('fetchFacultyResearch: Attempting to fetch faculty research...');
    try {
        const url = `/faculty_web/backend/faculty_control.php?action=get_faculty_research&facultyid=${facultyId}`;
        const response = await fetch(url);
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`fetchFacultyResearch: HTTP error! Status: ${response.status}, Response: ${errorText}`);
            throw new Error(`Server responded with status ${response.status}`);
        }
        const data = await response.json();
        console.log('fetchFacultyResearch: Received research data:', data);

        const researchTableBody = document.getElementById('researchTableBody');
        researchTableBody.innerHTML = ''; // Clear existing research

        if (data.status === 'success' && Array.isArray(data.data)) {
            researches = data.data; // Store fetched research globally
            if (researches.length === 0) {
                researchTableBody.innerHTML = '<tr><td colspan="4">No research entries found.</td></tr>';
            } else {
                researches.forEach(research => {
                    const row = researchTableBody.insertRow();
                    row.dataset.researchId = research.researchid; // Store research ID on the row
                    const publicationDate = new Date(research.Date_published);
                    const formattedDate = `${publicationDate.getDate()}/${publicationDate.getMonth() + 1}/${publicationDate.getFullYear()}`;
                    row.innerHTML = `
                        <td>${research.title}</td>
                        <td>${formattedDate}</td>
                        <td>
                            ${research.file ? `<button class="secondary-btn download-research-btn" data-research-id="${research.researchid}" data-research-title="${research.title}">Download File</button>` : 'No file'}
                        </td>
                        <td>
                            <div class="research-actions">
                                <button class="primary-btn edit-research-btn" data-research-id="${research.researchid}">Edit</button>
                                <button class="delete-btn delete-research-btn" data-research-id="${research.researchid}">Delete</button>
                            </div>
                        </td>
                    `;
                });

                // Attach event listeners to new buttons
                researchTableBody.querySelectorAll('.download-research-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const researchId = this.dataset.researchId;
                        const researchTitle = this.dataset.researchTitle;
                        downloadResearchFile(researchId, researchTitle);
                    });
                });
                researchTableBody.querySelectorAll('.edit-research-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const researchId = this.dataset.researchId;
                        editResearch(researchId);
                    });
                });
                researchTableBody.querySelectorAll('.delete-research-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const researchId = this.dataset.researchId;
                        showCustomConfirm('Are you sure you want to delete this research entry?', (confirm) => {
                            if (confirm) deleteResearch(researchId);
                        });
                    });
                });
            }
            showUIMessage('Academic research loaded successfully!', 'success');
        } else {
            researchTableBody.innerHTML = '<tr><td colspan="4">Error loading research data.</td></tr>';
            console.error('fetchFacultyResearch: Error from server:', data.message);
            showUIMessage(`Error loading research: ${data.message}`, 'error');
        }
    } catch (error) {
        researchTableBody.innerHTML = '<tr><td colspan="4">Error loading research data.</td></tr>';
        console.error('fetchFacultyResearch: Network error during research fetch:', error);
        showUIMessage(`Network error loading research: ${error.message}`, 'error');
    }
}

/**
 * Downloads a research file by fetching its Base64 content and creating a download link.
 * @param {string} researchId The ID of the research to download.
 * @param {string} researchTitle The title of the research for the filename.
 */
async function downloadResearchFile(researchId, researchTitle) {
    console.log(`downloadResearchFile: Attempting to download research ID: ${researchId}`);
    try {
        // Find the research entry in the local 'researches' array
        const research = researches.find(r => r.researchid == researchId);

        if (research && research.file) {
            // Determine file type (simple heuristic, can be improved)
            const fileType = research.file.startsWith('JVBERi0') ? 'pdf' : (research.file.startsWith('UEsDBBQ') ? 'docx' : 'bin');
            const mimeType = fileType === 'pdf' ? 'application/pdf' : (fileType === 'docx' ? 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' : 'application/octet-stream');
            
            const link = document.createElement('a');
            link.href = `data:${mimeType};base64,${research.file}`;
            link.download = `${researchTitle}.${fileType}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            showUIMessage('Research file downloaded successfully!', 'success');
        } else {
            showUIMessage('Research file not found or empty.', 'error');
            console.error('downloadResearchFile: Research file data missing or invalid for ID:', researchId);
        }
    } catch (error) {
        console.error('downloadResearchFile: Error during research file download:', error);
        showUIMessage(`Error during research file download: ${error.message}`, 'error');
    }
}

/**
 * دالة لتحرير بحث أكاديمي موجود.
 * @param {number} researchId - معرف البحث المراد تحريره.
 */
function editResearch(researchId) {
    console.log(`editResearch: Editing research ID: ${researchId}`);
    currentEditResearchId = researchId; // Store the ID of the research being edited

    const researchToEdit = researches.find(r => r.researchid == researchId);
    if (researchToEdit) {
        document.getElementById('editResearchTitle').value = researchToEdit.title;
        document.getElementById('editResearchDate').value = researchToEdit.Date_published; // Date input handles ISO-MM-DD
        document.getElementById('editResearchFileInput').value = ''; // Clear file input
        document.getElementById('editResearchFileSelectedName').textContent = 'No new file chosen. Existing file will be used.'; // Reset display name

        document.getElementById('editResearchForm').classList.remove('hidden'); // Show edit form
        document.getElementById('newResearchForm').classList.add('hidden'); // Hide new research form
        document.getElementById('toggleResearchFormBtn').classList.add('hidden'); // Hide add new research button
        // Show the file input container for edit research
        document.querySelector('#editResearchForm .file-input-container').classList.remove('hidden');
    } else {
        showUIMessage('Research not found for editing.', 'error');
        console.error('editResearch: Research not found in local data for ID:', researchId);
    }
}

/**
 * دالة لحفظ تغييرات البحث (التحديث).
 */
async function updateResearchData() {
    console.log('updateResearchData: Attempting to update research.');
    const researchId = currentEditResearchId;
    const title = document.getElementById('editResearchTitle').value.trim();
    const datePublished = document.getElementById('editResearchDate').value; // ISO-MM-DD
    const fileInput = document.getElementById('editResearchFileInput');
    const newFile = fileInput.files[0];

    if (!researchId || !title || !datePublished) {
        showUIMessage('Research ID, title, and publication date are required.', 'error');
        return;
    }

    let fileBase64 = null;
    if (newFile) {
        if (newFile.size > 20 * 1024 * 1024) { // 20MB limit for research files
            showUIMessage('Research file size must be less than 20MB.', 'error');
            return;
        }
        const reader = new FileReader();
        fileBase64 = await new Promise((resolve, reject) => {
            reader.onload = () => resolve(reader.result.split(',')[1]);
            reader.onerror = reject;
            reader.readAsDataURL(newFile);
        });
        console.log('updateResearchData: New file selected and converted to Base64.');
    }

    const formData = new FormData();
    formData.append('action', 'update_research');
    formData.append('facultyid', facultyId); // إضافة facultyid
    formData.append('researchid', researchId);
    formData.append('title', title);
    formData.append('datePublished', datePublished);
    if (fileBase64) {
        formData.append('file', fileBase64);
    }
    console.log('updateResearchData: FormData prepared.');

    try {
        const response = await fetch('/faculty_web/backend/faculty_control.php', {
            method: 'POST',
            body: formData
        });
        const data = await response.json();
        console.log('updateResearchData: Backend response:', data);

        if (data.status === 'success') {
            showUIMessage('Research updated successfully!', 'success');
            document.getElementById('editResearchForm').classList.add('hidden'); // Hide edit form
            document.getElementById('toggleResearchFormBtn').classList.remove('hidden'); // Show add new research button
            // Hide the file input container for edit research after saving
            document.querySelector('#editResearchForm .file-input-container').classList.add('hidden');
            await fetchFacultyResearch(); // Re-fetch and display research
        } else {
            showUIMessage(`Error updating research: ${data.message}`, 'error');
        }
    }
    catch (error) {
        console.error('updateResearchData: Network error during research update:', error);
        showUIMessage(`Network error during research update: ${error.message}`, 'error');
    }
}

/**
 * دالة لإلغاء تحرير البحث.
 */
function cancelEditResearch() {
    console.log('cancelEditResearch: Cancelling research editing.');
    currentEditResearchId = null;
    document.getElementById('editResearchTitle').value = '';
    document.getElementById('editResearchDate').value = '';
    document.getElementById('editResearchFileInput').value = '';
    document.getElementById('editResearchFileSelectedName').textContent = '';

    document.getElementById('editResearchForm').classList.add('hidden'); // Hide edit form
    document.getElementById('newResearchForm').classList.add('hidden'); // Ensure add form is hidden
    document.getElementById('toggleResearchFormBtn').classList.remove('hidden'); // Show add new research button
    // Hide the file input container for edit research after canceling
    document.querySelector('#editResearchForm .file-input-container').classList.add('hidden');
    showUIMessage('Research editing canceled.', 'info');
}

/**
 * دالة لحذف بحث أكاديمي.
 * @param {number} researchId - معرف البحث المراد حذفه.
 */
async function deleteResearch(researchId) {
    console.log(`deleteResearch: Attempting to delete research ID: ${researchId}`);
    const formData = new FormData();
    formData.append('action', 'delete_research');
    formData.append('facultyid', facultyId); // إضافة facultyid
    formData.append('researchid', researchId);

    try {
        const response = await fetch('/faculty_web/backend/faculty_control.php', {
            method: 'POST',
            body: formData
        });
        const data = await response.json();

        if (data.status === 'success') {
            showUIMessage('Research deleted successfully!', 'success');
            await fetchFacultyResearch(); // Re-fetch and display research
        } else {
            showUIMessage(`Error deleting research: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('deleteResearch: Network error during research deletion:', error);
        showUIMessage(`Network error during research deletion: ${error.message}`, 'error');
    }
}

/**
 * دالة لإضافة بحث جديد.
 */
async function addNewResearch() {
    console.log('addNewResearch: Attempting to add new research.');
    const title = document.getElementById('newResearchTitle').value.trim();
    const datePublished = document.getElementById('newResearchDate').value;
    const fileInput = document.getElementById('newResearchFileInput');
    const file = fileInput.files[0];

    if (!title || !datePublished || !file) {
        showUIMessage('All fields (Title, Publication Date, File) are required for new research.', 'error');
        return;
    }

    if (file.size > 20 * 1024 * 1024) { // 20MB limit for research files
        showUIMessage('Research file size must be less than 20MB.', 'error');
        return;
    }

    // Convert file to Base64
    const reader = new FileReader();
    const fileBase64 = await new Promise((resolve, reject) => {
        reader.onload = () => resolve(reader.result.split(',')[1]);
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
    console.log('addNewResearch: File converted to Base64.');

    const formData = new FormData();
    formData.append('facultyid', facultyId);
    formData.append('action', 'add_research');
    formData.append('title', title);
    formData.append('file', fileBase64);
    formData.append('datePublished', datePublished);
    console.log('addNewResearch: FormData prepared.');

    try {
        const response = await fetch('/faculty_web/backend/faculty_control.php', {
            method: 'POST',
            body: formData
        });
        const data = await response.json();
        console.log('addNewResearch: Backend response:', data);

        if (data.status === 'success') {
            showUIMessage('Research added successfully!', 'success');
            // Clear form and hide
            document.getElementById('newResearchTitle').value = '';
            document.getElementById('newResearchDate').value = '';
            document.getElementById('newResearchFileInput').value = '';
            document.getElementById('researchFileSelectedName').textContent = '';
            currentResearchFileObject = null;
            document.getElementById('newResearchForm').classList.add('hidden');
            document.getElementById('toggleResearchFormBtn').classList.remove('hidden'); // Show add new research button
            // Hide the file input container for new research after saving
            document.querySelector('#newResearchForm .file-input-container').classList.add('hidden');
            await fetchFacultyResearch(); // Re-fetch and display research
        } else {
            showUIMessage(`Error adding research: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('addNewResearch: Network error during research addition:', error);
        showUIMessage(`Network error during research addition: ${error.message}`, 'error');
    }
}


// =====================================================================
// Event Listeners (جميع مستمعي الأحداث)
// =====================================================================

document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOMContentLoaded: Page loaded. Initializing...');
    setupSidebarNavigation(); // إعداد التنقل في الشريط الجانبي
    showContentSection('personal-info'); // عرض قسم المعلومات الشخصية افتراضيًا
    
    await fetchDepartments(); // جلب الأقسام أولاً
    await fetchFacultyInfo(); // ثم جلب معلومات الأستاذ
    await fetchFacultyCourses(); // جلب المواد الدراسية للأستاذ
    await fetchFacultyResearch(); // جلب الأبحاث الأكاديمية للأستاذ

    // Personal Information Section Listeners
    document.getElementById('editInfoBtn').addEventListener('click', enableEditing);
    document.getElementById('saveInfoBtn').addEventListener('click', saveProfileData);
    document.getElementById('cancelInfoBtn').addEventListener('click', cancelEditing);

    document.getElementById('changePhotoBtn').addEventListener('click', () => {
        document.getElementById('profileImageInput').click(); // Trigger hidden file input click
    });
    document.getElementById('profileImageInput').addEventListener('change', updateImage);
    document.getElementById('saveProfileImageBtn').addEventListener('click', saveProfileImage);
    document.getElementById('cancelProfileImageBtn').addEventListener('click', () => cancelImageChange(true)); // Pass true to show message

    // Doctor Description Section Listeners
    document.getElementById('editDescriptionBtn').addEventListener('click', enableDescriptionEditing);
    document.getElementById('saveDescriptionBtn').addEventListener('click', saveDescriptionData);
    document.getElementById('cancelDescriptionBtn').addEventListener('click', cancelDescriptionEditing);

    // Certificate Modal Listeners
    document.getElementById('viewCertificateBtn').addEventListener('click', () => {
        console.log('View Certificate button clicked.'); // Added log
        // The modal should be hidden by default with CSS, then shown by JS
        document.getElementById('certificateModal').classList.remove('hidden');
        document.getElementById('certificateModal').classList.add('active'); // Show modal
    });
    document.getElementById('closeModalBtn').addEventListener('click', () => {
        console.log('Close Modal button clicked.'); // Added log
        document.getElementById('certificateModal').classList.remove('active'); // Hide modal
        document.getElementById('certificateModal').classList.add('hidden'); // Ensure it's fully hidden
    });
    document.getElementById('closeCertificateModalBtn').addEventListener('click', () => {
        console.log('Close Certificate Modal button clicked.'); // Added log
        document.getElementById('certificateModal').classList.remove('active'); // Hide modal
        document.getElementById('certificateModal').classList.add('hidden'); // Ensure it's fully hidden
    });
    // This listener handles the actual file selection for the certificate.
    document.getElementById('certificateImageInput').addEventListener('change', function() {
        console.log('certificateImageInput change event triggered.'); // Added log
        if (this.files.length > 0) {
            const file = this.files[0];
            if (file.size > 5 * 1024 * 1024) { // 5MB limit for certificate images
                showUIMessage('Certificate image size must be less than 5MB.', 'error');
                this.value = ''; // Clear the input
                currentCertificateFile = null;
                return;
            }
            currentCertificateFile = file;
            showUIMessage('Certificate image selected. Click "Save Changes" to upload.', 'info');
            console.log('Certificate file selected:', file.name); // Added log
            // No direct preview for certificate, only upload on Save Changes
        } else {
            currentCertificateFile = null;
            showUIMessage('No certificate image selected.', 'info');
            console.log('No certificate file selected.'); // Added log
        }
    });
    // The "Upload Image" button just acts as a visual trigger for the hidden input
    document.getElementById('uploadCertificateBtn').addEventListener('click', () => {
        console.log('Upload Certificate button clicked. Triggering hidden input.'); // Added log
        document.getElementById('certificateImageInput').click();
    });

    // Course Materials Section Listeners
    document.getElementById('courseName').addEventListener('change', function() {
        const selectedAssignmentId = this.value;
        const selectedCourse = coursesData.find(c => c.assignmentid == selectedAssignmentId);
        if (selectedCourse) {
            showCourseLectures(selectedCourse.assignmentid, selectedCourse.materialname);
        } else {
            // Hide everything if no course is selected
            document.getElementById('courseLectures').classList.add('hidden');
            document.getElementById('deleteSelectedCourseBtn').classList.add('hidden');
            const archiveBtn = document.getElementById('archiveCourseBtn');
            if (archiveBtn) { archiveBtn.classList.add('hidden'); } // Hide archive button as well
        }
        // Ensure forms are hidden when a course is selected/changed
        document.getElementById('addLectureForm').classList.add('hidden');
        document.getElementById('editLectureForm').classList.add('hidden');
        document.getElementById('showAddLectureFormBtn').classList.remove('hidden');
        // Ensure file input containers are hidden when a course is selected/changed
        document.querySelector('#addLectureForm .file-input-container').classList.add('hidden');
        document.querySelector('#editLectureForm .file-input-container').classList.add('hidden');
    });

    // MODIFIED: Show Add Course Form Button
    document.getElementById('showAddCourseFormBtn').addEventListener('click', function() {
        document.getElementById('addCourseForm').classList.remove('hidden');
        this.classList.add('hidden'); // Hide "Add New Course" button
        fetchDepartmentMaterialsAndPopulateDropdown(); // Populate the new dropdown
    });
    
    // MODIFIED: Add Course Button
    document.getElementById('addCourseBtn').addEventListener('click', async function() {
        const materialId = document.getElementById('newCourseMaterialSelect').value; // Get materialId from dropdown
        const semester = document.getElementById('newCourseSemester').value.trim();

        if (!materialId || semester === "") {
            showUIMessage('Please select a course material and enter a semester.', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('facultyid', facultyId);
    // REMOVED: addCourseAssignment action for add_course
        formData.append('action', 'add_course_assignment');
        formData.append('materialid', materialId); // Send materialid
        formData.append('semester', semester);

        try {
            const response = await fetch('/faculty_web/backend/faculty_control.php', {
                method: 'POST',
                body: formData
            });
            const data = await response.json();

            if (data.status === 'success') {
                showUIMessage('Course added successfully!', 'success');
                document.getElementById('newCourseMaterialSelect').value = ''; // Reset dropdown
                document.getElementById('newCourseSemester').value = ''; // Clear semester field
                document.getElementById('addCourseForm').classList.add('hidden'); // Hide form
                document.getElementById('showAddCourseFormBtn').classList.remove('hidden'); // Show "Add New Course" button
                await fetchFacultyCourses(); // Re-fetch and display courses
            } else {
                showUIMessage(`Error adding course: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Network error during course addition:', error);
            showUIMessage(`Network error during course addition: ${error.message}`, 'error');
        }
    });

    // MODIFIED: Cancel Add Course Button
    document.getElementById('cancelAddCourseBtn').addEventListener('click', function() {
        document.getElementById('newCourseMaterialSelect').value = ''; // Reset dropdown
        document.getElementById('newCourseSemester').value = ''; // Clear semester field
        document.getElementById('addCourseForm').classList.add('hidden'); // Hide form
        document.getElementById('showAddCourseFormBtn').classList.remove('hidden'); // Show "Add New Course" button
        showUIMessage('Add course canceled.', 'info');
    });

    document.getElementById('showAddLectureFormBtn').addEventListener('click', function() {
        document.getElementById('addLectureForm').classList.remove('hidden');
        this.classList.add('hidden'); // Hide "Add New Lecture" button
        document.getElementById('editLectureForm').classList.add('hidden'); // Hide edit lecture form
        // Show the file input container for add lecture
        document.querySelector('#addLectureForm .file-input-container').classList.remove('hidden');
    });
    
    document.getElementById('cancelAddLectureBtn').addEventListener('click', function() {
        document.getElementById('addLectureForm').classList.add('hidden');
        document.getElementById('showAddLectureFormBtn').classList.remove('hidden');
        document.getElementById('lectureTitle').value = '';
        document.getElementById('lectureFileInput').value = '';
        document.getElementById('fileSelectedName').textContent = '';
        currentLectureFile = null;
        // Hide the file input container for add lecture after canceling
        document.querySelector('#addLectureForm .file-input-container').classList.add('hidden');
        showUIMessage('Add lecture canceled.', 'info');
    });

    document.getElementById('addLectureBtn').addEventListener('click', async function() {
        const assignmentId = document.getElementById('courseName').value; // Currently selected assignment
        const title = document.getElementById('lectureTitle').value.trim();

        if (!assignmentId || !title || !currentLectureFile) {
            showUIMessage('Please select a course, enter a title, and choose a file for the lecture.', 'error');
            return;
        }

        if (currentLectureFile.size > 20 * 1024 * 1024) { // 20MB limit for lecture files
            showUIMessage('Lecture file size must be less than 20MB.', 'error');
            return;
        }

        const reader = new FileReader();
        const fileBase64 = await new Promise((resolve, reject) => {
            reader.onload = () => resolve(reader.result.split(',')[1]);
            reader.onerror = reject;
            reader.readAsDataURL(currentLectureFile);
        });

        const formData = new FormData();
        formData.append('action', 'add_lecture');
        formData.append('facultyid', facultyId); // NEW: Always send facultyid
        formData.append('assignmentid', assignmentId);
        formData.append('title', title);
        formData.append('file', fileBase64);

        try {
            const response = await fetch('/faculty_web/backend/faculty_control.php', {
                method: 'POST',
                body: formData
            });
            const data = await response.json();

            if (data.status === 'success') {
                showUIMessage('Lecture added successfully!', 'success');
                document.getElementById('lectureTitle').value = '';
                document.getElementById('lectureFileInput').value = '';
                document.getElementById('fileSelectedName').textContent = '';
                currentLectureFile = null;
                document.getElementById('addLectureForm').classList.add('hidden');
                document.getElementById('showAddLectureFormBtn').classList.remove('hidden');
                // Hide the file input container for add lecture after saving
                document.querySelector('#addLectureForm .file-input-container').classList.add('hidden');
                // Re-fetch and display lectures for the current course
                const selectedCourse = coursesData.find(c => c.assignmentid == assignmentId);
                if (selectedCourse) {
                    showCourseLectures(selectedCourse.assignmentid, selectedCourse.materialname);
                }
            } else {
                showUIMessage(`Error adding lecture: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Network error during lecture addition:', error);
            showUIMessage(`Network error during lecture addition: ${error.message}`, 'error');
        }
    });

    // Lecture file input change listener
    document.getElementById('lectureFileInput').addEventListener('change', function() {
        if (this.files.length > 0) {
            currentLectureFile = this.files[0];
            document.getElementById('fileSelectedName').textContent = currentLectureFile.name;
        } else {
            currentLectureFile = null;
            document.getElementById('fileSelectedName').textContent = '';
        }
    });
    document.getElementById('chooseFileBtn').addEventListener('click', () => {
        document.getElementById('lectureFileInput').click();
    });

    // Edit Lecture form listeners
    document.getElementById('updateLectureBtn').addEventListener('click', updateLectureData);
    document.getElementById('cancelEditLectureBtn').addEventListener('click', cancelEditLecture);
    document.getElementById('editLectureFileInput').addEventListener('change', function() {
        if (this.files.length > 0) {
            document.getElementById('editFileSelectedName').textContent = this.files[0].name;
        } else {
            document.getElementById('editFileSelectedName').textContent = 'No new file chosen. Existing file will be used.';
        }
    });
    document.getElementById('chooseEditLectureFileBtn').addEventListener('click', () => {
        document.getElementById('editLectureFileInput').click();
    });

    // Delete Course button
    document.getElementById('deleteSelectedCourseBtn').addEventListener('click', deleteSelectedCourse);

    // NEW: Archive Course button listener
    document.getElementById('archiveCourseBtn').addEventListener('click', function() {
        console.log('Archive button clicked. dataset.materialId:', this.dataset.materialId, 'typeof:', typeof this.dataset.materialId); // Added debug log
        const materialId = parseInt(this.dataset.materialId);
        const isArchived = this.dataset.isArchived === '1'; // Convert string to boolean
        if (!isNaN(materialId)) {
            toggleArchiveCourse(materialId, isArchived);
        } else {
            showUIMessage('Error: Material ID not found for archiving.', 'error');
            console.error('Attempted to archive/unarchive with invalid materialId:', this.dataset.materialId); // Added debug log
        }
    });


    // Academic Research Section Listeners
    document.getElementById('toggleResearchFormBtn').addEventListener('click', function() {
        document.getElementById('newResearchForm').classList.remove('hidden');
        this.classList.add('hidden'); // Hide "Add New Research" button
        document.getElementById('editResearchForm').classList.add('hidden'); // Hide edit form
        // Show the file input container for new research
        document.querySelector('#newResearchForm .file-input-container').classList.remove('hidden');
    });

    document.getElementById('cancelNewResearchBtn').addEventListener('click', function() {
        document.getElementById('newResearchForm').classList.add('hidden');
        document.getElementById('toggleResearchFormBtn').classList.remove('hidden');
        document.getElementById('newResearchTitle').value = '';
        document.getElementById('newResearchDate').value = '';
        document.getElementById('newResearchFileInput').value = '';
        document.getElementById('researchFileSelectedName').textContent = '';
        currentResearchFileObject = null;
        // Hide the file input container for new research after canceling
        document.querySelector('#newResearchForm .file-input-container').classList.add('hidden');
        showUIMessage('Add research canceled.', 'info');
    });

    document.getElementById('submitNewResearchBtn').addEventListener('click', addNewResearch);

    document.getElementById('newResearchFileInput').addEventListener('change', function() {
        if (this.files.length > 0) {
            currentResearchFileObject = this.files[0];
            document.getElementById('researchFileSelectedName').textContent = currentResearchFileObject.name;
        } else {
            currentResearchFileObject = null;
            document.getElementById('researchFileSelectedName').textContent = '';
        }
    });
    document.getElementById('chooseResearchFileBtn').addEventListener('click', () => {
        document.getElementById('newResearchFileInput').click();
    });

    // Edit Research form listeners
    document.getElementById('updateResearchBtn').addEventListener('click', updateResearchData);
    document.getElementById('cancelEditResearchBtn').addEventListener('click', cancelEditResearch);
    document.getElementById('editResearchFileInput').addEventListener('change', function() {
        if (this.files.length > 0) {
            document.getElementById('editResearchFileSelectedName').textContent = this.files[0].name;
        } else {
            document.getElementById('editResearchFileSelectedName').textContent = 'No new file chosen. Existing file will be used.';
        }
    });
    document.getElementById('chooseEditResearchFileBtn').addEventListener('click', () => {
        document.getElementById('editResearchFileInput').click();
    });
});
