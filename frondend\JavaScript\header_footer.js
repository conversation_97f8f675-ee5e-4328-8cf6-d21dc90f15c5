function loadComponent(id, file) {
    fetch(file)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(data => {
            const componentContainer = document.getElementById(id);
            if (componentContainer) {
                componentContainer.innerHTML = data;
                // Recalculate and set heights after content is loaded
                // Add a small delay to ensure rendering is complete before measuring
                setTimeout(updateLayoutHeights, 50); // 50ms delay
            } else {
                console.error(`Container with ID '${id}' not found.`);
            }
        })
        .catch(error => console.error(`Error loading ${file}:`, error));
}

function updateLayoutHeights() {
    const headerElement = document.getElementById('header');
    const footerElement = document.getElementById('footer');
    
    let headerHeight = 0;
    // Check if header element exists and has content (firstElementChild)
    if (headerElement && headerElement.firstElementChild) {
        headerHeight = headerElement.firstElementChild.offsetHeight;
    }
    document.documentElement.style.setProperty('--header-height', `${headerHeight}px`);
    console.log('Updated --header-height to:', headerHeight);

    let footerHeight = 0;
    // Check if footer element exists and has content (firstElementChild)
    if (footerElement && footerElement.firstElementChild) {
        footerHeight = footerElement.firstElementChild.offsetHeight;
    }
    document.documentElement.style.setProperty('--footer-height', `${footerHeight}px`);
    console.log('Updated --footer-height to:', footerHeight);

    // The sidebar positioning (top and bottom) is now handled purely by CSS variables
    // in facultycontrol.css. No explicit JS style setting needed here.
}

// Initial load on DOMContentLoaded
window.addEventListener("DOMContentLoaded", () => {
    loadComponent("header", "header.html");
    loadComponent("footer", "footer.html");
    // Also run updateLayoutHeights on DOMContentLoaded with a small delay
    setTimeout(updateLayoutHeights, 100); // Initial delay for layout stability
});

// Recalculate heights after all assets (like images) are loaded
// This is crucial for accurate offsetHeight if images affect header/footer height
window.addEventListener("load", updateLayoutHeights);

// Recalculate heights on window resize to handle responsive adjustments
window.addEventListener("resize", updateLayoutHeights);
