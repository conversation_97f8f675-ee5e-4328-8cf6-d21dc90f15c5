// متغير عام لتخزين بيانات المواد والمساقات
let coursesData = [];

// تحميل بيانات عضو هيئة التدريس من API عند تحميل الصفحة
function loadFacultyData(facultyId = 1) {
    fetch(`http://localhost/faculty_web/backend/faculty.php?facultyid=${facultyId}`)
        .then(async response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            const text = await response.text();
            console.log('Raw response text:', text);

            if (!response.ok) {
                // حاول تحليل الاستجابة كـ JSON حتى لو كانت غير ناجحة، فقد تحتوي على رسالة خطأ من PHP
                try {
                    const errorJson = JSON.parse(text);
                    // إذا كان هناك مفتاح 'error' في JSON، استخدمه كرسالة خطأ
                    throw new Error(errorJson.error || `HTTP error! status: ${response.status}`);
                } catch (e) {
                    // إذا لم يكن JSON صالحًا أو لم يحتوي على مفتاح 'error'، استخدم الاستجابة النصية كرسالة خطأ
                    throw new Error(`HTTP error! status: ${response.status}, response: ${text}`);
                }
            }

            // محاولة تحليل النص كـ JSON
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('JSON parsing error:', e);
                console.error('Text that failed to parse:', text);
                throw new Error(`Failed to parse JSON response: ${e.message}. Raw response: ${text.substring(0, 200)}...`);
            }
        })
        .then(data => {
            console.log("المحتوى القادم من الخادم:", data); // سجل البيانات الكاملة للتصحيح

            // عرض البيانات الشخصية (هذا الجزء لا يتأثر بالأخطاء الجزئية)
            document.getElementById('displayFullName').textContent = data.fullname || '';
            document.getElementById('displaySpecialization').textContent = data.specialization || '';
            document.getElementById('displayEmail').textContent = data.email || '';
            document.getElementById('displayDepartment').textContent = data.departmentname || 'No Department';
            document.getElementById('doctorDescriptionDisplay').textContent = data.description || '';
            document.getElementById('doctorDescriptionEdit').value = data.description || '';
            document.getElementById('personalImage').src = data.imagepath || 'https://placehold.co/150x150?text=No+Image';
            document.getElementById('modalCertificateImage').src = data.certificateImage || 'https://placehold.co/400x300?text=No+Certificate';

            // معالجة قسم المواد الدراسية
            if (data.courses && data.courses.error) {
                // إذا كان هناك خطأ في جلب المواد الدراسية
                showUIMessage(`خطأ في تحميل المواد الدراسية: ${data.courses.error}`, 'error');
                coursesData = []; // تهيئة البيانات كفارغة لمنع مشاكل العرض
                populateCourseDropdown(); // إعادة ملء القائمة المنسدلة بالخيار الفارغ/رسالة الخطأ
            } else if (Array.isArray(data.courses)) {
                // إذا كانت البيانات صحيحة وصفيف
                coursesData = data.courses;
                populateCourseDropdown();
            } else {
                // إذا كان تنسيق البيانات غير متوقع
                showUIMessage('تنسيق بيانات المواد الدراسية غير صالح.', 'error');
                coursesData = [];
                populateCourseDropdown();
            }

            // معالجة قسم الأبحاث
            if (data.researches && data.researches.error) {
                // إذا كان هناك خطأ في جلب الأبحاث
                showUIMessage(`خطأ في تحميل الأبحاث: ${data.researches.error}`, 'error');
                renderResearches([]); // عرض جدول فارغ مع رسالة "لا توجد أبحاث"
            } else if (Array.isArray(data.researches)) {
                // إذا كانت البيانات صحيحة وصفيف
                renderResearches(data.researches);
            } else {
                // إذا كان تنسيق البيانات غير متوقع
                showUIMessage('تنسيق بيانات الأبحاث غير صالح.', 'error');
                renderResearches([]);
            }
        })
        .catch(error => {
            // معالجة الأخطاء العامة (مثل مشاكل الشبكة أو فشل تحليل JSON الأولي)
            console.error('Error loading faculty data:', error);
            showUIMessage(`خطأ عام أثناء تحميل بيانات عضو هيئة التدريس: ${error.message}`, 'error');
        });
}

// دالة لعرض رسائل واجهة المستخدم المخصصة (متوفرة مسبقًا)
function showUIMessage(message, type = 'info') {
    let messageArea = document.getElementById('ui-message-area');
    if (!messageArea) {
        messageArea = document.createElement('div');
        messageArea.id = 'ui-message-area';
        document.querySelector('.container').prepend(messageArea);
    }

    messageArea.textContent = message;

    if (type === 'error') {
        messageArea.style.backgroundColor = '#f8d7da';
        messageArea.style.color = '#721c24';
        messageArea.style.border = '1px solid #f5c6cb';
    } else if (type === 'success') {
        messageArea.style.backgroundColor = '#d4edda';
        messageArea.style.color = '#155724';
        messageArea.style.border = '1px solid #c3e6cb';
    } else {
        messageArea.style.backgroundColor = '#e2e3e5';
        messageArea.style.color = '#383d41';
        messageArea.style.border = '1px solid #d6d8db';
    }

    messageArea.style.display = 'block';

    if (messageArea.timeoutId) {
        clearTimeout(messageArea.timeoutId);
    }

    messageArea.timeoutId = setTimeout(() => {
        messageArea.style.display = 'none';
    }, 5000); // زيادة مدة عرض الرسالة إلى 5 ثوانٍ
}

// دالة لملء القائمة المنسدلة للمساقات بالمواد الخاصة بعضو هيئة التدريس
function populateCourseDropdown() {
    const courseSelect = document.getElementById('courseName');
    // تفريغ القائمة المنسدلة وإضافة الخيار الافتراضي
    courseSelect.innerHTML = '<option value="" disabled selected>Select Course</option>';

    // التحقق مما إذا كانت بيانات المواد الدراسية صفيفًا صالحًا وغير فارغ
    if (!Array.isArray(coursesData) || coursesData.length === 0) {
        const option = document.createElement('option');
        option.value = "";
        option.textContent = "لا توجد مواد دراسية متاحة";
        option.disabled = true; // جعل الخيار غير قابل للتحديد
        courseSelect.appendChild(option);
        return; // إنهاء الدالة إذا لم تكن هناك مواد
    }

    // ملء القائمة المنسدلة بالمواد المتاحة
    coursesData.forEach(course => {
        const option = document.createElement('option');
        option.value = course.name; // يمكنك استخدام ID هنا إذا كان متوفرًا
        option.textContent = `${course.name} (${course.semester})`;
        courseSelect.appendChild(option);
    });
}

// دالة لعرض محاضرات المادة المختارة مع إمكانية تنزيل الملفات
function showCourseLectures() {
    const courseSelect = document.getElementById('courseName');
    const selectedCourseName = courseSelect.value;
    const courseLecturesDiv = document.getElementById('courseLectures');
    const selectedCourseTitle = document.getElementById('selectedCourseTitle');
    const lecturesList = document.getElementById('lecturesList');

    if (selectedCourseName) {
        courseLecturesDiv.classList.remove('hidden');
        selectedCourseTitle.textContent = `Lectures for: ${selectedCourseName}`;
        lecturesList.innerHTML = '';

        const selectedCourse = coursesData.find(course => course.name === selectedCourseName);

        // التحقق مما إذا كانت المحاضرات موجودة وصفيف
        if (selectedCourse && Array.isArray(selectedCourse.lectures)) {
            if (selectedCourse.lectures.length === 0) {
                lecturesList.innerHTML = '<p>لا توجد محاضرات متاحة لهذه المادة.</p>';
            } else if (selectedCourse.lectures.error) {
                // إذا كان هناك خطأ في جلب المحاضرات لمادة معينة
                lecturesList.innerHTML = `<p class="text-red-700">خطأ في تحميل المحاضرات: ${selectedCourse.lectures.error}</p>`;
            } else {
                selectedCourse.lectures.forEach(lecture => {
                    const lectureItem = document.createElement('div');
                    lectureItem.className = 'lecture-item';

                    // تنسيق التاريخ
                    let formattedDate = '';
                    if (lecture.uploaddate && lecture.uploaddate !== null && lecture.uploaddate !== '') {
                        try {
                            const date = new Date(lecture.uploaddate);
                            if (!isNaN(date.getTime())) {
                                // تنسيق التاريخ بصيغة dd/mm/yyyy
                                const day = date.getDate().toString().padStart(2, '0');
                                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                                const year = date.getFullYear();
                                formattedDate = `${day}/${month}/${year}`;
                            }
                        } catch (error) {
                            formattedDate = '';
                        }
                    }

                    lectureItem.innerHTML = `
                        <span>${lecture.title} (${formattedDate})</span>
                        <div>
                            <a href="#" onclick="downloadLecture('${lecture.file}', '${lecture.title}'); return false;" class="secondary-btn download-btn">Download</a>
                        </div>
                    `;
                    lecturesList.appendChild(lectureItem);
                });
            }
        } else {
            lecturesList.innerHTML = '<p>لا توجد محاضرات متاحة لهذه المادة.</p>';
        }
    } else {
        courseLecturesDiv.classList.add('hidden');
        selectedCourseTitle.textContent = '';
        lecturesList.innerHTML = '';
    }
}

// دالة لتحميل ملف المحاضرة
function downloadLecture(base64Data, fileName) {
    try {
        // تنظيف اسم الملف من الأحرف غير المسموحة
        const cleanFileName = fileName.replace(/[<>:"/\\|?*]/g, '_');

        // تحويل base64 إلى blob
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);

        // تحديد نوع الملف بناءً على البيانات الأولى
        let mimeType = 'application/octet-stream';
        let fileExtension = '';

        // فحص البايتات الأولى لتحديد نوع الملف
        if (byteArray.length >= 4) {
            const header = Array.from(byteArray.slice(0, 4)).map(b => b.toString(16).padStart(2, '0')).join('');

            if (header.startsWith('25504446')) { // PDF
                mimeType = 'application/pdf';
                fileExtension = '.pdf';
            } else if (header.startsWith('504b0304')) { // ZIP/DOCX/PPTX
                mimeType = 'application/zip';
                fileExtension = '.zip';
            } else if (header.startsWith('d0cf11e0')) { // DOC/XLS/PPT
                mimeType = 'application/msword';
                fileExtension = '.doc';
            }
        }

        const blob = new Blob([byteArray], { type: mimeType });

        // إنشاء رابط التحميل
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = cleanFileName + fileExtension;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        console.log('تم تحميل الملف:', cleanFileName + fileExtension);
        showUIMessage('تم تحميل الملف بنجاح: ' + cleanFileName, 'success');
    } catch (error) {
        console.error('خطأ في تحميل الملف:', error);
        showUIMessage('خطأ في تحميل الملف: ' + error.message, 'error');
    }
}

// دالة لتحميل ملف البحث
function downloadResearch(base64Data, fileName) {
    try {
        // تنظيف اسم الملف من الأحرف غير المسموحة
        const cleanFileName = fileName.replace(/[<>:"/\\|?*]/g, '_');

        // تحويل base64 إلى blob
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);

        // تحديد نوع الملف بناءً على البيانات الأولى
        let mimeType = 'application/octet-stream';
        let fileExtension = '';

        // فحص البايتات الأولى لتحديد نوع الملف
        if (byteArray.length >= 4) {
            const header = Array.from(byteArray.slice(0, 4)).map(b => b.toString(16).padStart(2, '0')).join('');

            if (header.startsWith('25504446')) { // PDF
                mimeType = 'application/pdf';
                fileExtension = '.pdf';
            } else if (header.startsWith('504b0304')) { // ZIP/DOCX/PPTX
                mimeType = 'application/zip';
                fileExtension = '.zip';
            } else if (header.startsWith('d0cf11e0')) { // DOC/XLS/PPT
                mimeType = 'application/msword';
                fileExtension = '.doc';
            }
        }

        const blob = new Blob([byteArray], { type: mimeType });

        // إنشاء رابط التحميل
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = cleanFileName + fileExtension;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        console.log('تم تحميل البحث:', cleanFileName + fileExtension);
        showUIMessage('تم تحميل البحث بنجاح: ' + cleanFileName, 'success');
    } catch (error) {
        console.error('خطأ في تحميل البحث:', error);
        showUIMessage('خطأ في تحميل البحث: ' + error.message, 'error');
    }
}


// دالة جديدة لعرض الأبحاث
function renderResearches(researches) {
    const researchTableBody = document.getElementById('researchTableBody');
    if (!researchTableBody) return;

    researchTableBody.innerHTML = ''; // تفريغ المحتوى السابق

    // التحقق مما إذا كانت الأبحاث صفيفًا فارغًا أو غير موجود
    if (!Array.isArray(researches) || researches.length === 0) {
        researchTableBody.innerHTML = '<tr><td colspan="3" class="text-center text-gray-500">لا توجد أبحاث متاحة حالياً.</td></tr>';
        return;
    }

    researches.forEach(research => {
        const row = document.createElement('tr');

        // تنسيق التاريخ
        let formattedDate = 'لم يتم تحديد التاريخ';
        if (research.date && research.date !== null && research.date !== '' && research.date !== '0000-00-00') {
            try {
                const date = new Date(research.date);
                if (!isNaN(date.getTime())) {
                    const day = date.getDate().toString().padStart(2, '0');
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const year = date.getFullYear();
                    formattedDate = `${day}/${month}/${year}`;
                }
            } catch (error) {
                formattedDate = 'تاريخ غير صالح';
            }
        }

        row.innerHTML = `
            <td>${research.title}</td>
            <td>${formattedDate}</td>
            <td>
                <button onclick="downloadResearch('${research.file}', '${research.title}')" class="secondary-btn download-btn">Download</button>
            </td>
        `;
        researchTableBody.appendChild(row);
    });
}

// تهيئة الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    loadFacultyData(1);  // تحميل بيانات عضو هيئة التدريس برقم 1، يمكن تعديل الرقم حسب الحاجة

    // ربط دالة عرض المحاضرات عند تغيير اختيار المادة
    const courseSelect = document.getElementById('courseName');
    if (courseSelect) {
        courseSelect.addEventListener('change', showCourseLectures);
    }

    // --- Personal Info Section ---
    const editInfoBtn = document.getElementById('editInfoBtn');
    const saveInfoBtn = document.getElementById('saveInfoBtn');
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    const displayFullName = document.getElementById('displayFullName');
    const editFullName = document.getElementById('editFullName');
    const displaySpecialization = document.getElementById('displaySpecialization');
    const editSpecialization = document.getElementById('editSpecialization');
    const displayEmail = document.getElementById('displayEmail');
    const editEmail = document.getElementById('editEmail');
    const displayDepartment = document.getElementById('displayDepartment');
    const editDepartment = document.getElementById('editDepartment');

    if (editInfoBtn) {
        editInfoBtn.addEventListener('click', () => {
            displayFullName.classList.add('hidden');
            editFullName.classList.remove('hidden');
            editFullName.value = displayFullName.textContent.trim();

            displaySpecialization.classList.add('hidden');
            editSpecialization.classList.remove('hidden');
            editSpecialization.value = displaySpecialization.textContent.trim();

            displayEmail.classList.add('hidden');
            editEmail.classList.remove('hidden');
            editEmail.value = displayEmail.textContent.trim();

            displayDepartment.classList.add('hidden');
            editDepartment.classList.remove('hidden');
            editDepartment.value = displayDepartment.textContent.trim();

            editInfoBtn.classList.add('hidden');
            saveInfoBtn.classList.remove('hidden');
            cancelEditBtn.classList.remove('hidden');
        });
    }

    if (saveInfoBtn) {
        saveInfoBtn.addEventListener('click', () => {
            displayFullName.textContent = editFullName.value;
            displaySpecialization.textContent = editSpecialization.value;
            displayEmail.textContent = editEmail.value;
            displayDepartment.textContent = editDepartment.value;

            displayFullName.classList.remove('hidden');
            editFullName.classList.add('hidden');
            displaySpecialization.classList.remove('hidden');
            editSpecialization.classList.add('hidden');
            displayEmail.classList.remove('hidden');
            editEmail.classList.add('hidden');
            displayDepartment.classList.remove('hidden');
            editDepartment.classList.add('hidden');

            editInfoBtn.classList.remove('hidden');
            saveInfoBtn.classList.add('hidden');
            cancelEditBtn.classList.add('hidden');

            // In a real application, you would save this data to a database here.
            console.log('Personal information saved!');
        });
    }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => {
            displayFullName.classList.remove('hidden');
            editFullName.classList.add('hidden');
            displaySpecialization.classList.remove('hidden');
            editSpecialization.classList.add('hidden');
            displayEmail.classList.remove('hidden');
            editEmail.classList.add('hidden');
            displayDepartment.classList.remove('hidden');
            editDepartment.classList.add('hidden');

            editInfoBtn.classList.remove('hidden');
            saveInfoBtn.classList.add('hidden');
            cancelEditBtn.classList.add('hidden');
        });
    }

    // --- Doctor Description Section ---
    const editDescriptionBtn = document.getElementById('editDescriptionBtn');
    const saveDescriptionBtn = document.getElementById('saveDescriptionBtn');
    const cancelDescriptionEditBtn = document.getElementById('cancelDescriptionEditBtn');
    const doctorDescriptionDisplay = document.getElementById('doctorDescriptionDisplay');
    const doctorDescriptionEdit = document.getElementById('doctorDescriptionEdit');
    // const descriptionTextAreaWrapper = document.getElementById('descriptionTextAreaWrapper'); // Not used directly in JS logic

    // Show edit button initially for demonstration if it exists
    if (editDescriptionBtn) {
        editDescriptionBtn.classList.remove('hidden'); // Ensure it's visible for interaction
        editDescriptionBtn.addEventListener('click', () => {
            doctorDescriptionDisplay.classList.add('hidden');
            doctorDescriptionEdit.classList.remove('hidden');
            doctorDescriptionEdit.value = doctorDescriptionDisplay.textContent.trim();
            editDescriptionBtn.classList.add('hidden');
            saveDescriptionBtn.classList.remove('hidden');
            cancelDescriptionEditBtn.classList.remove('hidden');
        });
    }

    if (saveDescriptionBtn) {
        saveDescriptionBtn.addEventListener('click', () => {
            doctorDescriptionDisplay.textContent = doctorDescriptionEdit.value;
            doctorDescriptionDisplay.classList.remove('hidden');
            doctorDescriptionEdit.classList.add('hidden');
            editDescriptionBtn.classList.remove('hidden');
            saveDescriptionBtn.classList.add('hidden');
            cancelDescriptionEditBtn.classList.add('hidden');
            console.log('Biography saved!');
        });
    }

    if (cancelDescriptionEditBtn) {
        cancelDescriptionEditBtn.addEventListener('click', () => {
            doctorDescriptionDisplay.classList.remove('hidden');
            doctorDescriptionEdit.classList.add('hidden');
            editDescriptionBtn.classList.remove('hidden');
            saveDescriptionBtn.classList.add('hidden');
            cancelDescriptionEditBtn.classList.add('hidden');
        });
    }

    // --- Academic Research Section ---
    const showAddResearchFormBtn = document.getElementById('showAddResearchFormBtn');
    const newResearchForm = document.getElementById('newResearchForm');
    const newResearchFileInput = document.getElementById('newResearchFileInput');
    const chooseResearchFileBtn = document.getElementById('chooseResearchFileBtn');
    const researchFileSelectedName = document.getElementById('researchFileSelectedName');

    if (showAddResearchFormBtn) {
        showAddResearchFormBtn.addEventListener('click', () => {
            if (newResearchForm) newResearchForm.classList.remove('hidden');
            showAddResearchFormBtn.classList.add('hidden');
        });
    }

    if (chooseResearchFileBtn) {
        chooseResearchFileBtn.addEventListener('click', () => {
            if (newResearchFileInput) newResearchFileInput.click();
        });
    }

    if (newResearchFileInput) {
        newResearchFileInput.addEventListener('change', () => {
            if (newResearchFileInput.files.length > 0) {
                if (researchFileSelectedName) researchFileSelectedName.textContent = newResearchFileInput.files[0].name;
            } else {
                if (researchFileSelectedName) researchFileSelectedName.textContent = 'No file chosen';
            }
        });
    }


    // --- Certificate Modal ---
    const viewCertificateBtn = document.getElementById('viewCertificateBtn');
    const certificateModal = document.getElementById('certificateModal');
    const closeModalBtn = document.getElementById('closeModalBtn');
    const draggableModalContent = document.getElementById('draggableModalContent');

    if (viewCertificateBtn) {
        viewCertificateBtn.addEventListener('click', () => {
            if (certificateModal) certificateModal.classList.add('show');
        });
    }

    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', () => {
            if (certificateModal) certificateModal.classList.remove('show');
        });
    }

    if (certificateModal) {
        certificateModal.addEventListener('click', (e) => {
            if (e.target === certificateModal) {
                certificateModal.classList.remove('show');
            }
        });

        // Make modal draggable
        let isDragging = false;
        let offsetX, offsetY;

        draggableModalContent.addEventListener('mousedown', (e) => {
            if (e.target === closeModalBtn || e.target.tagName === 'BUTTON' || e.target.tagName === 'IMG') {
                return; // Don't drag if clicking on close button, other buttons, or image
            }
            isDragging = true;
            offsetX = e.clientX - draggableModalContent.getBoundingClientRect().left;
            offsetY = e.clientY - draggableModalContent.getBoundingClientRect().top;
            draggableModalContent.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            draggableModalContent.style.left = `${e.clientX - offsetX}px`;
            draggableModalContent.style.top = `${e.clientY - offsetY}px`;
            draggableModalContent.style.position = 'absolute'; // Ensure position is absolute for dragging
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            draggableModalContent.style.cursor = 'grab';
        });
    }
});

// وظائف عامة للأبحاث (يجب أن تكون متاحة عالمياً إذا تم استدعاؤها من HTML onclick)
function submitNewResearch() {
    const newResearchTitle = document.getElementById('newResearchTitle');
    const newResearchDate = document.getElementById('newResearchDate');
    const newResearchFileInput = document.getElementById('newResearchFileInput');
    const researchFileSelectedName = document.getElementById('researchFileSelectedName');
    const researchTableBody = document.getElementById('researchTableBody');
    const newResearchForm = document.getElementById('newResearchForm');
    const showAddResearchFormBtn = document.getElementById('showAddResearchFormBtn');

    const title = newResearchTitle.value.trim();
    const date = newResearchDate.value;
    const file = newResearchFileInput.files[0];

    if (!title || !date || !file) {
        showUIMessage('الرجاء ملء جميع الحقول واختيار ملف.', 'error');
        return;
    }

    const newRow = researchTableBody.insertRow();
    newRow.innerHTML = `
        <td>${title}</td>
        <td>${date}</td>
        <td><a href="${URL.createObjectURL(file)}" download="${file.name}" class="secondary-btn download-btn">Download</a></td>
    `;

    // Clear form
    newResearchTitle.value = '';
    newResearchDate.value = '';
    newResearchFileInput.value = '';
    researchFileSelectedName.textContent = 'No file chosen';
    if (newResearchForm) newResearchForm.classList.add('hidden');
    if (showAddResearchFormBtn) showAddResearchFormBtn.classList.remove('hidden');

    showUIMessage('تمت إضافة البحث بنجاح!', 'success');
    console.log('New research added:', { title, date, file: file.name });
}

function cancelNewResearchForm() {
    const newResearchTitle = document.getElementById('newResearchTitle');
    const newResearchDate = document.getElementById('newResearchDate');
    const newResearchFileInput = document.getElementById('newResearchFileInput');
    const researchFileSelectedName = document.getElementById('researchFileSelectedName');
    const newResearchForm = document.getElementById('newResearchForm');
    const showAddResearchFormBtn = document.getElementById('showAddResearchFormBtn');

    newResearchTitle.value = '';
    newResearchDate.value = '';
    newResearchFileInput.value = '';
    researchFileSelectedName.textContent = 'No file chosen';
    if (newResearchForm) newResearchForm.classList.add('hidden');
    if (showAddResearchFormBtn) showAddResearchFormBtn.classList.remove('hidden');
    showUIMessage('تم إلغاء إضافة البحث.', 'info');
}


