<?php
// Start output buffering as early as possible
ob_start();

error_reporting(E_ALL); // Enable all error reporting for debugging
ini_set('display_errors', 1); // <--- TEMPORARY: Enable display of errors for debugging
ini_set('log_errors', 1); // Enable logging errors
ini_set('error_log', __DIR__ . '/php_error.log'); // Set a specific log file for PHP errors

error_log("PHP Script started: " . date('Y-m-d H:i:s')); // Log script start

// Set content type to JSON
header('Content-Type: application/json; charset=utf-8');

// Database connection parameters
$servername = "localhost";
$username = "root"; // Your database username (e.g., 'root' for XAMPP)
$password = "";     // Your database password (e.g., '' for XAMPP root)
$dbname = "facultymember"; // Your database name
$port = 3308; // <--- CHANGED: Your database port (e.g., 3308 as previously indicated)

// Establish database connection using MySQLi
$conn = new mysqli($servername, $username, $password, $dbname,  $port);

// Check connection
if ($conn->connect_error) {
    ob_clean(); // Clean any buffered output before sending error JSON
    http_response_code(500); // Internal Server Error
    error_log("Database connection failed: " . $conn->connect_error); // Log the error
    echo json_encode(["status" => "error", "message" => "Database connection failed: " . $conn->connect_error]);
    exit();
}
error_log("Database connection successful."); // Log successful DB connection

// Define upload directories (only needed for profile images and certificates, not lectures or researches now)
define('UPLOAD_DIR_BASE', __DIR__ . '/../uploads/'); // Base path for all uploads
define('UPLOAD_DIR_CERTIFICATES', UPLOAD_DIR_BASE . 'certificates/');
define('UPLOAD_DIR_PROFILE_IMAGES', UPLOAD_DIR_BASE . 'profile_images/');
// REMOVED: define('UPLOAD_DIR_RESEARCHES', UPLOAD_DIR_BASE . 'researches/'); // No longer needed for research files as they'll be BLOBs

// Ensure upload directories exist and are writable (for non-BLOB files)
foreach ([UPLOAD_DIR_CERTIFICATES, UPLOAD_DIR_PROFILE_IMAGES] as $dir) { // MODIFIED: Removed UPLOAD_DIR_RESEARCHES
    if (!is_dir($dir)) {
        if (!mkdir($dir, 0775, true)) { // 0775 permissions, recursive
            error_log("Failed to create upload directory: " . $dir);
            ob_clean();
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => 'Failed to create upload directory: ' . basename($dir)]);
            exit();
        }
    }
}

/**
 * Helper function for bind_param (required for dynamic binding with call_user_func_array)
 * Provides references for parameters needed by bind_param, including the type string.
 * @param string $types The type definition string (e.g., "ssi").
 * @param array $arr The array of parameters.
 * @return array The array of references ready for call_user_func_array.
 */
function refValues($types, $arr) {
    $refs = [];
    // The first argument for bind_param is the types string
    $refs[] = $types; 
    foreach ($arr as $key => $value) {
        $refs[] = &$arr[$key]; // Subsequent arguments are the variables, passed by reference
    }
    return $refs;
}

/**
 * Function to fetch faculty member information along with department name and Base64 encoded image data using MySQLi.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @return array|null Faculty information including department name or error array.
 */
function getFacultyInfo($conn, $facultyId) {
    try {
        $stmt = $conn->prepare("SELECT f.facultyid, f.fullname, f.specialization, f.email, f.departmentid, d.departmentname, f.description, f.imagepath, f.certificateImage 
                                FROM Faculty f 
                                LEFT JOIN Department d ON f.departmentid = d.departmentid 
                                WHERE f.facultyid = ?");
        if (!$stmt) {
            error_log("Prepare failed in getFacultyInfo: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in getFacultyInfo.'];
        }
        $stmt->bind_param("i", $facultyId);
        $stmt->execute();
        $result = $stmt->get_result();
        $facultyInfo = $result->fetch_assoc();
        $stmt->close();
        
        // Base64 encode image data for JSON output
        if ($facultyInfo && isset($facultyInfo['imagepath']) && $facultyInfo['imagepath'] !== null) {
            $facultyInfo['imagepath'] = base64_encode($facultyInfo['imagepath']);
        } else {
            $facultyInfo['imagepath'] = null;
        }
        if ($facultyInfo && isset($facultyInfo['certificateImage']) && $facultyInfo['certificateImage'] !== null) {
            $facultyInfo['certificateImage'] = base64_encode($facultyInfo['certificateImage']);
        } else {
            $facultyInfo['certificateImage'] = null;
        }
        
        return $facultyInfo;
    } catch (Exception $e) {
        error_log("Error fetching faculty data: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error fetching faculty data: ' . $e->getMessage()];
    }
}

/**
 * Function to fetch all departments (ID and Name) for dropdowns.
 * @param mysqli $conn Database connection object.
 * @return array List of departments or error array.
 */
function getAllDepartments($conn) {
    try {
        $result = $conn->query("SELECT departmentid, departmentname FROM Department ORDER BY departmentname ASC");
        if (!$result) {
            error_log("Query failed in getAllDepartments: " . $conn->error);
            return ['status' => 'error', 'message' => 'Query failed in getAllDepartments.'];
        }
        $departments = [];
        while ($row = $result->fetch_assoc()) {
            $departments[] = $row;
        }
        return ['status' => 'success', 'data' => $departments];
    } catch (Exception $e) {
        error_log("Error fetching departments: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error fetching departments: ' . $e->getMessage()];
    }
}

/**
 * Function to update faculty member's personal details (fullname, email, specialization, departmentid) using MySQLi.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @param string $fullname Full name of the faculty member.
 * @param string $email Email of the faculty member.
 * @param string $specialization Specialization of the faculty member.
 * @param int $departmentid Department ID.
 * @return bool|array True on success, or an error array on failure.
 */
function updateFacultyPersonalDetails($conn, $facultyId, $fullname, $email, $specialization, $departmentid) {
    try {
        $sql = "UPDATE Faculty SET fullname = ?, email = ?, specialization = ?, departmentid = ? WHERE facultyid = ?";
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("Prepare failed in updateFacultyPersonalDetails: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in updateFacultyPersonalDetails.'];
        }
        $types = "sssii"; // s for string, i for integer
        $params = [$fullname, $email, $specialization, $departmentid, $facultyId]; // 5 parameters

        call_user_func_array(array($stmt, 'bind_param'), refValues($types, $params));
        
        $success = $stmt->execute();
        if (!$success) {
            error_log("Execute failed in updateFacultyPersonalDetails: " . $stmt->error);
            return ['status' => 'error', 'message' => 'Execute statement failed in updateFacultyPersonalDetails.'];
        }
        $stmt->close();
        return $success;
    } catch (Exception $e) {
        error_log("Error updating faculty personal details: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error updating faculty personal details: ' . $e->getMessage()];
    }
}

/**
 * Function to update only the description (Faculty Biography) of a faculty member using MySQLi.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @param string $description Description/Biography of the faculty member.
 * @return bool|array True on success, or an error array on failure.
 */
function updateFacultyDescription($conn, $facultyId, $description) {
    try {
        $sql = "UPDATE Faculty SET description = ? WHERE facultyid = ?";
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("Prepare failed in updateFacultyDescription: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in updateFacultyDescription.'];
        }
        $types = "si"; // s for string, i for integer
        $params = [$description, $facultyId];

        call_user_func_array(array($stmt, 'bind_param'), refValues($types, $params));
        
        $success = $stmt->execute();
        if (!$success) {
            error_log("Execute failed in updateFacultyDescription: " . $stmt->error);
            return ['status' => 'error', 'message' => 'Execute statement failed in updateFacultyDescription.'];
        }
        $stmt->close();
        return $success;
    } catch (Exception $e) {
        error_log("Error updating faculty description: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error updating faculty description: ' . $e->getMessage()];
    }
}

/**
 * Function to update only the profile image of a faculty member using MySQLi.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @param string|null $imageData Binary image data for profile picture (not Base64 encoded here).
 * @return bool|array True on success, or an error array on failure.
 */
function updateFacultyImage($conn, $facultyId, $imageData) {
    error_log("DEBUG: updateFacultyImage called for facultyId: " . $facultyId);
    if ($imageData === null) {
        error_log("DEBUG: imageData is NULL in updateFacultyImage.");
    } else {
        error_log("DEBUG: imageData length: " . strlen($imageData) . " bytes.");
    }

    try {
        $sql = "UPDATE Faculty SET imagepath = ? WHERE facultyid = ?";
        $stmt = $conn->prepare($sql);
        
        if (!$stmt) {
            $errorMessage = "Prepare failed in updateFacultyImage: " . $conn->error;
            error_log("ERROR: " . $errorMessage);
            return ['status' => 'error', 'message' => $errorMessage];
        }
        error_log("DEBUG: Prepare statement successful.");

        $stmt->bind_param("si", $imageData, $facultyId); 
        error_log("DEBUG: bind_param executed.");

        $success = $stmt->execute();
        
        if (!$success) {
            $errorMessage = "Execute failed in updateFacultyImage: " . $stmt->error;
            error_log("ERROR: " . $errorMessage);
            $stmt->close();
            return ['status' => 'error', 'message' => $errorMessage];
        }
        error_log("DEBUG: Execute statement successful. Rows affected: " . $stmt->affected_rows);
        $stmt->close();
        return true;
    } catch (Exception $e) {
        error_log("ERROR: Exception in updateFacultyImage: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Exception updating faculty image: ' . $e->getMessage()];
    }
}

/**
 * Function to update only the certificate image of a faculty member using MySQLi.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @param string|null $imageData Binary image data for the certificate (not Base64 encoded here).
 * @return bool|array True on success, or an error array on failure.
 */
function updateFacultyCertificateImage($conn, $facultyId, $imageData) {
    error_log("DEBUG: updateFacultyCertificateImage called for facultyId: " . $facultyId);
    if ($imageData === null) {
        error_log("DEBUG: certificate imageData is NULL in updateFacultyCertificateImage.");
    } else {
        error_log("DEBUG: certificate imageData length: " . strlen($imageData) . " bytes.");
    }

    try {
        $sql = "UPDATE Faculty SET certificateImage = ? WHERE facultyid = ?";
        $stmt = $conn->prepare($sql);
        
        if (!$stmt) {
            $errorMessage = "Prepare failed in updateFacultyCertificateImage: " . $conn->error;
            error_log("ERROR: " . $errorMessage);
            return ['status' => 'error', 'message' => $errorMessage];
        }
        error_log("DEBUG: Prepare statement successful for certificate.");

        $stmt->bind_param("si", $imageData, $facultyId); 
        error_log("DEBUG: bind_param executed for certificate.");

        $success = $stmt->execute();
        
        if (!$success) {
            $errorMessage = "Execute failed in updateFacultyCertificateImage: " . $stmt->error;
            error_log("ERROR: " . $errorMessage);
            $stmt->close();
            return ['status' => 'error', 'message' => $errorMessage];
        }
        error_log("DEBUG: Execute statement successful for certificate. Rows affected: " . $stmt->affected_rows);
        $stmt->close();
        return true;
    } catch (Exception $e) {
        error_log("ERROR: Exception in updateFacultyCertificateImage: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Exception updating faculty certificate image: ' . $e->getMessage()];
    }
}

// =============================================================
// FUNCTIONS FOR CURRENT SEMESTER COURSES SECTION
// =============================================================

/**
 * Function to fetch courses assigned to a specific faculty member.
 * MODIFIED: Now fetches materialid and Is_archived status from Material_Assignment.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @return array List of courses or error array.
 */
function getFacultyCourses($conn, $facultyId) {
    try {
        $stmt = $conn->prepare("
            SELECT ma.assignmentid, ma.semester, m.materialname, ma.materialid, ma.Is_archived
            FROM Material_Assignment ma
            JOIN Material m ON ma.materialid = m.materialid
            WHERE ma.facultyid = ?
            ORDER BY ma.semester, m.materialname
        ");
        if (!$stmt) {
            error_log("Prepare failed in getFacultyCourses: " . $conn->error);
            if ($conn->errno) {
                error_log("MySQLi Prepare Error No: " . $conn->errno);
                error_log("MySQLi Prepare Error: " . $conn->error);
            }
            return ['status' => 'error', 'message' => 'Prepare statement failed in getFacultyCourses.'];
        }
        $stmt->bind_param("i", $facultyId);
        $stmt->execute();
        
        if ($stmt->errno) {
            error_log("MySQLi Execute Error No: " . $stmt->errno);
            error_log("MySQLi Execute Error: " . $stmt->error);
        }

        $result = $stmt->get_result();
        $courses = [];
        while ($row = $result->fetch_assoc()) {
            $courses[] = $row;
        }
        $stmt->close();
        return ['status' => 'success', 'data' => $courses];
    } catch (Exception $e) {
        error_log("Error fetching faculty courses: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error fetching faculty courses: ' . $e->getMessage()];
    }
}

/**
 * NEW: Function to fetch materials (courses) for a specific department.
 * @param mysqli $conn Database connection object.
 * @param int $departmentId The ID of the department.
 * @return array List of materials or error array.
 */
function getDepartmentMaterials($conn, $departmentId) {
    error_log("getDepartmentMaterials called with departmentId: " . $departmentId);
    try {
        $stmt = $conn->prepare("SELECT materialid, materialname FROM Material WHERE departmentid = ? ORDER BY materialname ASC");
        if (!$stmt) {
            error_log("Prepare failed in getDepartmentMaterials: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in getDepartmentMaterials.'];
        }
        $stmt->bind_param("i", $departmentId);
        $stmt->execute();
        $result = $stmt->get_result();
        $materials = [];
        while ($row = $result->fetch_assoc()) {
            $materials[] = $row;
        }
        $stmt->close();
        error_log("getDepartmentMaterials: Successfully fetched " . count($materials) . " materials.");
        return ['status' => 'success', 'data' => $materials];
    } catch (Exception $e) {
        error_log("Error fetching department materials: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error fetching department materials: ' . $e->getMessage()];
    }
}

/**
 * Function to toggle the Is_archived status of a material assignment.
 * @param mysqli $conn Database connection object.
 * @param int $materialId The ID of the material to update.
 * @param int $newStatus The new status (0 for unarchive, 1 for archive).
 * @return bool|array True on success, or an error array on failure.
 */
function toggleArchiveMaterial($conn, $materialId, $newStatus) {
    error_log("toggleArchiveMaterial: Called with MaterialID: " . $materialId . ", New Status: " . $newStatus);
    try {
        $stmt = $conn->prepare("UPDATE Material_Assignment SET Is_archived = ? WHERE materialid = ?");
        if (!$stmt) {
            error_log("toggleArchiveMaterial: Prepare failed: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed to toggle archive status.'];
        }
        $stmt->bind_param("ii", $newStatus, $materialId);
        $success = $stmt->execute();
        if (!$success) {
            error_log("toggleArchiveMaterial: Execute failed: " . $stmt->error);
            return ['status' => 'error', 'message' => 'Execute statement failed to toggle archive status.'];
        }
        $stmt->close();
        error_log("toggleArchiveMaterial: Status updated successfully for MaterialID: " . $materialId . " to " . $newStatus);
        return true;
    } catch (Exception $e) {
        error_log("Error toggling archive status: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error toggling archive status: ' . $e->getMessage()];
    }
}


/**
 * Function to fetch lectures for a specific assignment ID.
 * @param mysqli $conn Database connection object.
 * @param int $assignmentId The ID of the material assignment.
 * @return array List of lectures or error array.
 */
function getLecturesByAssignment($conn, $assignmentId) {
    error_log("getLecturesByAssignment called with assignmentId: " . $assignmentId);
    try {
        // Select the BLOB data and encode it to Base64 in the query
        $stmt = $conn->prepare("SELECT lectureid, title, TO_BASE64(file) as file_base64, uploaddate FROM Lecture WHERE assignmentid = ? ORDER BY uploaddate DESC");
        if (!$stmt) {
            error_log("Prepare failed in getLecturesByAssignment: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in getLecturesByAssignment.'];
        }
        error_log("getLecturesByAssignment: Prepared statement successfully.");
        $stmt->bind_param("i", $assignmentId);
        error_log("getLecturesByAssignment: Parameters bound.");
        $stmt->execute();
        error_log("getLecturesByAssignment: Execute command sent.");
        $result = $stmt->get_result();
        
        if ($result === false) {
            error_log("getLecturesByAssignment: get_result failed: " . $stmt->error);
            $stmt->close();
            return ['status' => 'error', 'message' => 'Failed to get result set from query.'];
        }
        error_log("getLecturesByAssignment: Got result set.");

        $lectures = [];
        while ($row = $result->fetch_assoc()) {
            $row['file'] = $row['file_base64'];
            unset($row['file_base64']);
            $lectures[] = $row;
        }
        $stmt->close();
        error_log("getLecturesByAssignment: Successfully fetched " . count($lectures) . " lectures.");
        return ['status' => 'success', 'data' => $lectures];
    } catch (Exception $e) {
        error_log("Error fetching lectures by assignment: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error fetching lectures by assignment: ' . $e->getMessage()];
    }
}

/**
 * Function to add a new course assignment for a faculty member.
 * This function now expects materialid directly.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @param int $materialId The ID of the material/course.
 * @param string $semester The semester string (e.g., "Spring 2025").
 * @return bool|array True on success, or an error array on failure.
 */
function addCourseAssignment($conn, $facultyId, $materialId, $semester) {
    error_log("addCourseAssignment: Called with FacultyID: " . $facultyId . ", MaterialID: " . $materialId . ", Semester: " . $semester);
    try {
        $conn->begin_transaction();

        // Check for duplicate assignment (same faculty, material, semester)
        $checkStmt = $conn->prepare("SELECT assignmentid FROM Material_Assignment WHERE facultyid = ? AND materialid = ? AND semester = ?");
        if (!$checkStmt) {
            error_log("addCourseAssignment: Prepare failed for duplicate check: " . $conn->error);
            throw new Exception("Prepare failed for duplicate check: " . $conn->error);
        }
        $checkStmt->bind_param("iis", $facultyId, $materialId, $semester);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        if ($checkResult->num_rows > 0) {
            $checkStmt->close();
            throw new Exception("This course is already assigned to this faculty for the specified semester.");
        }
        $checkStmt->close();
        error_log("addCourseAssignment: No duplicate assignment found.");

        // Insert into Material_Assignment table
        $stmt = $conn->prepare("INSERT INTO Material_Assignment (facultyid, materialid, semester) VALUES (?, ?, ?)");
        if (!$stmt) {
            error_log("addCourseAssignment: Prepare failed assignment insert: " . $conn->error);
            throw new Exception("Prepare failed assignment insert: " . $conn->error);
        }
        $stmt->bind_param("iis", $facultyId, $materialId, $semester);
        $success = $stmt->execute();
        if (!$success) {
            error_log("addCourseAssignment: Execute failed assignment insert: " . $stmt->error);
            throw new Exception("Execute failed assignment insert: " . $stmt->error);
        }
        $stmt->close();
        error_log("addCourseAssignment: Material assignment inserted successfully.");

        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        error_log("Error adding course assignment: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to add course assignment: ' . $e->getMessage()];
    }
}

/**
 * Function to delete a course assignment and all related lectures.
 * Since lecture files are stored as BLOBs in the database, no physical file deletion is needed.
 * @param mysqli $conn Database connection object.
 * @param int $assignmentId The ID of the material assignment to delete.
 * @return bool|array True on success, or an error array on failure.
 */
function deleteCourseAssignment($conn, $assignmentId) {
    try {
        $conn->begin_transaction();

        // 1. Delete associated lectures from the database
        $stmt = $conn->prepare("DELETE FROM Lecture WHERE assignmentid = ?");
        if (!$stmt) { throw new Exception("Prepare failed lecture delete: " . $conn->error); }
        $stmt->bind_param("i", $assignmentId);
        $stmt->execute();
        $stmt->close();

        // 2. Delete the material assignment itself
        $stmt = $conn->prepare("DELETE FROM Material_Assignment WHERE assignmentid = ?");
        if (!$stmt) { throw new Exception("Prepare failed assignment delete: " . $conn->error); }
        $stmt->bind_param("i", $assignmentId);
        $stmt->execute();
        $stmt->close();

        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        error_log("Error deleting course assignment: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to delete course assignment: ' . $e->getMessage()];
    }
}

/**
 * Function to add a new lecture.
 * The file content is stored directly as BLOB in the database.
 * @param mysqli $conn Database connection object.
 * @param int $assignmentId The ID of the material assignment this lecture belongs to.
 * @param string $title The title of the lecture.
 * @param string $fileBase64 The Base64 encoded file content.
 * @return bool|array True on success, or an error array on failure.
 */
function addLecture($conn, $assignmentId, $title, $fileBase64) {
    try {
        $decodedFile = base64_decode($fileBase64);
        if ($decodedFile === false) {
            throw new Exception("Failed to decode Base64 file content.");
        }

        $stmt = $conn->prepare("INSERT INTO Lecture (title, file, uploaddate, assignmentid) VALUES (?, ?, NOW(), ?)");
        if (!$stmt) {
            throw new Exception("Prepare failed for adding lecture: " . $conn->error);
        }
        $stmt->bind_param("ssi", $title, $decodedFile, $assignmentId);
        
        $success = $stmt->execute();
        if (!$success) {
            throw new Exception("Execute failed for adding lecture: " . $stmt->error);
        }
        $stmt->close();
        return true;
    } catch (Exception $e) {
        error_log("Error adding lecture: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to add lecture: ' . $e->getMessage()];
    }
}

/**
 * Function to update an existing lecture.
 * Handles file update if a new Base64 file is provided.
 * @param mysqli $conn Database connection object.
 * @param int $lectureId The ID of the lecture to update.
 * @param string $title The new title of the lecture.
 * @param string|null $fileBase64 The Base64 encoded new file content (if provided).
 * @return bool|array True on success, or an error array on failure.
 */
function updateLecture($conn, $lectureId, $title, $fileBase64 = null) {
    error_log("updateLecture called. Lecture ID: " . $lectureId . ", Title: " . $title . ", File provided: " . ($fileBase64 !== null ? 'Yes' : 'No'));
    try {
        $updateSql = "UPDATE Lecture SET title = ?, uploaddate = NOW() WHERE lectureid = ?";
        $types = "si"; // s for string (title), i for integer (lectureid)
        $params = [$title, $lectureId];

        // Handle file update if a new file is uploaded (Base64 string provided)
        if ($fileBase64 !== null && $fileBase64 !== '') {
            $decodedFile = base64_decode($fileBase64);
            if ($decodedFile === false) {
                throw new Exception("Failed to decode Base64 file content for lecture update.");
            }
            $updateSql = "UPDATE Lecture SET title = ?, file = ?, uploaddate = NOW() WHERE lectureid = ?";
            $types = "ssi"; // s (title), s (decodedFile - BLOB), i (lectureid)
            $params = [$title, $decodedFile, $lectureId];
        }

        $stmt = $conn->prepare($updateSql);
        if (!$stmt) {
            error_log("Prepare failed in updateLecture: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in updateLecture.'];
        }
        
        call_user_func_array(array($stmt, 'bind_param'), refValues($types, $params));

        $success = $stmt->execute();
        if (!$success) {
            error_log("Execute failed in updateLecture: " . $stmt->error);
            return ['status' => 'error', 'message' => 'Execute statement failed in updateLecture.'];
        }
        $stmt->close();
        error_log("Lecture updated successfully in DB. Lecture ID: " . $lectureId);
        return true;
    } catch (Exception $e) {
        error_log("Error updating lecture: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to update lecture: ' . $e->getMessage()];
    }
}


/**
 * Function to delete a lecture.
 * Since lecture file is stored as BLOB, no physical file deletion is needed.
 * @param mysqli $conn Database connection object.
 * @param int $lectureId The ID of the lecture to delete.
 * @return bool|array True on success, or an error array on failure.
 */
function deleteLecture($conn, $lectureId) {
    try {
        $conn->begin_transaction();

        $stmt = $conn->prepare("DELETE FROM Lecture WHERE lectureid = ?");
        if (!$stmt) { throw new Exception("Prepare failed lecture delete: " . $conn->error); }
        $stmt->bind_param("i", $lectureId);
        $stmt->execute();
        $stmt->close();

        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        error_log("Error deleting lecture: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to delete lecture: ' . $e->getMessage()];
    }
}

// =============================================================
// FUNCTIONS FOR ACADEMIC RESEARCH SECTION (MODIFIED FOR BLOB)
// =============================================================

/**
 * Function to fetch research for a specific faculty member.
 * Returns Base64 encoded file data.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @return array List of research or error array.
 */
function getFacultyResearch($conn, $facultyId) {
    error_log("getFacultyResearch called with facultyId: " . $facultyId);
    try {
        // SELECT TO_BASE64(file) as file_base64
        $stmt = $conn->prepare("SELECT researchid, title, TO_BASE64(file) as file_base64, Date_published FROM Research WHERE facultyid = ? ORDER BY Date_published DESC");
        if (!$stmt) {
            error_log("Prepare failed in getFacultyResearch: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in getFacultyResearch.'];
        }
        $stmt->bind_param("i", $facultyId);
        $stmt->execute();
        $result = $stmt->get_result();
        $researches = [];
        while ($row = $result->fetch_assoc()) {
            $row['file'] = $row['file_base64']; // Rename for consistency with JS
            unset($row['file_base64']);
            $researches[] = $row;
        }
        $stmt->close();
        error_log("getFacultyResearch: Successfully fetched " . count($researches) . " researches.");
        return ['status' => 'success', 'data' => $researches];
    } catch (Exception $e) {
        error_log("Error fetching faculty research: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error fetching faculty research: ' . $e->getMessage()];
    }
}

/**
 * Function to add a new research entry.
 * The file content is stored directly as BLOB in the database.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @param string $title The title of the research.
 * @param string $fileBase64 The Base64 encoded file content.
 * @param string $datePublished The publication date.
 * @return bool|array True on success, or an error array on failure.
 */
function addResearch($conn, $facultyId, $title, $fileBase64, $datePublished) {
    error_log("addResearch called. Title: " . $title);
    try {
        $decodedFile = base64_decode($fileBase64);
        if ($decodedFile === false) {
            throw new Exception("Failed to decode Base64 file content for research.");
        }

        $stmt = $conn->prepare("INSERT INTO Research (facultyid, title, file, Date_published) VALUES (?, ?, ?, ?)");
        if (!$stmt) {
            throw new Exception("Prepare failed in addResearch: " . $conn->error);
        }
        // Use 's' for BLOB binding
        // Corrected types: i (facultyid), s (title), s (decodedFile - BLOB), s (datePublished)
        $stmt->bind_param("isss", $facultyId, $title, $decodedFile, $datePublished); 
        
        $success = $stmt->execute();
        if (!$success) {
            throw new Exception("Execute failed in addResearch: " . $stmt->error);
        }
        $stmt->close();
        error_log("Research added successfully to DB.");
        return true;
    } catch (Exception $e) {
        error_log("Error adding research: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to add research: ' . $e->getMessage()];
    }
}

/**
 * Function to update an existing research entry.
 * Handles file update if a new Base64 file is provided.
 * @param mysqli $conn Database connection object.
 * @param int $researchId The ID of the research to update.
 * @param string $title The new title of the research.
 * @param string|null $fileBase64 The Base64 encoded new file content (if provided).
 * @param string $datePublished The new publication date.
 * @return bool|array True on success, or an error array on failure.
 */
function updateResearch($conn, $researchId, $title, $fileBase64, $datePublished) {
    error_log("updateResearch called. Research ID: " . $researchId . ", Title: " . $title . ", File provided: " . ($fileBase64 !== null ? 'Yes' : 'No'));
    try {
        $updateSql = "UPDATE Research SET title = ?, Date_published = ? WHERE researchid = ?";
        $types = "ssi"; // s (title), s (datePublished), i (researchid)
        $params = [$title, $datePublished, $researchId];

        if ($fileBase64 !== null && $fileBase64 !== '') {
            $decodedFile = base64_decode($fileBase64);
            if ($decodedFile === false) {
                throw new Exception("Failed to decode Base64 file content for research update.");
            }
            $updateSql = "UPDATE Research SET title = ?, file = ?, Date_published = ? WHERE researchid = ?";
            $types = "sssi"; // s (title), s (decodedFile - BLOB), s (datePublished), i (researchid)
            $params = [$title, $decodedFile, $datePublished, $researchId];
        }

        $stmt = $conn->prepare($updateSql);
        if (!$stmt) {
            error_log("Prepare failed in updateResearch: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in updateResearch.'];
        }
        
        call_user_func_array(array($stmt, 'bind_param'), refValues($types, $params));

        $success = $stmt->execute();
        if (!$success) {
            error_log("Execute failed in updateResearch: " . $stmt->error);
            return ['status' => 'error', 'message' => 'Execute statement failed in updateResearch.'];
        }
        $stmt->close();
        error_log("Research updated successfully in DB. Research ID: " . $researchId);
        return true;
    } catch (Exception $e) {
        error_log("Error updating research: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to update research: ' . $e->getMessage()];
    }
}

/**
 * Function to delete a research entry.
 * Since research file is stored as BLOB, no physical file deletion is needed.
 * @param mysqli $conn Database connection object.
 * @param int $researchId The ID of the research to delete.
 * @return bool|array True on success, or an error array on failure.
 */
function deleteResearch($conn, $researchId) {
    try {
        $stmt = $conn->prepare("DELETE FROM Research WHERE researchid = ?");
        if (!$stmt) { throw new Exception("Prepare failed research delete: " . $conn->error); }
        $stmt->bind_param("i", $researchId);
        $stmt->execute();
        $stmt->close();
        return true;
    } catch (Exception $e) {
        error_log("Error deleting research: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to delete research: ' . $e->getMessage()];
    }
}


// =============================================================
// MAIN REQUEST HANDLING LOGIC
// =============================================================

// Determine request method and action
$action = $_REQUEST['action'] ?? ''; // Use $_REQUEST to get action from GET or POST

$response = ['status' => 'error', 'message' => 'Invalid action.'];

switch ($action) {
    case 'get_faculty_info':
        $facultyId = filter_var($_GET['facultyid'] ?? '', FILTER_VALIDATE_INT);
        if ($facultyId) {
            $info = getFacultyInfo($conn, $facultyId);
            if ($info && !isset($info['status']) || $info['status'] !== 'error') {
                $response = ['status' => 'success', 'data' => $info];
            } else {
                $response = $info; // Propagate error message from function
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Faculty ID is required.'];
        }
        break;

    case 'get_departments':
        $response = getAllDepartments($conn);
        break;

    case 'update_personal_details':
        $facultyId = filter_var($_POST['facultyid'] ?? '', FILTER_VALIDATE_INT);
        $fullname = trim($_POST['fullname'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $specialization = trim($_POST['specialization'] ?? '');
        $departmentid = filter_var($_POST['departmentid'] ?? '', FILTER_VALIDATE_INT);

        if ($facultyId && $fullname && $email && $specialization && $departmentid) {
            $result = updateFacultyPersonalDetails($conn, $facultyId, $fullname, $email, $specialization, $departmentid);
            if ($result === true) {
                $response = ['status' => 'success', 'message' => 'Personal details updated.'];
            } else {
                $response = $result; // Propagate error from function
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Missing data for personal details update.'];
        }
        break;

    case 'update_description':
        $facultyId = filter_var($_POST['facultyid'] ?? '', FILTER_VALIDATE_INT);
        $description = $_POST['description'] ?? '';

        if ($facultyId) {
            $result = updateFacultyDescription($conn, $facultyId, $description);
            if ($result === true) {
                $response = ['status' => 'success', 'message' => 'Description updated.'];
            } else {
                $response = $result; // Propagate error from function
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Faculty ID is required for description update.'];
        }
        break;

    case 'update_image':
        $facultyId = filter_var($_POST['facultyid'] ?? '', FILTER_VALIDATE_INT);
        $imageDataBase64 = $_POST['imagepath'] ?? ''; // This is Base64 string from JS

        if ($facultyId && $imageDataBase64) {
            $imageDataBinary = base64_decode($imageDataBase64);
            if ($imageDataBinary === false) {
                $response = ['status' => 'error', 'message' => 'Failed to decode image data.'];
            } else {
                $result = updateFacultyImage($conn, $facultyId, $imageDataBinary);
                if ($result === true) {
                    $response = ['status' => 'success', 'message' => 'Profile image updated.'];
                } else {
                    $response = $result; // Propagate error from function
                }
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Missing data for image update.'];
        }
        break;

    case 'update_certificate_image':
        $facultyId = filter_var($_POST['facultyid'] ?? '', FILTER_VALIDATE_INT);
        $imageDataBase64 = $_POST['certificateImage'] ?? ''; // This is Base64 string from JS

        if ($facultyId && $imageDataBase64) {
            $imageDataBinary = base64_decode($imageDataBase64);
            if ($imageDataBinary === false) {
                $response = ['status' => 'error', 'message' => 'Failed to decode certificate image data.'];
            } else {
                $result = updateFacultyCertificateImage($conn, $facultyId, $imageDataBinary);
                if ($result === true) {
                    $response = ['status' => 'success', 'message' => 'Certificate image updated.'];
                } else {
                    $response = $result; // Propagate error from function
                }
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Missing data for certificate image update.'];
        }
        break;

    case 'get_faculty_courses':
        $facultyId = filter_var($_GET['facultyid'] ?? '', FILTER_VALIDATE_INT);
        if ($facultyId) {
            $response = getFacultyCourses($conn, $facultyId);
        } else {
            $response = ['status' => 'error', 'message' => 'Faculty ID is required.'];
        }
        break;
    
    case 'get_department_materials':
        $departmentId = filter_var($_GET['departmentid'] ?? '', FILTER_VALIDATE_INT);
        if ($departmentId) {
            $response = getDepartmentMaterials($conn, $departmentId);
        } else {
            $response = ['status' => 'error', 'message' => 'Department ID is required.'];
        }
        break;

    case 'get_lectures_by_assignment':
        $assignmentId = filter_var($_GET['assignmentid'] ?? '', FILTER_VALIDATE_INT);
        if ($assignmentId) {
            $response = getLecturesByAssignment($conn, $assignmentId);
        } else {
            $response = ['status' => 'error', 'message' => 'Assignment ID is required.'];
        }
        break;

    case 'add_course_assignment':
        $facultyId = filter_var($_POST['facultyid'] ?? '', FILTER_VALIDATE_INT);
        $materialId = filter_var($_POST['materialid'] ?? '', FILTER_VALIDATE_INT);
        $semester = trim($_POST['semester'] ?? '');
        if ($facultyId && $materialId && $semester) {
            $result = addCourseAssignment($conn, $facultyId, $materialId, $semester);
            if ($result === true) {
                $response = ['status' => 'success', 'message' => 'Course assignment added.'];
            } else {
                $response = $result; // Propagate error from function
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Missing data for course assignment.'];
        }
        break;

    case 'delete_course_assignment':
        $assignmentId = filter_var($_POST['assignmentid'] ?? '', FILTER_VALIDATE_INT);
        if ($assignmentId) {
            $result = deleteCourseAssignment($conn, $assignmentId);
            if ($result === true) {
                $response = ['status' => 'success', 'message' => 'Course assignment and lectures deleted.'];
            } else {
                $response = $result; // Propagate error from function
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Assignment ID is required for deletion.'];
        }
        break;

    case 'add_lecture':
        $assignmentId = filter_var($_POST['assignmentid'] ?? '', FILTER_VALIDATE_INT);
        $title = trim($_POST['title'] ?? '');
        $fileBase64 = $_POST['file'] ?? '';

        if ($assignmentId && $title && $fileBase64) {
            $result = addLecture($conn, $assignmentId, $title, $fileBase64);
            if ($result === true) {
                $response = ['status' => 'success', 'message' => 'Lecture added successfully.'];
            } else {
                $response = $result;
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Missing data for adding lecture.'];
        }
        break;

    case 'update_lecture':
        $lectureId = filter_var($_POST['lectureid'] ?? '', FILTER_VALIDATE_INT);
        $title = trim($_POST['title'] ?? '');
        $fileBase64 = $_POST['file'] ?? null; // Can be null if no new file is selected

        if ($lectureId && $title) { // File is optional for update
            $result = updateLecture($conn, $lectureId, $title, $fileBase64);
            if ($result === true) {
                $response = ['status' => 'success', 'message' => 'Lecture updated successfully.'];
            } else {
                $response = $result;
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Missing required data for updating lecture.'];
        }
        break;

    case 'delete_lecture':
        $lectureId = filter_var($_POST['lectureid'] ?? '', FILTER_VALIDATE_INT);
        if ($lectureId) {
            $result = deleteLecture($conn, $lectureId);
            if ($result === true) {
                $response = ['status' => 'success', 'message' => 'Lecture deleted.'];
            } else {
                $response = $result;
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Lecture ID is required for deletion.'];
        }
        break;

    case 'get_faculty_research':
        $facultyId = filter_var($_GET['facultyid'] ?? '', FILTER_VALIDATE_INT);
        if ($facultyId) {
            $response = getFacultyResearch($conn, $facultyId);
        } else {
            $response = ['status' => 'error', 'message' => 'Faculty ID is required.'];
        }
        break;

    case 'add_research':
        $facultyId = filter_var($_POST['facultyid'] ?? '', FILTER_VALIDATE_INT);
        $title = trim($_POST['title'] ?? '');
        $fileBase64 = $_POST['file'] ?? '';
        $datePublished = trim($_POST['datePublished'] ?? '');

        if ($facultyId && $title && $fileBase64 && $datePublished) {
            $result = addResearch($conn, $facultyId, $title, $fileBase64, $datePublished);
            if ($result === true) {
                $response = ['status' => 'success', 'message' => 'Research added successfully.'];
            } else {
                $response = $result;
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Missing data for adding research.'];
        }
        break;

    case 'update_research':
        $researchId = filter_var($_POST['researchid'] ?? '', FILTER_VALIDATE_INT);
        $title = trim($_POST['title'] ?? '');
        $fileBase64 = $_POST['file'] ?? null; // Can be null if no new file is selected
        $datePublished = trim($_POST['datePublished'] ?? '');

        if ($researchId && $title && $datePublished) { // File is optional for update
            $result = updateResearch($conn, $researchId, $title, $fileBase64, $datePublished);
            if ($result === true) {
                $response = ['status' => 'success', 'message' => 'Research updated successfully.'];
            } else {
                $response = $result;
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Missing required data for updating research.'];
        }
        break;

    case 'delete_research':
        $researchId = filter_var($_POST['researchid'] ?? '', FILTER_VALIDATE_INT);
        if ($researchId) {
            $result = deleteResearch($conn, $researchId);
            if ($result === true) {
                $response = ['status' => 'success', 'message' => 'Research deleted.'];
            } else {
                $response = $result;
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Research ID is required for deletion.'];
        }
        break;
    case 'toggle_archive_material': // NEW case for archiving
        $materialId = filter_var($_POST['materialid'] ?? '', FILTER_VALIDATE_INT);
        $newStatus = filter_var($_POST['new_status'] ?? '', FILTER_VALIDATE_INT); // Expecting 0 or 1

        // Validate newStatus to be strictly 0 or 1
        if ($newStatus !== 0 && $newStatus !== 1) {
            $response = ['status' => 'error', 'message' => 'Invalid archive status provided. Must be 0 or 1.'];
            break;
        }

        if ($materialId) {
            $result = toggleArchiveMaterial($conn, $materialId, $newStatus);
            if ($result === true) {
                $response = ['status' => 'success', 'message' => 'Archive status updated.'];
            } else {
                $response = $result; // Propagate error from function
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Material ID is required for archiving.'];
        }
        break;
    default:
        $response = ['status' => 'error', 'message' => 'Unknown action.'];
        break;
}

// Close connection
$conn->close();

// Output JSON response
ob_clean(); // Clean any remaining buffered output
echo json_encode($response);
exit();
?>
