<?php
// Start output buffering as early as possible
ob_start();

// Start the session at the very beginning
session_start();

error_reporting(E_ALL); // Enable all error reporting for debugging
ini_set('display_errors', 1); // <--- TEMPORARY: Enable display of errors for debugging
ini_set('log_errors', 1); // Enable logging errors
ini_set('error_log', __DIR__ . '/php_error.log'); // Set a specific log file for PHP errors

error_log("PHP Script started: " . date('Y-m-d H:i:s')); // Log script start

// Set content type to JSON
header('Content-Type: application/json; charset=utf-8');

// Database connection parameters
$servername = "localhost";
$username = "root";    // Your database username (e.g., 'root' for XAMPP)
$password = "";        // Your database password (e.g., '' for XAMPP root)
$dbname = "facultymember"; // Your database name
$port = 3308; // <--- CHANGED: Your database port (e.g., 3308 as previously indicated)

// Establish database connection using MySQLi
$conn = new mysqli($servername, $username, $password, $dbname, $port);

// Check connection
if ($conn->connect_error) {
    ob_clean(); // Clean any buffered output before sending error JSON
    http_response_code(500); // Internal Server Error
    error_log("Database connection failed: " . $conn->connect_error); // Log the error
    echo json_encode(["status" => "error", "message" => "Database connection failed: " . $conn->connect_error]);
    exit();
}
error_log("Database connection successful."); // Log successful DB connection

// Define upload directories (only needed for profile images and certificates, not lectures or researches now)
define('UPLOAD_DIR_BASE', __DIR__ . '/../uploads/'); // Base path for all uploads
define('UPLOAD_DIR_CERTIFICATES', UPLOAD_DIR_BASE . 'certificates/');
define('UPLOAD_DIR_PROFILE_IMAGES', UPLOAD_DIR_BASE . 'profile_images/');
// REMOVED: define('UPLOAD_DIR_RESEARCHES', UPLOAD_DIR_BASE . 'researches/'); // No longer needed for research files as they'll be BLOBs

// Ensure upload directories exist and are writable (for non-BLOB files)
foreach ([UPLOAD_DIR_CERTIFICATES, UPLOAD_DIR_PROFILE_IMAGES] as $dir) { // MODIFIED: Removed UPLOAD_DIR_RESEARCHES
    if (!is_dir($dir)) {
        if (!mkdir($dir, 0775, true)) { // 0775 permissions, recursive
            error_log("Failed to create upload directory: " . $dir);
            ob_clean();
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => 'Failed to create upload directory: ' . basename($dir)]);
            exit();
        }
    }
}

/**
 * Helper function for bind_param (required for dynamic binding with call_user_func_array)
 * Provides references for parameters needed by bind_param, including the type string.
 * @param string $types The type definition string (e.g., "ssi").
 * @param array $arr The array of parameters.
 * @return array The array of references ready for call_user_func_array.
 */
function refValues($types, $arr) {
    $refs = [];
    // The first argument for bind_param is the types string
    $refs[] = $types; 
    foreach ($arr as $key => $value) {
        $refs[] = &$arr[$key]; // Subsequent arguments are the variables, passed by reference
    }
    return $refs;
}

/**
 * Function to fetch faculty member information along with department name and Base64 encoded image data using MySQLi.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @return array|null Faculty information including department name or error array.
 */
function getFacultyInfo($conn, $facultyId) {
    try {
        $stmt = $conn->prepare("SELECT f.facultyid, f.fullname, f.specialization, f.email, f.departmentid, d.departmentname, f.description, f.imagepath, f.certificateImage 
                                 FROM Faculty f 
                                 LEFT JOIN Department d ON f.departmentid = d.departmentid 
                                 WHERE f.facultyid = ?");
        if (!$stmt) {
            error_log("Prepare failed in getFacultyInfo: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in getFacultyInfo.'];
        }
        $stmt->bind_param("i", $facultyId);
        $stmt->execute();
        $result = $stmt->get_result();
        $facultyInfo = $result->fetch_assoc();
        $stmt->close();
        
        // Base64 encode image data for JSON output
        if ($facultyInfo && isset($facultyInfo['imagepath']) && $facultyInfo['imagepath'] !== null) {
            $facultyInfo['imagepath'] = base64_encode($facultyInfo['imagepath']);
        } else {
            $facultyInfo['imagepath'] = null;
        }
        if ($facultyInfo && isset($facultyInfo['certificateImage']) && $facultyInfo['certificateImage'] !== null) {
            $facultyInfo['certificateImage'] = base64_encode($facultyInfo['certificateImage']);
        } else {
            $facultyInfo['certificateImage'] = null;
        }
        
        return $facultyInfo;
    } catch (Exception $e) {
        error_log("Error fetching faculty data: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error fetching faculty data: ' . $e->getMessage()];
    }
}

/**
 * Function to fetch all departments (ID and Name) for dropdowns.
 * @param mysqli $conn Database connection object.
 * @return array List of departments or error array.
 */
function getAllDepartments($conn) {
    try {
        $result = $conn->query("SELECT departmentid, departmentname FROM Department ORDER BY departmentname ASC");
        if (!$result) {
            error_log("Query failed in getAllDepartments: " . $conn->error);
            return ['status' => 'error', 'message' => 'Query failed in getAllDepartments.'];
        }
        $departments = [];
        while ($row = $result->fetch_assoc()) {
            $departments[] = $row;
        }
        return ['status' => 'success', 'data' => $departments];
    } catch (Exception $e) {
        error_log("Error fetching departments: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error fetching departments: ' . $e->getMessage()];
    }
}

/**
 * Function to update faculty member's personal details (fullname, email, specialization, departmentid) using MySQLi.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @param string $fullname Full name of the faculty member.
 * @param string $email Email of the faculty member.
 * @param string $specialization Specialization of the faculty member.
 * @param int $departmentid Department ID.
 * @return bool|array True on success, or an error array on failure.
 */
function updateFacultyPersonalDetails($conn, $facultyId, $fullname, $email, $specialization, $departmentid) {
    try {
        $sql = "UPDATE Faculty SET fullname = ?, email = ?, specialization = ?, departmentid = ? WHERE facultyid = ?";
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("Prepare failed in updateFacultyPersonalDetails: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in updateFacultyPersonalDetails.'];
        }
        $types = "sssii"; // s for string, i for integer
        $params = [$fullname, $email, $specialization, $departmentid, $facultyId]; // 5 parameters

        call_user_func_array(array($stmt, 'bind_param'), refValues($types, $params));
        
        $success = $stmt->execute();
        if (!$success) {
            error_log("Execute failed in updateFacultyPersonalDetails: " . $stmt->error);
            return ['status' => 'error', 'message' => 'Execute statement failed in updateFacultyPersonalDetails.'];
        }
        $stmt->close();
        return $success;
    } catch (Exception $e) {
        error_log("Error updating faculty personal details: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error updating faculty personal details: ' . $e->getMessage()];
    }
}

/**
 * Function to update only the description (Faculty Biography) of a faculty member using MySQLi.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @param string $description Description/Biography of the faculty member.
 * @return bool|array True on success, or an error array on failure.
 */
function updateFacultyDescription($conn, $facultyId, $description) {
    try {
        $sql = "UPDATE Faculty SET description = ? WHERE facultyid = ?";
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("Prepare failed in updateFacultyDescription: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in updateFacultyDescription.'];
        }
        $types = "si"; // s for string, i for integer
        $params = [$description, $facultyId];

        call_user_func_array(array($stmt, 'bind_param'), refValues($types, $params));
        
        $success = $stmt->execute();
        if (!$success) {
            error_log("Execute failed in updateFacultyDescription: " . $stmt->error);
            return ['status' => 'error', 'message' => 'Execute statement failed in updateFacultyDescription.'];
        }
        $stmt->close();
        return $success;
    } catch (Exception $e) {
        error_log("Error updating faculty description: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error updating faculty description: ' . $e->getMessage()];
    }
}

/**
 * Function to update only the profile image of a faculty member using MySQLi.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @param string|null $imageData Binary image data for profile picture (not Base64 encoded here).
 * @return bool|array True on success, or an error array on failure.
 */
function updateFacultyImage($conn, $facultyId, $imageData) {
    error_log("DEBUG: updateFacultyImage called for facultyId: " . $facultyId);
    if ($imageData === null) {
        error_log("DEBUG: imageData is NULL in updateFacultyImage.");
    } else {
        error_log("DEBUG: imageData length: " . strlen($imageData) . " bytes.");
    }

    try {
        $sql = "UPDATE Faculty SET imagepath = ? WHERE facultyid = ?";
        $stmt = $conn->prepare($sql);
        
        if (!$stmt) {
            $errorMessage = "Prepare failed in updateFacultyImage: " . $conn->error;
            error_log("ERROR: " . $errorMessage);
            return ['status' => 'error', 'message' => $errorMessage];
        }
        error_log("DEBUG: Prepare statement successful.");

        $stmt->bind_param("si", $imageData, $facultyId); 
        error_log("DEBUG: bind_param executed.");

        $success = $stmt->execute();
        
        if (!$success) {
            $errorMessage = "Execute failed in updateFacultyImage: " . $stmt->error;
            error_log("ERROR: " . $errorMessage);
            $stmt->close();
            return ['status' => 'error', 'message' => $errorMessage];
        }
        error_log("DEBUG: Execute statement successful. Rows affected: " . $stmt->affected_rows);
        $stmt->close();
        return true;
    } catch (Exception $e) {
        error_log("ERROR: Exception in updateFacultyImage: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Exception updating faculty image: ' . $e->getMessage()];
    }
}

/**
 * Function to update only the certificate image of a faculty member using MySQLi.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @param string|null $imageData Binary image data for the certificate (not Base64 encoded here).
 * @return bool|array True on success, or an error array on failure.
 */
function updateFacultyCertificateImage($conn, $facultyId, $imageData) {
    error_log("DEBUG: updateFacultyCertificateImage called for facultyId: " . $facultyId);
    if ($imageData === null) {
        error_log("DEBUG: certificate imageData is NULL in updateFacultyCertificateImage.");
    } else {
        error_log("DEBUG: certificate imageData length: " . strlen($imageData) . " bytes.");
    }

    try {
        $sql = "UPDATE Faculty SET certificateImage = ? WHERE facultyid = ?";
        $stmt = $conn->prepare($sql);
        
        if (!$stmt) {
            $errorMessage = "Prepare failed in updateFacultyCertificateImage: " . $conn->error;
            error_log("ERROR: " . $errorMessage);
            return ['status' => 'error', 'message' => $errorMessage];
        }
        error_log("DEBUG: Prepare statement successful for certificate.");

        $stmt->bind_param("si", $imageData, $facultyId); 
        error_log("DEBUG: bind_param executed for certificate.");

        $success = $stmt->execute();
        
        if (!$success) {
            $errorMessage = "Execute failed in updateFacultyCertificateImage: " . $stmt->error;
            error_log("ERROR: " . $errorMessage);
            $stmt->close();
            return ['status' => 'error', 'message' => $errorMessage];
        }
        error_log("DEBUG: Execute statement successful for certificate. Rows affected: " . $stmt->affected_rows);
        $stmt->close();
        return true;
    } catch (Exception $e) {
        error_log("ERROR: Exception in updateFacultyCertificateImage: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Exception updating faculty certificate image: ' . $e->getMessage()];
    }
}

// =============================================================
// FUNCTIONS FOR CURRENT SEMESTER COURSES SECTION
// =============================================================

/**
 * Function to fetch courses assigned to a specific faculty member.
 * MODIFIED: Now fetches materialid and Is_archived status from Material_Assignment.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @return array List of courses or error array.
 */
function getFacultyCourses($conn, $facultyId) {
    try {
        $stmt = $conn->prepare("
            SELECT ma.assignmentid, ma.semester, m.materialname, ma.materialid, ma.Is_archived
            FROM Material_Assignment ma
            JOIN Material m ON ma.materialid = m.materialid
            WHERE ma.facultyid = ?
            ORDER BY ma.semester, m.materialname
        ");
        if (!$stmt) {
            error_log("Prepare failed in getFacultyCourses: " . $conn->error);
            if ($conn->errno) {
                error_log("MySQLi Prepare Error No: " . $conn->errno);
                error_log("MySQLi Prepare Error: " . $conn->error);
            }
            return ['status' => 'error', 'message' => 'Prepare statement failed in getFacultyCourses.'];
        }
        $stmt->bind_param("i", $facultyId);
        $stmt->execute();
        
        if ($stmt->errno) {
            error_log("MySQLi Execute Error No: " . $stmt->errno);
            error_log("MySQLi Execute Error: " . $stmt->error);
        }

        $result = $stmt->get_result();
        $courses = [];
        while ($row = $result->fetch_assoc()) {
            $courses[] = $row;
        }
        $stmt->close();
        return ['status' => 'success', 'data' => $courses];
    } catch (Exception $e) {
        error_log("Error fetching faculty courses: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error fetching faculty courses: ' . $e->getMessage()];
    }
}

/**
 * NEW: Function to fetch materials (courses) for a specific department.
 * @param mysqli $conn Database connection object.
 * @param int $departmentId The ID of the department.
 * @return array List of materials or error array.
 */
function getDepartmentMaterials($conn, $departmentId) {
    error_log("getDepartmentMaterials called with departmentId: " . $departmentId);
    try {
        $stmt = $conn->prepare("SELECT materialid, materialname FROM Material WHERE departmentid = ? ORDER BY materialname ASC");
        if (!$stmt) {
            error_log("Prepare failed in getDepartmentMaterials: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in getDepartmentMaterials.'];
        }
        $stmt->bind_param("i", $departmentId);
        $stmt->execute();
        $result = $stmt->get_result();
        $materials = [];
        while ($row = $result->fetch_assoc()) {
            $materials[] = $row;
        }
        $stmt->close();
        error_log("getDepartmentMaterials: Successfully fetched " . count($materials) . " materials.");
        return ['status' => 'success', 'data' => $materials];
    } catch (Exception $e) {
        error_log("Error fetching department materials: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error fetching department materials: ' . $e->getMessage()];
    }
}

/**
 * Function to toggle the Is_archived status of a material assignment.
 * @param mysqli $conn Database connection object.
 * @param int $materialId The ID of the material to update.
 * @param int $newStatus The new status (0 for unarchive, 1 for archive).
 * @return bool|array True on success, or an error array on failure.
 */
function toggleArchiveMaterial($conn, $materialId, $newStatus) {
    error_log("toggleArchiveMaterial: Called with MaterialID: " . $materialId . ", New Status: " . $newStatus);
    try {
        $stmt = $conn->prepare("UPDATE Material_Assignment SET Is_archived = ? WHERE materialid = ?");
        if (!$stmt) {
            error_log("toggleArchiveMaterial: Prepare failed: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed to toggle archive status.'];
        }
        $stmt->bind_param("ii", $newStatus, $materialId);
        $success = $stmt->execute();
        if (!$success) {
            error_log("toggleArchiveMaterial: Execute failed: " . $stmt->error);
            return ['status' => 'error', 'message' => 'Execute statement failed to toggle archive status.'];
        }
        $stmt->close();
        error_log("toggleArchiveMaterial: Status updated successfully for MaterialID: " . $materialId . " to " . $newStatus);
        return true;
    } catch (Exception $e) {
        error_log("Error toggling archive status: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error toggling archive status: ' . $e->getMessage()];
    }
}


/**
 * Function to fetch lectures for a specific assignment ID.
 * @param mysqli $conn Database connection object.
 * @param int $assignmentId The ID of the material assignment.
 * @return array List of lectures or error array.
 */
function getLecturesByAssignment($conn, $assignmentId) {
    error_log("getLecturesByAssignment called with assignmentId: " . $assignmentId);
    try {
        // Select the BLOB data and encode it to Base64 in the query
        $stmt = $conn->prepare("SELECT lectureid, title, TO_BASE64(file) as file_base64, uploaddate FROM Lecture WHERE assignmentid = ? ORDER BY uploaddate DESC");
        if (!$stmt) {
            error_log("Prepare failed in getLecturesByAssignment: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in getLecturesByAssignment.'];
        }
        error_log("getLecturesByAssignment: Prepared statement successfully.");
        $stmt->bind_param("i", $assignmentId);
        error_log("getLecturesByAssignment: Parameters bound.");
        $stmt->execute();
        error_log("getLecturesByAssignment: Execute command sent.");
        $result = $stmt->get_result();
        
        if ($result === false) {
            error_log("getLecturesByAssignment: get_result failed: " . $stmt->error);
            $stmt->close();
            return ['status' => 'error', 'message' => 'Failed to get result set from query.'];
        }
        error_log("getLecturesByAssignment: Got result set.");

        $lectures = [];
        while ($row = $result->fetch_assoc()) {
            $row['file'] = $row['file_base64'];
            unset($row['file_base64']);
            $lectures[] = $row;
        }
        $stmt->close();
        error_log("getLecturesByAssignment: Successfully fetched " . count($lectures) . " lectures.");
        return ['status' => 'success', 'data' => $lectures];
    } catch (Exception $e) {
        error_log("Error fetching lectures by assignment: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error fetching lectures by assignment: ' . $e->getMessage()];
    }
}

/**
 * Function to add a new course assignment for a faculty member.
 * This function now expects materialid directly.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @param int $materialId The ID of the material/course.
 * @param string $semester The semester string (e.g., "Spring 2025").
 * @return bool|array True on success, or an error array on failure.
 */
function addCourseAssignment($conn, $facultyId, $materialId, $semester) {
    error_log("addCourseAssignment: Called with FacultyID: " . $facultyId . ", MaterialID: " . $materialId . ", Semester: " . $semester);
    try {
        $conn->begin_transaction();

        // Check for duplicate assignment (same faculty, material, semester)
        $checkStmt = $conn->prepare("SELECT assignmentid FROM Material_Assignment WHERE facultyid = ? AND materialid = ? AND semester = ?");
        if (!$checkStmt) {
            error_log("addCourseAssignment: Prepare failed for duplicate check: " . $conn->error);
            throw new Exception("Prepare failed for duplicate check: " . $conn->error);
        }
        $checkStmt->bind_param("iis", $facultyId, $materialId, $semester);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        if ($checkResult->num_rows > 0) {
            $checkStmt->close();
            throw new Exception("This course is already assigned to this faculty for the specified semester.");
        }
        $checkStmt->close();
        error_log("addCourseAssignment: No duplicate assignment found.");

        // Insert into Material_Assignment table
        $stmt = $conn->prepare("INSERT INTO Material_Assignment (facultyid, materialid, semester) VALUES (?, ?, ?)");
        if (!$stmt) {
            error_log("addCourseAssignment: Prepare failed assignment insert: " . $conn->error);
            throw new Exception("Prepare failed assignment insert: " . $conn->error);
        }
        $stmt->bind_param("iis", $facultyId, $materialId, $semester);
        $success = $stmt->execute();
        if (!$success) {
            error_log("addCourseAssignment: Execute failed assignment insert: " . $stmt->error);
            throw new Exception("Execute failed assignment insert: " . $stmt->error);
        }
        $stmt->close();
        error_log("addCourseAssignment: Material assignment inserted successfully.");

        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        error_log("Error adding course assignment: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to add course assignment: ' . $e->getMessage()];
    }
}

/**
 * Function to delete a course assignment and all related lectures.
 * Since lecture files are stored as BLOBs in the database, no physical file deletion is needed.
 * @param mysqli $conn Database connection object.
 * @param int $assignmentId The ID of the material assignment to delete.
 * @return bool|array True on success, or an error array on failure.
 */
function deleteCourseAssignment($conn, $assignmentId) {
    try {
        $conn->begin_transaction();

        // 1. Delete associated lectures from the database
        $stmt = $conn->prepare("DELETE FROM Lecture WHERE assignmentid = ?");
        if (!$stmt) { throw new Exception("Prepare failed lecture delete: " . $conn->error); }
        $stmt->bind_param("i", $assignmentId);
        $stmt->execute();
        $stmt->close();

        // 2. Delete the material assignment itself
        $stmt = $conn->prepare("DELETE FROM Material_Assignment WHERE assignmentid = ?");
        if (!$stmt) { throw new Exception("Prepare failed assignment delete: " . $conn->error); }
        $stmt->bind_param("i", $assignmentId);
        $stmt->execute();
        $stmt->close();

        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        error_log("Error deleting course assignment: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to delete course assignment: ' . $e->getMessage()];
    }
}

/**
 * Function to add a new lecture.
 * The file content is stored directly as BLOB in the database.
 * @param mysqli $conn Database connection object.
 * @param int $assignmentId The ID of the material assignment this lecture belongs to.
 * @param string $title The title of the lecture.
 * @param string $fileBase64 The Base64 encoded file content.
 * @return bool|array True on success, or an error array on failure.
 */
function addLecture($conn, $assignmentId, $title, $fileBase64) {
    try {
        $decodedFile = base64_decode($fileBase64);
        if ($decodedFile === false) {
            throw new Exception("Failed to decode Base64 file content.");
        }

        $stmt = $conn->prepare("INSERT INTO Lecture (title, file, uploaddate, assignmentid) VALUES (?, ?, NOW(), ?)");
        if (!$stmt) {
            throw new Exception("Prepare failed for adding lecture: " . $conn->error);
        }
        $stmt->bind_param("ssi", $title, $decodedFile, $assignmentId);

        $success = $stmt->execute();
        if (!$success) {
            throw new Exception("Execute failed for adding lecture: " . $stmt->error);
        }
        $stmt->close();
        return true;
    } catch (Exception $e) {
        error_log("Error adding lecture: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to add lecture: ' . $e->getMessage()];
    }
}

/**
 * Function to update an existing lecture.
 * Handles file update if a new Base64 file is provided.
 * @param mysqli $conn Database connection object.
 * @param int $lectureId The ID of the lecture to update.
 * @param string $title The new title of the lecture.
 * @param string|null $fileBase64 The Base64 encoded new file content (if provided).
 * @return bool|array True on success, or an error array on failure.
 */
function updateLecture($conn, $lectureId, $title, $fileBase64 = null) {
    error_log("updateLecture called. Lecture ID: " . $lectureId . ", Title: " . $title . ", File provided: " . ($fileBase64 !== null ? 'Yes' : 'No'));
    try {
        $updateSql = "UPDATE Lecture SET title = ?, uploaddate = NOW() WHERE lectureid = ?";
        $types = "si"; // s for string (title), i for integer (lectureid)
        $params = [$title, $lectureId];

        // Handle file update if a new file is uploaded (Base64 string provided)
        if ($fileBase64 !== null && $fileBase64 !== '') {
            $decodedFile = base64_decode($fileBase64);
            if ($decodedFile === false) {
                throw new Exception("Failed to decode Base64 file content for lecture update.");
            }
            $updateSql = "UPDATE Lecture SET title = ?, file = ?, uploaddate = NOW() WHERE lectureid = ?";
            $types = "ssi"; // s (title), s (decodedFile - BLOB), i (lectureid)
            $params = [$title, $decodedFile, $lectureId];
        }

        $stmt = $conn->prepare($updateSql);
        if (!$stmt) {
            error_log("Prepare failed in updateLecture: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in updateLecture.'];
        }
        
        call_user_func_array(array($stmt, 'bind_param'), refValues($types, $params));

        $success = $stmt->execute();
        if (!$success) {
            error_log("Execute failed in updateLecture: " . $stmt->error);
            return ['status' => 'error', 'message' => 'Execute statement failed in updateLecture.'];
        }
        $stmt->close();
        error_log("Lecture updated successfully in DB. Lecture ID: " . $lectureId);
        return true;
    } catch (Exception $e) {
        error_log("Error updating lecture: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to update lecture: ' . $e->getMessage()];
    }
}


/**
 * Function to delete a lecture.
 * Since lecture file is stored as BLOB, no physical file deletion is needed.
 * @param mysqli $conn Database connection object.
 * @param int $lectureId The ID of the lecture to delete.
 * @return bool|array True on success, or an error array on failure.
 */
function deleteLecture($conn, $lectureId) {
    try {
        $conn->begin_transaction();

        $stmt = $conn->prepare("DELETE FROM Lecture WHERE lectureid = ?");
        if (!$stmt) { throw new Exception("Prepare failed lecture delete: " . $conn->error); }
        $stmt->bind_param("i", $lectureId);
        $stmt->execute();
        $stmt->close();

        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        error_log("Error deleting lecture: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to delete lecture: ' . $e->getMessage()];
    }
}

// =============================================================
// FUNCTIONS FOR ACADEMIC RESEARCH SECTION (MODIFIED FOR BLOB)
// =============================================================

/**
 * Function to fetch research for a specific faculty member.
 * Returns Base64 encoded file data.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @return array List of research or error array.
 */
function getFacultyResearch($conn, $facultyId) {
    error_log("getFacultyResearch called with facultyId: " . $facultyId);
    try {
        // SELECT TO_BASE64(file) as file_base64
        $stmt = $conn->prepare("SELECT researchid, title, TO_BASE64(file) as file_base64, Date_published FROM Research WHERE facultyid = ? ORDER BY Date_published DESC");
        if (!$stmt) {
            error_log("Prepare failed in getFacultyResearch: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in getFacultyResearch.'];
        }
        $stmt->bind_param("i", $facultyId);
        $stmt->execute();
        $result = $stmt->get_result();
        $researches = [];
        while ($row = $result->fetch_assoc()) {
            $row['file'] = $row['file_base64']; // Rename for consistency with JS
            unset($row['file_base64']);
            $researches[] = $row;
        }
        $stmt->close();
        error_log("getFacultyResearch: Successfully fetched " . count($researches) . " researches.");
        return ['status' => 'success', 'data' => $researches];
    } catch (Exception $e) {
        error_log("Error fetching faculty research: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Error fetching faculty research: ' . $e->getMessage()];
    }
}

/**
 * Function to add a new research entry.
 * The file content is stored directly as BLOB in the database.
 * @param mysqli $conn Database connection object.
 * @param int $facultyId The ID of the faculty member.
 * @param string $title The title of the research.
 * @param string $fileBase64 The Base64 encoded file content.
 * @param string $datePublished The publication date.
 * @param string $fileExtension The original file extension.
 * @return bool|array True on success, or an error array on failure.
 */
function addResearch($conn, $facultyId, $title, $fileBase64, $datePublished) {
    error_log("addResearch called. Title: " . $title);
    try {
        $decodedFile = base64_decode($fileBase64);
        if ($decodedFile === false) {
            throw new Exception("Failed to decode Base64 file content.");
        }

        $stmt = $conn->prepare("INSERT INTO Research (facultyid, title, file, Date_published) VALUES (?, ?, ?, ?)");
        if (!$stmt) {
            throw new Exception("Prepare failed for adding research: " . $conn->error);
        }
        $stmt->bind_param("isss", $facultyId, $title, $decodedFile, $datePublished);

        $success = $stmt->execute();
        if (!$success) {
            throw new Exception("Execute failed for adding research: " . $stmt->error);
        }
        $stmt->close();
        return true;
    } catch (Exception $e) {
        error_log("Error adding research: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to add research: ' . $e->getMessage()];
    }
}

/**
 * Function to update an existing research entry.
 * Handles file update if a new Base64 file is provided.
 * @param mysqli $conn Database connection object.
 * @param int $researchId The ID of the research to update.
 * @param string $title The new title of the research.
 * @param string $datePublished The new publication date.
 * @param string|null $fileBase64 The Base64 encoded new file content (if provided).
 * @return bool|array True on success, or an error array on failure.
 */
function updateResearch($conn, $researchId, $title, $datePublished, $fileBase64 = null) {
    error_log("updateResearch called. Research ID: " . $researchId . ", Title: " . $title . ", Date: " . $datePublished . ", File provided: " . ($fileBase64 !== null ? 'Yes' : 'No'));
    try {
        $updateSql = "UPDATE Research SET title = ?, Date_published = ? WHERE researchid = ?";
        $types = "ssi"; // s (title), s (date), i (researchid)
        $params = [$title, $datePublished, $researchId];

        // Handle file update if a new file is uploaded (Base64 string provided)
        if ($fileBase64 !== null && $fileBase64 !== '') {
            $decodedFile = base64_decode($fileBase64);
            if ($decodedFile === false) {
                throw new Exception("Failed to decode Base64 file content for research update.");
            }
            $updateSql = "UPDATE Research SET title = ?, file = ?, Date_published = ? WHERE researchid = ?";
            $types = "sssi"; // s (title), s (decodedFile - BLOB), s (date), i (researchid)
            $params = [$title, $decodedFile, $datePublished, $researchId];
        }

        $stmt = $conn->prepare($updateSql);
        if (!$stmt) {
            error_log("Prepare failed in updateResearch: " . $conn->error);
            return ['status' => 'error', 'message' => 'Prepare statement failed in updateResearch.'];
        }
        
        call_user_func_array(array($stmt, 'bind_param'), refValues($types, $params));

        $success = $stmt->execute();
        if (!$success) {
            error_log("Execute failed in updateResearch: " . $stmt->error);
            return ['status' => 'error', 'message' => 'Execute statement failed in updateResearch.'];
        }
        $stmt->close();
        error_log("Research updated successfully in DB. Research ID: " . $researchId);
        return true;
    } catch (Exception $e) {
        error_log("Error updating research: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to update research: ' . $e->getMessage()];
    }
}

/**
 * Function to delete a research entry.
 * Since research file is stored as BLOB, no physical file deletion is needed.
 * @param mysqli $conn Database connection object.
 * @param int $researchId The ID of the research to delete.
 * @return bool|array True on success, or an error array on failure.
 */
function deleteResearch($conn, $researchId) {
    try {
        $conn->begin_transaction();

        $stmt = $conn->prepare("DELETE FROM Research WHERE researchid = ?");
        if (!$stmt) { throw new Exception("Prepare failed research delete: " . $conn->error); }
        $stmt->bind_param("i", $researchId);
        $stmt->execute();
        $stmt->close();

        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        error_log("Error deleting research: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'Failed to delete research: ' . $e->getMessage()];
    }
}

// =============================================================
// LOGIN FUNCTIONALITY
// =============================================================

/**
 * Function to handle faculty member login.
 * @param mysqli $conn Database connection object.
 * @param string $username The username (e.g., email or unique identifier).
 * @param string $password The plain text password.
 * @return array A result array with status and message/facultyId.
 */
function loginFacultyMember($conn, $username, $password) {
    error_log("Attempting login for username: " . $username);
    try {
        // Prepare statement to prevent SQL injection
        // Assuming 'email' is used as the username for login in the Faculty table
        // IMPORTANT: In a real application, passwords should be hashed and verified using password_verify()
        // For demonstration, using plain text password comparison as per the table structure implies
        // but it's HIGHLY RECOMMENDED to hash passwords.
        $stmt = $conn->prepare("SELECT facultyid, email, password FROM Faculty WHERE email = ?");
        if (!$stmt) {
            error_log("Prepare failed in loginFacultyMember: " . $conn->error);
            return ['status' => 'error', 'message' => 'Database error during login preparation.'];
        }
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 1) {
            $faculty = $result->fetch_assoc();
            // In a real application, use password_verify($password, $faculty['password'])
            // Assuming plain text for demonstration based on the provided context (no hashing evident)
            if ($password === $faculty['password']) { // Replace with password_verify() if using hashed passwords
                // Login successful
                $_SESSION['loggedin'] = true;
                $_SESSION['faculty_id'] = $faculty['facultyid'];
                $_SESSION['faculty_email'] = $faculty['email']; // Store email if needed for display
                error_log("Login successful for facultyId: " . $faculty['facultyid']);
                return ['status' => 'success', 'message' => 'Login successful.', 'facultyId' => $faculty['facultyid']];
            } else {
                error_log("Login failed: Incorrect password for username: " . $username);
                return ['status' => 'error', 'message' => 'Incorrect password.'];
            }
        } else {
            error_log("Login failed: Username not found: " . $username);
            return ['status' => 'error', 'message' => 'Username not found.'];
        }
    } catch (Exception $e) {
        error_log("Error during login: " . $e->getMessage());
        return ['status' => 'error', 'message' => 'An error occurred during login: ' . $e->getMessage()];
    } finally {
        if (isset($stmt)) {
            $stmt->close();
        }
    }
}

// =============================================================
// API ENDPOINT HANDLING
// =============================================================

// Get the request method
$method = $_SERVER['REQUEST_METHOD'];
error_log("Request Method: " . $method);
error_log("Request URI: " . $_SERVER['REQUEST_URI']);

// Get the action from the URL path (e.g., /backend/api.php/login)
$request_uri_parts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
$action = end($request_uri_parts); // Gets the last part of the URL (e.g., "login", "getFacultyInfo")

// Ensure we are processing a POST request for login or a GET/POST for other actions that require authentication
// Login action does not require prior session authentication
if ($action === 'login' && $method === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
    $username = $data['username'] ?? '';
    $password = $data['password'] ?? '';

    $loginResult = loginFacultyMember($conn, $username, $password);
    ob_clean(); // Clear any previous output
    echo json_encode($loginResult);
    exit();
}

// For all other actions, check if the user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    ob_clean();
    http_response_code(401); // Unauthorized
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized: Please log in.']);
    exit();
}

// Get the facultyId from the session
$facultyId = $_SESSION['faculty_id'];
error_log("Authenticated Faculty ID: " . $facultyId);

// Decode JSON input for POST/PUT requests
$input = json_decode(file_get_contents('php://input'), true);
error_log("Received input: " . print_r($input, true));

$response = ['status' => 'error', 'message' => 'Invalid request.']; // Default response

try {
    switch ($action) {
        // --- Faculty Info ---
        case 'getFacultyInfo':
            if ($method === 'GET') {
                $response = getFacultyInfo($conn, $facultyId);
            } else {
                http_response_code(405); // Method Not Allowed
                $response = ['status' => 'error', 'message' => 'Method not allowed for getFacultyInfo.'];
            }
            break;
        case 'updatePersonalDetails':
            if ($method === 'POST') { // Or PUT
                $fullname = $input['fullname'] ?? null;
                $email = $input['email'] ?? null;
                $specialization = $input['specialization'] ?? null;
                $departmentid = $input['departmentid'] ?? null;

                if ($fullname && $email && $specialization && $departmentid !== null) {
                    $result = updateFacultyPersonalDetails($conn, $facultyId, $fullname, $email, $specialization, $departmentid);
                    if ($result === true) {
                        $response = ['status' => 'success', 'message' => 'Personal details updated successfully.'];
                    } else {
                        $response = $result; // Will contain error message from function
                    }
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing data for personal details update.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for updatePersonalDetails.'];
            }
            break;
        case 'updateDescription':
            if ($method === 'POST') { // Or PUT
                $description = $input['description'] ?? null;
                if ($description !== null) {
                    $result = updateFacultyDescription($conn, $facultyId, $description);
                    if ($result === true) {
                        $response = ['status' => 'success', 'message' => 'Description updated successfully.'];
                    } else {
                        $response = $result;
                    }
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing description data.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for updateDescription.'];
            }
            break;
        case 'updateImage':
            if ($method === 'POST') { // Or PUT
                $imageDataBase64 = $input['imageData'] ?? null;
                // Decode base64 to binary data before passing to function
                $imageDataBinary = $imageDataBase64 ? base64_decode($imageDataBase64) : null;
                
                if ($imageDataBinary !== null || ($imageDataBase64 === null && isset($input['imageData']))) { // Allow setting to null
                    $result = updateFacultyImage($conn, $facultyId, $imageDataBinary);
                    if ($result === true) {
                        $response = ['status' => 'success', 'message' => 'Profile image updated successfully.'];
                    } else {
                        $response = $result;
                    }
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing or invalid image data.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for updateImage.'];
            }
            break;
        case 'updateCertificateImage':
            if ($method === 'POST') { // Or PUT
                $imageDataBase64 = $input['imageData'] ?? null;
                // Decode base64 to binary data before passing to function
                $imageDataBinary = $imageDataBase64 ? base64_decode($imageDataBase64) : null;
                
                if ($imageDataBinary !== null || ($imageDataBase64 === null && isset($input['imageData']))) { // Allow setting to null
                    $result = updateFacultyCertificateImage($conn, $facultyId, $imageDataBinary);
                    if ($result === true) {
                        $response = ['status' => 'success', 'message' => 'Certificate image updated successfully.'];
                    } else {
                        $response = $result;
                    }
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing or invalid certificate image data.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for updateCertificateImage.'];
            }
            break;

        // --- Departments ---
        case 'getAllDepartments':
            if ($method === 'GET') {
                $response = getAllDepartments($conn);
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for getAllDepartments.'];
            }
            break;
        
        // --- Courses ---
        case 'getFacultyCourses':
            if ($method === 'GET') {
                $response = getFacultyCourses($conn, $facultyId);
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for getFacultyCourses.'];
            }
            break;
        case 'getDepartmentMaterials':
            if ($method === 'GET') {
                $departmentId = $_GET['departmentId'] ?? null;
                if ($departmentId !== null) {
                    $response = getDepartmentMaterials($conn, $departmentId);
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing departmentId for getDepartmentMaterials.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for getDepartmentMaterials.'];
            }
            break;
        case 'addCourseAssignment':
            if ($method === 'POST') {
                $materialId = $input['materialId'] ?? null;
                $semester = $input['semester'] ?? null;
                if ($materialId !== null && $semester !== null) {
                    $result = addCourseAssignment($conn, $facultyId, $materialId, $semester);
                    if ($result === true) {
                        $response = ['status' => 'success', 'message' => 'Course assigned successfully.'];
                    } else {
                        $response = $result;
                    }
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing data for course assignment.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for addCourseAssignment.'];
            }
            break;
        case 'deleteCourseAssignment':
            if ($method === 'POST') { // Or DELETE
                $assignmentId = $input['assignmentId'] ?? null;
                if ($assignmentId !== null) {
                    $result = deleteCourseAssignment($conn, $assignmentId);
                    if ($result === true) {
                        $response = ['status' => 'success', 'message' => 'Course assignment deleted successfully.'];
                    } else {
                        $response = $result;
                    }
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing assignmentId for course assignment deletion.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for deleteCourseAssignment.'];
            }
            break;
        case 'toggleArchiveMaterial':
            if ($method === 'POST') { // Or PUT
                $materialId = $input['materialId'] ?? null;
                $newStatus = $input['newStatus'] ?? null;
                if ($materialId !== null && ($newStatus === 0 || $newStatus === 1)) {
                    $result = toggleArchiveMaterial($conn, $materialId, $newStatus);
                    if ($result === true) {
                        $response = ['status' => 'success', 'message' => 'Material archive status toggled successfully.'];
                    } else {
                        $response = $result;
                    }
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing or invalid data for toggling archive status.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for toggleArchiveMaterial.'];
            }
            break;

        // --- Lectures ---
        case 'getLecturesByAssignment':
            if ($method === 'GET') {
                $assignmentId = $_GET['assignmentId'] ?? null;
                if ($assignmentId !== null) {
                    $response = getLecturesByAssignment($conn, $assignmentId);
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing assignmentId for getLecturesByAssignment.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for getLecturesByAssignment.'];
            }
            break;
        case 'addLecture':
            if ($method === 'POST') {
                $assignmentId = $input['assignmentId'] ?? null;
                $title = $input['title'] ?? null;
                $fileBase64 = $input['fileBase64'] ?? null;
                if ($assignmentId !== null && $title !== null && $fileBase64 !== null) {
                    $result = addLecture($conn, $assignmentId, $title, $fileBase64);
                    if ($result === true) {
                        $response = ['status' => 'success', 'message' => 'Lecture added successfully.'];
                    } else {
                        $response = $result;
                    }
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing data for adding lecture.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for addLecture.'];
            }
            break;
        case 'updateLecture':
            if ($method === 'POST') { // Or PUT
                $lectureId = $input['lectureId'] ?? null;
                $title = $input['title'] ?? null;
                $fileBase64 = $input['fileBase64'] ?? null; // Optional
                if ($lectureId !== null && $title !== null) {
                    $result = updateLecture($conn, $lectureId, $title, $fileBase64);
                    if ($result === true) {
                        $response = ['status' => 'success', 'message' => 'Lecture updated successfully.'];
                    } else {
                        $response = $result;
                    }
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing data for updating lecture.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for updateLecture.'];
            }
            break;
        case 'deleteLecture':
            if ($method === 'POST') { // Or DELETE
                $lectureId = $input['lectureId'] ?? null;
                if ($lectureId !== null) {
                    $result = deleteLecture($conn, $lectureId);
                    if ($result === true) {
                        $response = ['status' => 'success', 'message' => 'Lecture deleted successfully.'];
                    } else {
                        $response = $result;
                    }
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing lectureId for lecture deletion.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for deleteLecture.'];
            }
            break;

        // --- Research ---
        case 'getFacultyResearch':
            if ($method === 'GET') {
                $response = getFacultyResearch($conn, $facultyId);
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for getFacultyResearch.'];
            }
            break;
        case 'addResearch':
            if ($method === 'POST') {
                $title = $input['title'] ?? null;
                $fileBase64 = $input['fileBase64'] ?? null;
                $datePublished = $input['datePublished'] ?? null;
                // $fileExtension = $input['fileExtension'] ?? null; // No longer needed if storing BLOB directly
                if ($title !== null && $fileBase64 !== null && $datePublished !== null) {
                    $result = addResearch($conn, $facultyId, $title, $fileBase64, $datePublished);
                    if ($result === true) {
                        $response = ['status' => 'success', 'message' => 'Research added successfully.'];
                    } else {
                        $response = $result;
                    }
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing data for adding research.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for addResearch.'];
            }
            break;
        case 'updateResearch':
            if ($method === 'POST') { // Or PUT
                $researchId = $input['researchId'] ?? null;
                $title = $input['title'] ?? null;
                $datePublished = $input['datePublished'] ?? null;
                $fileBase64 = $input['fileBase64'] ?? null; // Optional
                if ($researchId !== null && $title !== null && $datePublished !== null) {
                    $result = updateResearch($conn, $researchId, $title, $datePublished, $fileBase64);
                    if ($result === true) {
                        $response = ['status' => 'success', 'message' => 'Research updated successfully.'];
                    } else {
                        $response = $result;
                    }
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing data for updating research.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for updateResearch.'];
            }
            break;
        case 'deleteResearch':
            if ($method === 'POST') { // Or DELETE
                $researchId = $input['researchId'] ?? null;
                if ($researchId !== null) {
                    $result = deleteResearch($conn, $researchId);
                    if ($result === true) {
                        $response = ['status' => 'success', 'message' => 'Research deleted successfully.'];
                    } else {
                        $response = $result;
                    }
                } else {
                    $response = ['status' => 'error', 'message' => 'Missing researchId for research deletion.'];
                }
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for deleteResearch.'];
            }
            break;
        case 'logout': // Add a logout action
            if ($method === 'POST') {
                session_unset();    // Unset all session variables
                session_destroy();  // Destroy the session
                $response = ['status' => 'success', 'message' => 'Logged out successfully.'];
            } else {
                http_response_code(405);
                $response = ['status' => 'error', 'message' => 'Method not allowed for logout.'];
            }
            break;

        default:
            http_response_code(400); // Bad Request
            $response = ['status' => 'error', 'message' => 'Unknown action requested: ' . $action];
            break;
    }
} catch (Exception $e) {
    http_response_code(500); // Internal Server Error
    error_log("Unhandled exception in API: " . $e->getMessage());
    $response = ['status' => 'error', 'message' => 'An unexpected server error occurred: ' . $e->getMessage()];
}

// Close the database connection
$conn->close();
error_log("Database connection closed.");

// Send the JSON response
ob_clean(); // Clear any buffered output before sending final JSON
echo json_encode($response);
error_log("Response sent. Script finished.");

exit(); // Ensure no further code is executed
?>