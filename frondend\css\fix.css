/* إصلاح شامل للشريط الجانبي والفوتر */

/* إصلاح body */
body {
    display: flex !important;
    flex-direction: column !important;
    min-height: 100vh !important;
    padding-top: 60px !important;
    padding-bottom: 0 !important;
}

/* إصلاح الشريط الجانبي */
.sidebar {
    position: fixed !important;
    left: 0 !important;
    top: 60px !important;
    bottom: 0 !important;
    width: 200px !important;
    height: calc(100vh - 60px) !important;
    background: #34495e !important;
    color: white !important;
    display: flex !important;
    flex-direction: column !important;
    padding: 20px 0 !important;
    overflow-y: auto !important;
    z-index: 1000 !important;
}

/* إصلاح قائمة الشريط الجانبي */
.sidebar-nav {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    list-style: none !important;
    padding: 0 15px !important;
    margin: 0 !important;
}

/* إصلاح عناصر القائمة */
.sidebar-nav li {
    display: block !important;
    margin-bottom: 8px !important;
}

/* إصلاح روابط القائمة */
.sidebar-nav a {
    display: flex !important;
    align-items: center !important;
    padding: 12px 15px !important;
    color: #bdc3c7 !important;
    text-decoration: none !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

.sidebar-nav a:hover {
    background-color: rgba(255,255,255,0.15) !important;
    color: #ecf0f1 !important;
}

/* إصلاح زر تسجيل الخروج */
.logout-item {
    margin-top: auto !important;
    padding-top: 20px !important;
    margin-bottom: 20px !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.logout-link {
    color: #fd2626 !important;
    background-color: rgba(255, 107, 107, 0.1) !important;
    border: 1px solid rgba(255, 107, 107, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 12px 15px !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
}

.logout-link:hover {
    background-color: rgba(255, 107, 107, 0.2) !important;
    color: #ff5252 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(255, 107, 107, 0.2) !important;
}

.logout-link::before {
    content: "⟲ " !important;
    margin-right: 5px !important;
    font-size: 16px !important;
}

/* إصلاح المحتوى الرئيسي */
.main-content {
    margin-left: 200px !important;
    flex: 1 !important;
    padding: 20px !important;
}

/* إصلاح الفوتر */
.footer {
    position: static !important;
    margin-left: 200px !important;
    margin-top: auto !important;
    background-color: #4b4a4a !important;
    color: white !important;
    padding: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: left !important;
    gap: 20px !important;
    flex-wrap: wrap !important;
    text-align: center !important;
}
