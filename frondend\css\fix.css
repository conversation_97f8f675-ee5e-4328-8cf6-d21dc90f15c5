/* إصلاح شامل للشريط الجانبي والفوتر */

/* إصلاح body */
body {
    display: flex !important;
    flex-direction: column !important;
    min-height: 100vh !important;
    padding-top: 60px !important;
    padding-bottom: 0 !important;
}

/* إصلاح الشريط الجانبي */
.sidebar {
    position: fixed !important;
    left: 0 !important;
    top: 60px !important;
    bottom: 0 !important;
    width: 200px !important;
    height: calc(100vh - 60px) !important;
    background: #34495e !important;
    color: white !important;
    display: flex !important;
    flex-direction: column !important;
    padding: 20px 0 20px 0 !important; /* مساحة متساوية في الأعلى والأسفل */
    overflow-y: auto !important;
    z-index: 1000 !important;
}

/* إصلاح قائمة الشريط الجانبي */
.sidebar-nav {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    list-style: none !important;
    padding: 0 15px !important;
    margin: 0 !important;
}

/* إصلاح عناصر القائمة */
.sidebar-nav li {
    display: block !important;
    margin-bottom: 8px !important;
}

/* إصلاح روابط القائمة */
.sidebar-nav a {
    display: flex !important;
    align-items: center !important;
    padding: 12px 15px !important;
    color: #bdc3c7 !important;
    text-decoration: none !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

.sidebar-nav a:hover {
    background-color: rgba(255,255,255,0.15) !important;
    color: #ecf0f1 !important;
}

/* إضافة مساحة فارغة في أسفل الشريط الجانبي بدلاً من زر تسجيل الخروج */
.sidebar ul {
    padding-bottom: 40px !important; /* مساحة فارغة في الأسفل */
}

/* إصلاح المحتوى الرئيسي - مع دعم الرسائل */
.main-content {
    margin-left: 200px !important;
    flex: 1 !important;
    padding: 20px !important;
    position: relative !important; /* مهم لدعم position: absolute للرسائل */
    height: 100% !important; /* ارتفاع كامل للرسائل */
    overflow-y: auto !important; /* تمرير عند الحاجة */
}

/* عندما تكون الرسائل مرئية، إزالة padding من المحتوى الرئيسي */
.main-content:has(#messages-section[style*="display: block"]) {
    padding: 0 !important;
}

/* أو استخدام طريقة أخرى للتحكم في padding عند عرض الرسائل */
.main-content.messages-active {
    padding: 0 !important;
}

/* إصلاح الفوتر */
.footer {
    position: static !important;
    margin-left: 200px !important;
    margin-top: auto !important;
    background-color: #4b4a4a !important;
    color: white !important;
    padding: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: left !important;
    gap: 20px !important;
    flex-wrap: wrap !important;
    text-align: center !important;
}

/* إصلاح صفحة الرسائل لإزالة المساحة البيضاء */
#messages-section {
    /* عندما يكون مرئي، يجب أن يملأ المساحة بالكامل */
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
}

#messages-section.section {
    /* إزالة أي padding أو margin قد يسبب المساحة البيضاء */
    padding: 0 !important;
    margin: 0 !important;
}

#messagesFrame {
    /* التأكد من أن iframe يملأ المساحة بالكامل */
    width: 100% !important;
    height: 100% !important;
    border: none !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* إصلاح div.iframe داخل messages-section */
#messages-section .iframe {
    width: 100% !important;
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    box-sizing: border-box !important;
}

/* إزالة padding من main-content عندما تكون الرسائل مرئية */
.main-content:has(#messages-section[style*="display: block"]) {
    padding: 0 !important;
}

/* طريقة بديلة - عندما يكون messages-section مرئي */
.main-content[data-messages-active="true"] {
    padding: 0 !important;
}
