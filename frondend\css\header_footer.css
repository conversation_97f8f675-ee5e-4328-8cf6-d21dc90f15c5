.hero-section {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(to right, #c0bfbf, #a9a9a9); /* تدرج رمادي */
  color: white;
  z-index: 1000;
  padding: 10px 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}
.overlay {
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  padding: 0 10px;
}
.logo {
  display: flex;
  align-items: center;
  font-size: 20px;
}
.logo img {
  height: 60px;
  margin-right: 20px;
}
.logo span {
  font-size: 18px;
  line-height: 1.2;
  color: white;
}
.nav-links {
  display: flex;
  gap: 50px;
}
.nav-links a {
  color: white;
  text-decoration: none;
  font-weight: bold;
}
.nav-search a {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 10px 20px;
  background-color: #a9a9a9;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  font-size: 18px;
  color: white;
  text-decoration: none;
  margin-left: 10px;
  margin-right: 30px;
  font-weight: bold;
  transition: background-color 0.3s ease, transform 0.2s ease;
}
.nav-search a:hover {
  background-color: #616161;
  transform: scale(1.05);
}
.footer {
  background-color: #4b4a4a !important;
  color: white;
  padding: 10px 20px; /* تقليل الارتفاع من 20px إلى 10px */
  display: flex;
  align-items: center;
  justify-content: center; /* توسيط المحتوى */
  gap: 20px;
  flex-wrap: wrap;
  text-align: center;
  width: 100%; /* عرض كامل */
  position: relative;
  bottom: 0;
  margin-top: auto; /* دفع الفوتر إلى الأسفل */
}
.footer-text {
  font-size: 14px; /* تصغير النص */
  margin-right: 20px; /* تقليل المسافة */
}
.footer-button {
  color: white;
  text-decoration: none;
  font-size: 16px; /* تصغير الأيقونات */
  margin-left: 15px; /* تقليل المسافة */
  transition: color 0.3s;
  padding: 5px; /* إضافة padding للأيقونات */
}
.footer-button:hover {
  color: #4da6ff;
}

/* تنسيقات متجاوبة للفوتر */
@media (max-width: 768px) {
  .footer {
    padding: 8px 15px; /* تقليل أكثر للشاشات الصغيرة */
    gap: 15px;
    flex-direction: row; /* الحفاظ على الترتيب الأفقي */
    justify-content: center;
  }

  .footer-text {
    font-size: 12px; /* نص أصغر للشاشات الصغيرة */
    margin-right: 10px;
  }

  .footer-button {
    font-size: 14px; /* أيقونات أصغر */
    margin-left: 10px;
    padding: 3px;
  }
}


