.hero-section {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: #757577;
  color: white;
  z-index: 1000;
  padding: 5px 20px; /* تم التقليص هنا */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}
.overlay {
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  padding: 0 10px;
}
.logo {
  display: flex;
  align-items: center;
  font-size: 20px;
}
.logo img {
  height: 30px; /* تم التقليص هنا */
  margin-right: 20px;
}
.logo span {
  font-size: 14px; /* تم التقليص هنا */
  line-height: 1.2;
  color: white;
}
.nav-links {
  display: flex;
  gap: 50px;
}
.nav-links a {
  color: white;
  text-decoration: none;
  font-weight: bold;
  font-size: 15px; /* تم التقليص هنا */
}
.nav-search a {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 15px; /* تم التقليص هنا */
  background-color: #757577;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  font-size: 16px; /* تم التقليص هنا */
  color: white;
  text-decoration: none;
  margin-left: 10px;
  margin-right: 30px;
  font-weight: bold;
  transition: background-color 0.3s ease, transform 0.2s ease;
}
.nav-search a:hover {
  background-color: #616161;
  transform: scale(1.05);
}
.footer {
  background-color: #757577 !important;
  color: white;
  padding: 5px 20px; /* تم التقليص هنا */
  display: flex;
  align-items: center;
  justify-content: left;
  gap: 15px;
  flex-wrap: wrap;
  text-align: center;
}
.footer-text {
  font-size: 12px; /* تم التقليص هنا */
  margin-right: 30px;
}
.footer-button {
  color: white;
  text-decoration: none;
  font-size: 16px; /* تم التقليص هنا */
  margin-left: 30px;
  transition: color 0.3s;
}
.footer-button:hover {
  color: #4da6ff;
}