.hero-section {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(to right, #c0bfbf, #a9a9a9); /* تدرج رمادي */
  color: white;
  z-index: 1000;
  padding: 10px 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}
.overlay {
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  padding: 0 10px;
}
.logo {
  display: flex;
  align-items: center;
  font-size: 20px;
}
.logo img {
  height: 60px;
  margin-right: 20px;
}
.logo span {
  font-size: 18px;
  line-height: 1.2;
  color: white;
}
.nav-links {
  display: flex;
  gap: 50px;
}
.nav-links a {
  color: white;
  text-decoration: none;
  font-weight: bold;
}
.nav-search a {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 10px 20px;
  background-color: #a9a9a9;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  font-size: 18px;
  color: white;
  text-decoration: none;
  margin-left: 10px;
  margin-right: 30px;
  font-weight: bold;
  transition: background-color 0.3s ease, transform 0.2s ease;
}
.nav-search a:hover {
  background-color: #616161;
  transform: scale(1.05);
}
#footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
}

.footer {
  background-color: #4b4a4a !important;
  color: white;
  padding: 15px 0; /* ارتفاع مناسب */
  display: flex;
  align-items: center;
  justify-content: center; /* توسيط المحتوى */
  gap: 30px; /* مسافة أكبر بين الأيقونات */
  flex-wrap: wrap;
  text-align: center;
  width: 100%; /* عرض كامل */
  margin: 0; /* إزالة أي هوامش */
}
.footer-button {
  color: white !important;
  text-decoration: none;
  font-size: 24px; /* حجم أكبر للأيقونات */
  transition: all 0.3s ease;
  padding: 8px; /* padding للأيقونات */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px; /* زوايا مدورة خفيفة */
}

.footer-button i {
  color: white !important;
  font-size: 24px;
}
.footer-button:hover {
  color: #4da6ff;
  transform: scale(1.1); /* تكبير خفيف عند التحويم */
}

/* إضافة مساحة للمحتوى لتجنب تداخل الفوتر */
body {
  padding-bottom: 70px; /* مساحة للفوتر */
}

/* تأكد من أن الفوتر يظهر على طول الشاشة */
html, body {
  overflow-x: hidden; /* منع التمرير الأفقي */
}

#footer .footer {
  min-width: 100vw; /* ضمان العرض الكامل */
}

/* تنسيقات متجاوبة للفوتر */
@media (max-width: 768px) {
  .footer {
    padding: 12px 0; /* ارتفاع مناسب للشاشات الصغيرة */
    gap: 25px; /* مسافة مناسبة بين الأيقونات */
  }

  .footer-button {
    font-size: 20px; /* أيقونات أصغر قليلاً للشاشات الصغيرة */
    padding: 6px;
  }

  .footer-button i {
    font-size: 20px;
  }

  body {
    padding-bottom: 60px; /* مساحة أقل للفوتر على الشاشات الصغيرة */
  }
}


