<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messages</title>
    <link rel="stylesheet" href="css/masege.css">
    
</head>
<body>
    <div class="container">
        <h1>Messages</h1>

        <div class="buttons-container">
            <button class="btn" id="newMessageBtn" onclick="setActiveButton(this); openModal();">New Message</button>
            <button class="btn" id="inboxBtn" onclick="setActiveButton(this); toggleView('inbox');">Inbox</button>
            <button class="btn" id="sentBtn" onclick="setActiveButton(this); fetchMessages('sent');">Sent Messages</button>
            <button class="btn btn-delete" id="deleteSelectedBtn" style="display: block;" onclick="confirmDeleteSelected()">Delete Selected</button>
        </div>

        <table id="messagesTable">
            <thead>
                <tr>
                    <th><input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll(this)"></th>
                    <th>#</th>
                    <th>Name/To</th>
                    <th>Email</th>
                    <th>Subject</th>
                    <th>Date Sent</th>
                </tr>
            </thead>
            <tbody>
            </tbody>
        </table>
    </div>

    <!-- New Message Modal -->
    <!-- نافذة الرسالة الجديدة المنبثقة -->
    <div id="messageModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>Send New Message</h2>
            <form id="newMessageForm" onsubmit="sendMessage(event)">
                <label for="fromEmail">From:</label>
                <input type="email" id="fromEmail" value="<EMAIL>" readonly />

                <label for="toEmail">To:</label>
                <input type="email" id="toEmail" required />

                <label for="subject">Subject:</label>
                <input type="text" id="subject" required />

                <label for="messageContent">Message Content:</label>
                <textarea id="messageContent" rows="4" required></textarea>
                
                <button type="submit" class="btn btn-active">Send</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
            </form>
        </div>
    </div>


    <!-- نافذة عرض الرسالة المنبثقة -->
    <div id="viewMessageModal" class="view-message-overlay">
        <div class="view-message-content">
            <span class="close" onclick="closeViewMessageModal()">&times;</span>
            <h3>Message Details</h3>
            <div class="message-detail">
                <strong>From:</strong> <span id="viewFromName"></span>
            </div>
            <div class="message-detail">
                <strong>Email:</strong> <span id="viewFromEmail"></span>
            </div>
            <div class="message-detail">
                <strong>Subject:</strong> <span id="viewSubject"></span>
            </div>
            <div class="message-detail">
                <strong>Date:</strong> <span id="viewDate"></span>
            </div>
            <div class="message-detail content-detail">
                <strong>Content:</strong>
                <pre id="viewContent"></pre>
            </div>
            <div style="text-align: center; margin-top: 20px;"> <!-- NEW: Container for buttons -->
                <!-- جديد: حاوية للأزرار -->
                <button class="btn" onclick="replyToMessage(currentViewingMessage)">Reply</button>
                <button class="btn" onclick="closeViewMessageModal()">Close</button>
            </div>
        </div>
    </div>

    <!-- مربع الرسائل المخصص (للتنبيهات) -->
    <div id="customMessageBox" class="message-box-overlay">
        <div class="message-box-content">
            <h3 id="messageBoxTitle"></h3>
            <p id="messageBoxContent"></p>
            <button class="btn" onclick="closeMessageBox()">OK</button>
        </div>
    </div>


    <!-- مربع تأكيد الحذف -->
    <div id="confirmDeleteBox" class="confirm-box-overlay">
        <div class="confirm-box-content">
            <h3>Confirm Deletion</h3>
            <p>Are you sure you want to delete the selected message(s)? This action cannot be undone.</p>
            <button class="btn btn-confirm" onclick="deleteSelectedMessages()">Delete</button>
            <button type="button" class="btn btn-cancel" onclick="closeConfirmDeleteBox()">Cancel</button>
        </div>
    </div>


<script src="javascript/masege.js"></script>

</body>
</html>
