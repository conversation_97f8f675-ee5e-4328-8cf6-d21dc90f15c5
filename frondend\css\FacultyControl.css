/* CSS Styles for the sidebar and main content */
body {
    font-family: 'Arial', sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0; /* Removed padding-top and padding-bottom related to header/footer */
    color: #000;
    display: flex; /* Use flexbox for the body */
    flex-direction: column; /* Stack header, main content area, footer vertically */
    min-height: 100vh; /* Ensures body takes full viewport height */
}

/* NEW: Wrapper for sidebar and main content to manage horizontal layout */
.main-layout-wrapper {
    display: flex; /* Arrange sidebar and main content horizontally */
    flex: 1; /* Allow this wrapper to grow and take available vertical space */
    /* This wrapper will now define the area where sidebar and main-content live */
}

/* Sidebar Styles */
.sidebar {
    width: 200px;
    background: #34495e; /* Changed to a solid dark blue-grey color */
    color: white;
    position: fixed; /* Keep sidebar fixed */
    left: 0;
    top: 0; /* Adjusted to start from the very top */
    bottom: 0; /* Adjusted to extend to the very bottom */
    height: auto; /* Allow height to be determined by top and bottom */
    padding: 20px 0;
    box-shadow: none; /* Removed box-shadow */
    transition: transform 0.3s ease-in-out;
    z-index: 1000; /* Sidebar z-index */
    border-top-right-radius: 0; /* Removed rounded corner */
    border-bottom-right-radius: 0; /* Removed rounded corner */
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    text-align: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: bold;
    text-shadow: none; /* Removed text-shadow */
}

.sidebar-nav {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    list-style: none;
    padding: 0 15px;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 8px;
}

.sidebar-nav a {
    color: #bdc3c7;
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.sidebar-nav a:hover {
    background-color: rgba(255,255,255,0.15);
    transform: none; /* Removed slide on hover */
    color: #ecf0f1;
}

.sidebar-nav a.active {
    background-color: #3498db;
    font-weight: bold;
    box-shadow: none; /* Removed shadow for active link */
    color: white;
    transform: none; /* Reset transform for active state */
    border-right: none; /* Removed border-right highlight */
}

/* Main Content Styles */
.main-content {
    flex: 1; /* Allow main content to grow horizontally within main-layout-wrapper */
    margin-left: 200px; /* Push content right by sidebar width */
    padding: 20px;
    transition: margin-left 0.3s ease-in-out;
    box-sizing: border-box; /* Include padding in element's total width and height */
    /* Set a height for main-content so its internal content can scroll */
    height: 100%; /* Make main-content fill the height of main-layout-wrapper */
    overflow-y: auto; /* Enable scrolling for main content */
    position: relative; /* Make it a positioning context for absolute children like messages-section */
    /* Removed margin-top here as body padding should handle it */
}

h1, h2 {
    text-align: center;
    color: #000;
    margin-bottom: 20px;
}

.container {
    max-width: 800px;
    margin: auto; /* This will center it within the main-content */
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    color: #000;
}

.section {
    margin-bottom: 30px;
    scroll-margin-top: 20px;
}

.card {
    background: #e9ecef;
    padding: 20px;
    margin: 10px 0;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 20px;
    color: #000;
}

.profile-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 150px;
}

.profile-container {
    width: 120px; /* Adjusted width for image */
    height: 120px; /* Adjusted height for image */
    border: 3px solid #007bff;
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f8f9fa;
}

.profile-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.info {
    flex: 1;
    text-align: left;
}

.info p {
    margin: 6px 0; /* Reduced margin here for less spacing */
    font-size: 16px; /* Font size for better balance */
    line-height: 1.6;
    color: #000;
}

.info strong {
    color: #000;
    font-weight: bold;
}

input, select {
    width: calc(100% - 22px);
    padding: 10px;
    margin: 8px 0;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    color: #000;
}

.info input[type="text"] {
    width: calc(100% - 22px);
    padding: 8px;
    font-size: 16px; /* Adjusted input font size as well */
    margin-bottom: 5px;
}

button {
    background: #1e88e5;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
    margin-top: 10px;
    margin-right: 5px;
}

button:hover {
    background: #1565c0;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* Specific button styling for the profile section */
.profile-section button {
    padding: 8px 14px; /* Adjusted padding for consistent button size */
    font-size: 13px; /* Adjusted font size for consistent button size */
}

/* Specific button styling for the info section within personal-info */
.personal-info .info .button-group button {
    padding: 8px 14px; /* Adjusted padding for consistent button size */
    font-size: 13px; /* Adjusted font size for consistent button size */
}

/* Styling for the new description and certificate section */
.doctor-description .card {
    flex-direction: row-reverse; /* Modified to keep image in place */
    align-items: flex-start;
    text-align: right; /* Align text right for Arabic */
}

.doctor-description .description-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-end; /* Align description content to the right */
    padding: 20px;
    min-height: 250px; /* Ensure minimum height */
}

/* Container to manage height and prevent layout shifts */
.description-text-area-wrapper {
    width: 100%;
    min-height: 200px; /* Increased minimum height */
    position: relative;
    overflow: hidden; /* Hide overflow during transitions */
    /* لا يوجد انتقال للارتفاع هنا لأن الارتفاع ثابت بالحد الأدنى */
    font-family: 'Arial', sans-serif;
    font-size: 16;
    line-height: 2;
}

/* Styles for the text display and textarea */
.doctor-description .description-display-text,
.doctor-description .description-edit-textarea {
    margin: 0;
    font-size: 14px; /* Increased font size for better readability */
    line-height: 1.5;
    color: #333;
    text-align:left;
    box-sizing: border-box; /* Include padding/border in dimensions */
    width: 100%; /* Changed from 200% to 100% to prevent overflow */
    position: absolute;
    height: 100%;
    min-height: 200px; /* Increased minimum height */
    padding: 15px; /* Added padding for better appearance */
    border: 2px solid transparent; /* Added for consistent layout */
    /* الانتقال هنا لخاصية الشفافية فقط */
    transition: all 0.3s ease;
}

/* Default state: display text visible, textarea hidden */
.doctor-description .description-display-text {
    display: block; /* افتراض مرئي */
    opacity: 1; /* افتراض شفافية كاملة */
    background: #f9f9f9;
    border: 2px solid #eee;
    font-size: 16px;
    line-height: 1.5;
    border-radius: 8px;
}
.doctor-description .description-edit-textarea {
    display: none; /* مخفي مبدئي */
    opacity: 0; /* شفافية صفرية مبدئي */
    min-height: 200px; /* Ensure a minimum height for textarea */
    border: 2px solid #1e88e5; /* More prominent border */
    border-radius: 8px;
    padding: 15px; /* Specific padding for textarea */
    resize: vertical; /* Allow vertical resizing */
    box-shadow: 0 4px 15px rgba(30, 136, 229, 0.2); /* Added shadow for depth */
    font-size: 16px; /* Larger font for editing */
    line-height: 1.5;
    text-align: left;
    background: #fff;
}

.doctor-description .description-edit-textarea:focus {
    border: 2px solid #007bff; /* More prominent border when focused */
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.4); /* Add subtle shadow for better look */
    outline: none; /* Remove default focus outline */
}

/* تعديل بسيط لحاوية الصورة */
.doctor-description .description-image-wrapper {
    width: 200px; /* Changed from 200% to 40% for better layout */
    min-width: 200px; /* Ensure a minimum width for the image wrapper */
    max-width: 300px; /* Ensure a maximum width for the image wrapper */
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* تعديل حاوية الشهادة */
.doctor-description .certificate-container {
    width: 100px; /* حجم ثابت للحاوية */
    height: 100px;    /* حجم ثابت للحاوية */
    max-width: 100px; /* لضمان عدم تجاوز هذا الحجم */
    max-height: 100px; /* لضمان عدم تجاوز هذا الحجم */
    border: 2px solid #007bff;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: white;
    margin: 0 auto; /* توسيط الحاوية داخل الـ 40% المخصصة لها */
}

/* تعديل صورة الشهادة */
.doctor-description .certificate-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain; /* تأكد من احتواء الصورة داخل الحاوية مع الحفاظ على الأبعاد */
    display: block; /* للتأكد من عدم وجود مسافات إضافية */
}

/* إضافة تأثير تكبير عند التحويم */
.doctor-description .certificate-container:hover .certificate-img {
    transform: scale(1.05);
}

/* تعديل زر تحميل الصورة */
.doctor-description .description-image-wrapper .secondary-btn {
    width: 100%; /* عرض كامل */
    padding: 12px 0; /* تباعد عمودي فقط */
    font-size: 15px;
    text-align: center;
}

.doctor-description .button-group {
    display: flex;
    gap: 10px;
    margin-top: 15px; /* Adjust margin-top if needed to push down */
    justify-content: flex-end; /* Align buttons to the right */
    width: 100%; /* Take full width of parent column */
}

/* REMOVED: CSS Rule for .doctor-description .file-input-container as JS will handle visibility */
/* REMOVED: CSS Rule for .doctor-description.editing .file-input-container as JS will handle visibility */

/* تعديلات للشاشات الصغيرة */
@media (max-width: 768px) {
    .doctor-description .card {
        flex-direction: column; /* ترتيب عمودي على الشاشات الصغيرة */
    }

    .doctor-description .description-content,
    .doctor-description .description-image-wrapper {
        flex: 1 1 100%; /* تأخذ عرض كامل */
        width: 100%;
    }

    .doctor-description .certificate-container {
        width: 80px; /* حجم أصغر للحاوية على الشاشات الصغيرة */
        height: 80px; /* ليتناسب مع العرض الجديد */
        max-width: 80px; /* لضمان عدم تجاوز هذا الحجم */
        max-height: 80px; /* لضمان عدم تجاوز هذا الحجم */
        margin: 0 auto;
    }
}

.delete-btn {
    background: #dc3545;
}

.delete-btn:hover {
    background: #c82333;
}

.primary-btn { background: #1976d2; }
.secondary-btn { background: #42a5f5; }
.add-btn { background: #2196f3; }
.edit-btn { background: #64b5f6; }
.cancel-btn { background: #6c757d; }
.cancel-btn:hover { background: #5a6268; }

ul {
    list-style-type: none;
    padding: 0;
}

li {
    padding: 15px;
    background: #fff;
    margin: 10px 0;
    border-radius: 5px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
    position: relative;
    color: #000;
}

.lecture-item {
    margin-bottom: 10px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.lecture-item-title {
    font-weight: bold;
    flex-grow: 1;
    margin: 0;
}

.lecture-item-link {
    color: #1e88e5;
    text-decoration: none;
    margin: 0 10px;
    white-space: nowrap;
}

.lecture-item-link:hover {
    text-decoration: underline;
}

.lecture-actions {
    display: flex;
    gap: 5px;
}

/* UPDATED: Increased size for lecture action buttons */
.lecture-actions button {
    padding: 6px 10px; /* Slightly larger padding */
    margin: 0;
    font-size: 12px; /* Slightly larger font size */
}

.course-lectures, .research-form-container {
    width: 100%;
    margin-top: 15px;
}

/* The .hidden class is a utility class used by JS to toggle visibility */
.hidden {
    display: none !important;
}

a {
    color: #000;
    text-decoration: underline;
}

a:hover {
    color: #0056b3;
}

.research-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.research-table th {
    background-color: #e9ecef;
    padding: 12px;
    text-align: left;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
}

.research-table td {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.research-table tr:last-child td {
    border-bottom: none;
}

.research-table tr:hover {
    background-color: #f8f9fa;
}

.research-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    flex-wrap: nowrap; /* Ensure buttons don't wrap */
}

/* UPDATED: Increased size for research table action buttons */
.research-table button {
    margin: 0;
    padding: 6px 10px; /* Slightly larger padding */
    font-size: 12px; /* Slightly larger font size */
}

.download-btn { /* Style for the download button in table, now inherits size from .research-table button */
    background: #64B5F6; /* Light blue background */
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none; /* Remove underline for button-like link */
    display: inline-block; /* Allow padding and margin */
}

.download-btn:hover {
    background: #42A5F5; /* Darker blue on hover */
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

#ui-message-area {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 25px;
    border-radius: 8px;
    z-index: 1000;
    box-shadow: 0 3px 15px rgba(0,0,0,0.15);
    font-size: 15px;
    text-align: center;
    display: none; /* Hidden by default */
}

#customConfirmModal {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

#customConfirmModal > div {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
    min-width: 300px;
    color: #000;
}

#customConfirmModal p {
    margin-bottom: 20px;
    font-size: 16px;
}

#customConfirmModal button {
    margin: 0 5px;
}

/* NEW: Styles for the general modal content */
.modal-content {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
    max-width: 500px; /* Max width for larger screens */
    width: 90%; /* Take 90% of parent width, up to max-width */
    box-sizing: border-box; /* Include padding and border in the element's total width and height */
    position: relative; /* Needed for absolute positioning of the close button */
    margin: auto; /* Ensures centering within a flex container if needed, although flex properties on parent should handle it */
}

/* Specific styles for the certificate modal content to center its image */
#certificateModal .modal-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px; /* Adjust padding as needed */
}

/* NEW: Styles for the close button within modals */
.close-button {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 28px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-button:hover,
.close-button:focus {
    color: #333;
}

.button-group {
    margin-top: 15px;
}

/* Hamburger menu button */
.menu-toggle {
    display: none; /* Hidden by default on larger screens */
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001; /* Above sidebar and overlay */
    cursor: pointer;
    font-size: 32px; /* Larger icon for better touch target */
    color: #2c3e50;
    background-color: white;
    padding: 5px 10px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    line-height: 1; /* Adjust line height for icon centering */
}

/* Overlay to dim background when sidebar is open on mobile */
.sidebar-overlay {
    display: none; /* Hidden by default */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999; /* Below sidebar, above main content */
}

/* Messages Section - Needs special handling for fixed position */
#messages-section {
    display: none; /* Hidden by default, controlled by JS */
    position: absolute; /* Positioned relative to its parent (.main-content) */
    top: 0; /* Start at the top of main-content */
    left: 0; /* Start at the left of main-content */
    width: 100%; /* Fill parent width */
    height: 100%; /* Fill parent height */
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    padding: 0;
    overflow: hidden;
    z-index: 900; /* Ensure it's above other content in main-content */
}

/* Adjust the iframe within messages-section */
#messagesFrame {
    width: 100%;
    height: 100%; /* Make iframe fill the messages-section */
    border: none;
    background: white;
    border-radius: 0 0 10px 10px;
}

/* Media query for smaller screens */
@media (max-width: 768px) {
    /* Adjust body display for smaller screens */
    body {
        flex-direction: column;
        position: relative;
    }

    .sidebar {
        transform: translateX(-100%);
        box-shadow: none;
        height: 100vh; /* On mobile, sidebar can take full height */
        top: 0; /* On mobile, sidebar can start from top */
    }

    .main-content {
        margin-left: 0; /* No margin on mobile */
        padding-top: 20px; /* Base padding */
        padding-bottom: 20px; /* Base padding */
        width: 100%; /* Full width on mobile */
        height: 100vh; /* Adjusted to fill viewport height */
    }

    #messages-section {
        left: 0; /* Full width on mobile */
        width: 100%;
        height: 100%; /* Fill parent height */
    }

    /* Adjust description content for mobile */
    .doctor-description .card {
        flex-direction: column; /* Stack image and text vertically on mobile */
        align-items: center; /* Center items */
        text-align: center; /* Center text */
    }

    .doctor-description .description-content {
        align-items: center; /* Center content */
    }

    .doctor-description .description-content p {
        text-align: center; /* Center text */
    }

    .doctor-description .button-group {
        justify-content: center; /* Center buttons */
    }
}

/* أنماط خاصة لزر الرسائل في القائمة الجانبية */
.sidebar-link[data-section="messages-section"] {
    border: 2px solid #007bff; /* حدود زرقاء */
    border-radius: 8px; /* زوايا مستديرة */
    background-color: rgba(0, 123, 255, 0.1); /* خلفية زرقاء شفافة */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* ظل خفيف */
    transition: all 0.3s ease; /* تأثير انتقالي سلس */
}

/* تأثير التحويم على زر الرسائل */
.sidebar-link[data-section="messages-section"]:hover {
    background-color: rgba(0, 123, 255, 0.2); /* خلفية أغمق عند التحويم */
    border-color: #0056b3; /* لون حدود أغمق عند التحويم */
    transform: translateY(-2px); /* تأثير ارتفاع طفيف */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* ظل أكبر */
}

/* تأثير النشاط على زر الرسائل */
.sidebar-link[data-section="messages-section"].active {
    background-color: #007bff; /* خلفية زرقاء كاملة عند النشاط */
    color: white; /* لون النص أبيض */
    border-color: #0056b3; /* لون حدود أغمق */
    box-shadow: 0 4px 10px rgba(0, 123, 255, 0.3); /* ظل أزرق */
}

/* أنماط زر تسجيل الخروج */
.logout-item {
    margin-top: auto; /* دفع العنصر إلى أسفل القائمة */
    padding-top: 20px; /* إضافة مسافة فوق زر تسجيل الخروج */
    margin-bottom: 20px; /* إضافة مسافة أسفل زر تسجيل الخروج */
    border-top: 1px solid rgba(255, 255, 255, 0.1); /* خط فاصل فوق الزر */
    border-bottom: 2px solid rgba(255, 255, 255, 0.1); /* خط فاصل تحت الزر */
}

.logout-link {
    color: #fd2626 !important; /* لون أحمر فاتح للنص */
    background-color: rgba(255, 107, 107, 0.1); /* خلفية حمراء شفافة */
    border: 1px solid rgba(255, 107, 107, 0.3); /* حدود حمراء شفافة */
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.logout-link:hover {
    background-color: rgba(255, 107, 107, 0.2); /* خلفية أغمق عند التحويم */
    color: #ff5252 !important; /* لون أحمر أغمق عند التحويم */
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 107, 107, 0.2);
}

/* إضافة أيقونة لزر تسجيل الخروج (اختياري) */
.logout-link::before {
    content: "⟲"; /* رمز تسجيل الخروج */
    margin-right: 8px;
    font-size: 16px;
}

/* تعديل الشريط الجانبي ليدعم وضع زر تسجيل الخروج في الأسفل */
.sidebar {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.sidebar-nav {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    list-style: none;
    padding: 0 15px;
    margin: 0;
}

/* Styles for the certificate modal and image */
#certificateModal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7); /* Darker overlay */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 3000; /* Ensure it's on top of everything */
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

#certificateModal.active {
    opacity: 1;
    visibility: visible;
}

#certificateModal .modal-content {
    background-color: white;
    padding: 20px; /* Reduced padding for more image space */
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 700px; /* Increased max-width for the modal itself */
    width: 90%;
    max-height: 90%; /* Ensure modal doesn't exceed viewport height */
    overflow: hidden; /* Hide overflow if image is too large */
    display: flex; /* Use flexbox for content alignment */
    flex-direction: column; /* Stack items vertically */
    align-items: center; /* Center horizontally */
    justify-content: center; /* Center vertically */
    position: relative; /* For close button positioning */
}

#modalCertificateImage {
    max-width: 100%; /* Ensure image fits within modal content width */
    max-height: 100%;
    object-fit: contain; /* Scale down image to fit, maintaining aspect ratio */
    display: block; /* Remove extra space below image */
    margin: auto; /* Center image horizontally within the flex container */
    border-radius: 8px; /* Slightly rounded corners for the image */
}
