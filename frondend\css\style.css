body, html {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', sans-serif;
}

.hero-section {
  background-image: url('../assets/index.jpg'); 
  background-size: cover;
  background-position: center;
  height: 100vh;
  position: relative;
  color: white;
}

.overlay {
  background-color: rgba(0, 0, 0, 0.6); 
  height: 100%;
  width: 100%;
  padding: 20px 40px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 20px;
}

.logo img {
  height: 70px;
  margin-right: 10px;
}
/* تنسيقات الازرار القائمة والبحث */
.nav-search a {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 20px 25px; /* حجم الزر */
  background-color: #0a0a0a; /* لون خلفية الزر */
  border-radius: 6px;
  font-size: 20px;
  color: white;
  text-decoration: none;
  margin-left: 15px;
  font-weight: bold;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.nav-right a:hover {
  background-color: #666;
  transform: scale(1.05);
}

/*يظهر العنوان الرئيسي في وسط الشاشة مع تنسيق*/
.hero-content {
  text-align: center;
  margin-top: 50px;
}

.hero-content h1 {
  color: #f3f0f0;
  font-size: 64px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 20px;
}

.hero-content p {
  font-size: 18px;
  max-width: 800px;
  margin: auto;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: left;
}

.nav-links {
  display: flex;
  gap: 50px;
}

.nav-links a {
  color: white;
  text-decoration: none;
  font-weight: bold;
}

.nav-search a {
  color: white;
  text-decoration: none;
}

/* تنسيق content*/
.container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      max-width: 1200px;
      margin: auto;
    }

    .section {
      flex: 1;
      min-width: 300px;
      background: #fff;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    h1, h2, h3 {
      color: #000000;
    }

    ul {
      list-style-type: disc;
      margin-left: 20px;
    }

    ul ul {
      list-style-type: circle;
      margin-left: 20px;
    }

    details summary {
      font-weight: bold;
      cursor: pointer;
      margin-top: 15px;
    }
/* تنسيق التذييل */

.footer {
  display: flex;
  align-items: center;
  justify-content: left; /* أو space-between إذا أردت تباعد كامل */
  gap: 20px; /* مسافة بين كل عنصر في الفوتر */
  padding: 20px;
  background-color: #222;
  color: white;
  flex-wrap: wrap; /* إذا صغر العرض، ينزل المحتوى لسطر جديد */
}

.footer-text {
  font-size: 16px;
  margin-right: 40px; /* مسافة بين النص والأيقونات */
}

.footer-button {
  color: white;
  text-decoration: none;
  font-size: 20px;
  margin-left: 40px;
  transition: color 0.3s;
}

.footer-button:hover {
  color: #4da6ff;
}
