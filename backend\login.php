<?php
// إعدادات الأخطاء (للتطوير فقط، عطلها في بيئة الإنتاج)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// بدء الجلسة
session_start();

// تضمين ملف الاتصال بقاعدة البيانات
include 'connect.php';

// دالة موحدة لإرسال الاستجابات بصيغة JSON
function sendJsonResponse($status, $message, $data = [], $statusCode = 200) {
    header('Content-Type: application/json');
    http_response_code($statusCode); // تعيين كود حالة الـ HTTP
    echo json_encode(['status' => $status, 'message' => $message, 'data' => $data]);
    exit(); // إنهاء التنفيذ بعد إرسال الاستجابة
}

// التحقق من أن الطلب من نوع POST
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // التأكد من وجود البيانات المطلوبة
    if (!isset($_POST['email']) || !isset($_POST['password'])) {
        sendJsonResponse('error', 'البريد الإلكتروني وكلمة المرور مطلوبان.', [], 400); // 400 Bad Request
    }

    $email = $_POST['email'];
    $password = $_POST['password']; // كلمة المرور المدخلة من المستخدم (غير مشفرة)

    // استخدام العبارات المحضرة (Prepared Statements) لمنع حقن SQL
    // جلب جميع الأعمدة اللازمة، بما في ذلك 'facultyid'
    $stmt = $conn->prepare("SELECT userid, email, password, type, facultyid FROM login WHERE email = ?");
    $stmt->bind_param("s", $email); // "s" تعني أن المتغير من نوع string (سلسلة نصية)
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 1) {
        $row = $result->fetch_assoc();

        // **هام:** المقارنة المباشرة لكلمات المرور (غير مشفرة)
        if ($password === $row['password']) {
            // تسجيل الدخول ناجح
            $_SESSION['loggedin'] = true;       // للإشارة إلى تسجيل الدخول
            $_SESSION['userid'] = $row['userid']; // معرف حساب الدخول
            $_SESSION['email'] = $row['email'];
            $_SESSION['type'] = $row['type'];
            $_SESSION['faculty_id'] = $row['facultyid']; // تخزين facultyid في الجلسة

            // إرسال استجابة JSON للنجاح
            sendJsonResponse('success', 'تم تسجيل الدخول بنجاح.', [
                'userid' => $row['userid'],
                'email' => $row['email'],
                'type' => $row['type'],
                'faculty_id' => $row['facultyid'] // إرسال facultyid للواجهة الأمامية
            ]);
        } else {
            // كلمة المرور غير صحيحة
            sendJsonResponse('error', 'كلمة المرور غير صحيحة.', [], 401); // 401 Unauthorized
        }
    } else {
        // البريد الإلكتروني غير مسجل
        sendJsonResponse('error', 'البريد الإلكتروني غير مسجل.', [], 401); // 401 Unauthorized
    }
    
    $stmt->close(); // إغلاق العبارة المحضرة
} else {
    // إذا لم يكن الطلب من نوع POST
    sendJsonResponse('error', 'طريقة الطلب غير مدعومة.', [], 405); // 405 Method Not Allowed
}

$conn->close(); // إغلاق الاتصال بقاعدة البيانات
?>