const apiUrl = 'http://localhost/faculty_web/backend/masege.php';
const currentUserId = 1; // عدله لرقم العضو الفعلي (تأكد من أن هذا المعرف صحيح)

let inboxMessages = [];
let sentMessages = [];
let currentView = 'inbox';
let currentViewingMessage = null;

// دالة لتعيين نمط الزر النشط
function setActiveButton(clickedButton) {
    document.querySelectorAll('.buttons-container .btn').forEach(button => {
        button.classList.remove('btn-active');
        button.classList.add('btn-default');
    });
    clickedButton.classList.add('btn-active');
    clickedButton.classList.remove('btn-default');
}

// دالة لعرض مربع الرسائل المخصص
function showMessageBox(title, message) {
    document.getElementById('messageBoxTitle').textContent = title;
    document.getElementById('messageBoxContent').textContent = message;
    document.getElementById('customMessageBox').classList.add('show');
    centerModal(document.querySelector('.message-box-content'));
}

// دالة لإغلاق مربع الرسائل المخصص
function closeMessageBox() {
    document.getElementById('customMessageBox').classList.remove('show');
}

// دالة لفتح نافذة الرسالة الجديدة وتعبئة بريد 'من' الإلكتروني
function openModal() {
    const modal = document.getElementById('messageModal');
    modal.classList.add('show');
    centerModal(modal.querySelector('.modal-content'));
    
    // مسح قيم الإدخال السابقة
    document.getElementById('toEmail').value = '';
    document.getElementById('subject').value = '';
    document.getElementById('messageContent').value = '';

    // جلب وتعيين بريد عضو هيئة التدريس الإلكتروني
    fetchFacultyEmail(); 
}

// دالة لإغلاق نافذة الرسالة الجديدة
function closeModal() {
    document.getElementById('messageModal').classList.remove('show');
}

// دالة لفتح نافذة عرض الرسالة
function openViewMessageModal(message) {
    currentViewingMessage = message;
    document.getElementById('viewFromName').textContent = message.from_name;
    document.getElementById('viewFromEmail').textContent = message.from_email;
    document.getElementById('viewSubject').textContent = message.subject;
    document.getElementById('viewDate').textContent = message.datesent;
    document.getElementById('viewContent').textContent = message.content;
    document.getElementById('viewMessageModal').classList.add('show');
    centerModal(document.querySelector('.view-message-content'));
}

// دالة لإغلاق نافذة عرض الرسالة
function closeViewMessageModal() {
    document.getElementById('viewMessageModal').classList.remove('show');
}

// دالة لتأكيد حذف الرسائل المحددة
function confirmDeleteSelected() {
    const selected = Array.from(document.querySelectorAll('.message-checkbox:checked'));
    if (selected.length === 0) {
        showMessageBox('No Selection', 'Please select at least one message.');
        return;
    }
    document.getElementById('confirmDeleteBox').classList.add('show');
    centerModal(document.querySelector('.confirm-box-content'));
}

// دالة لإغلاق مربع تأكيد الحذف
function closeConfirmDeleteBox() {
    document.getElementById('confirmDeleteBox').classList.remove('show');
}

/**
 * Function to send a message.
 * Updated to use FormData and send data as $_POST to PHP.
 */
async function sendMessage(event) {
    event.preventDefault(); // منع الإرسال الافتراضي للنموذج

    const to = document.getElementById('toEmail').value;
    const subject = document.getElementById('subject').value;
    const content = document.getElementById('messageContent').value;
    // Get sender's email from the read-only 'fromEmail' field
    const fromEmail = document.getElementById('fromEmail').value; 

    // Basic data validation
    if (!fromEmail || !to || !subject || !content) {
        showMessageBox('Submission Error', 'Please fill in all required fields.');
        return;
    }

    const formData = new FormData();
    formData.append('action', 'send');
    formData.append('facultyid', currentUserId);
    formData.append('fromEmail', fromEmail);
    formData.append('toEmail', to);
    formData.append('subject', subject);
    formData.append('content', content);

    // Log FormData contents for verification
    console.log('sendMessage: FormData prepared:');
    for (let pair of formData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
    }

    try {
        const response = await fetch(apiUrl, {
            method: 'POST',
            body: formData // Send FormData directly, no need for Content-Type here
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }
        const data = await response.json();

        if (data.status === 'success') {
            showMessageBox('Success', data.message);
            closeModal();
            fetchMessages('sent'); // Update sent messages after successful sending
        } else {
            showMessageBox('Error', data.message || 'Failed to send message.');
        }
    } catch (error) {
        showMessageBox('Error', `Failed to connect to server: ${error.message}`);
        console.error('Error details:', error);
    }
}

// New function: Fetch faculty member's email from the backend
function fetchFacultyEmail() {
    if (currentUserId <= 0) {
        document.getElementById('fromEmail').value = 'Error: Invalid user ID';
        // Add a class for error styling (if you're using Tailwind CSS or custom styles)
        document.getElementById('fromEmail').style.color = 'red'; 
        return;
    }

    fetch(`${apiUrl}?action=getFacultyEmail&userId=${currentUserId}`)
        .then(response => {
            if (!response.ok) {
                return response.text().then(text => { throw new Error(`HTTP error! status: ${response.status}, message: ${text}`); });
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success' && data.email) {
                document.getElementById('fromEmail').value = data.email;
                // Remove error styling if successful
                document.getElementById('fromEmail').style.color = ''; 
            } else {
                document.getElementById('fromEmail').value = data.message || 'Failed to fetch email';
                // Add styling for error
                document.getElementById('fromEmail').style.color = 'red'; 
                showMessageBox('Error', data.message || 'Failed to fetch faculty email.');
            }
        })
        .catch(error => {
            document.getElementById('fromEmail').value = 'Network Error';
            // Add styling for network error
            document.getElementById('fromEmail').style.color = 'red'; 
            showMessageBox('Error', `Failed to connect to server for email fetch: ${error.message}`);
            console.error('Email fetch error details:', error);
        });
}


// Function to fetch messages (inbox or sent)
function fetchMessages(folder = 'inbox') {
    fetch(`${apiUrl}?action=getMessages&userId=${currentUserId}&folder=${folder}`)
        .then(res => {
            if (!res.ok) {
                return res.text().then(text => { throw new Error(`HTTP error! status: ${res.status}, message: ${text}`); });
            }
            return res.json();
        })
        .then(data => {
            if (data.status === 'success') {
                if (folder === 'inbox') inboxMessages = data.messages;
                else sentMessages = data.messages;
                toggleView(folder);
            } else {
                showMessageBox('Error', data.message || 'Failed to load messages.');
            }
        })
        .catch(error => showMessageBox('Error', `Network error loading messages: ${error.message}`));
}

// Function to toggle between inbox and sent view
function toggleView(view) {
    currentView = view;
    renderMessages();
    const inboxBtn = document.getElementById('inboxBtn');
    const sentBtn = document.getElementById('sentBtn');
    setActiveButton(view === 'inbox' ? inboxBtn : sentBtn);
}

// Function to render messages in the table
function renderMessages() {
    const tbody = document.querySelector('#messagesTable tbody');
    tbody.innerHTML = ''; // مسح الصفوف الموجودة
    const list = currentView === 'inbox' ? inboxMessages : sentMessages;

    list.forEach((msg, i) => {
        const row = tbody.insertRow();
        row.classList.add('message-row');

        const checkboxCell = row.insertCell();
        const cb = document.createElement('input');
        cb.type = 'checkbox';
        cb.classList.add('message-checkbox');
        cb.value = msg.id;
        checkboxCell.appendChild(cb);

        row.insertCell().textContent = i + 1;
        row.insertCell().textContent = currentView === 'inbox' ? msg.from_name : msg.to_name || msg.from_name;
        row.insertCell().textContent = currentView === 'inbox' ? msg.from_email : msg.to_email || msg.from_email;
        row.insertCell().textContent = msg.subject;
        row.insertCell().textContent = msg.datesent;

        // منع فتح النافذة المنبثقة عند النقر على مربع الاختيار
        row.onclick = e => { if (e.target.type !== 'checkbox') openViewMessageModal(msg); };
    });

    document.getElementById('selectAllCheckbox').checked = false;
}

/**
 * Function to delete selected messages.
 * Calls the deleteMessage function for each selected message.
 */
async function deleteSelectedMessages() {
    const selected = Array.from(document.querySelectorAll('.message-checkbox:checked')).map(cb => cb.value);
    if (selected.length === 0) {
        closeConfirmDeleteBox();
        showMessageBox('No Selection', 'Please select at least one message to delete.');
        return;
    }

    closeConfirmDeleteBox(); // Close confirmation box immediately

    let allSuccessful = true;
    let successfulCount = 0;
    for (const messageId of selected) {
        // Call the single message delete function with individual alerts suppressed
        const success = await deleteMessage(messageId, currentView, currentUserId, false); 
        if (success) {
            successfulCount++;
        } else {
            allSuccessful = false;
        }
    }

    if (allSuccessful) {
        showMessageBox('Deleted', `Successfully deleted ${successfulCount} message(s).`);
    } else {
        showMessageBox('Error', `An error occurred while deleting some messages. ${successfulCount} message(s) deleted successfully.`);
    }
    fetchMessages(currentView); // Refresh messages after deletion
}

/**
 * Function to delete a single message.
 * Sends the request using FormData and expects 'deleteMessage' action from PHP.
 * @param {string} messageId - ID of the message to delete.
 * @param {string} folder - Folder where the message resides ('inbox' or 'sent').
 * @param {number} currentFacultyId - Current faculty ID for security.
 * @param {boolean} showAlert - Whether to show individual success/error alerts (default: true). Used for bulk operations.
 * @returns {boolean} - true if deletion was successful, false if failed.
 */
async function deleteMessage(messageId, folder, currentFacultyId, showAlert = true) {
    console.log(`deleteMessage: Attempting to delete message ID: ${messageId} from folder: ${folder} for faculty ID: ${currentFacultyId}`);

    const formData = new FormData();
    formData.append('action', 'deleteMessage'); // Must match what PHP expects
    formData.append('messageId', messageId);
    formData.append('folder', folder);
    formData.append('facultyid', currentFacultyId); // Very important: send faculty ID for security

    // Log FormData contents for verification
    console.log('deleteMessage: FormData prepared:');
    for (let pair of formData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
    }

    try {
        const response = await fetch(apiUrl, { // Ensure correct path to masege.php
            method: 'POST',
            body: formData // Send FormData directly
        });

        console.log('deleteMessage: Raw response status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }

        const data = await response.json();
        console.log('deleteMessage: Backend response:', data);

        if (data.status === 'success') {
            if (showAlert) showMessageBox('Deleted', 'Message deleted successfully!');
            return true; // Indicate success
        } else {
            if (showAlert) showMessageBox('Error', `Error deleting message: ${data.message}`);
            return false; // Indicate failure
        }
    } catch (error) {
        console.error('deleteMessage: Network or parsing error:', error);
        if (showAlert) showMessageBox('Error', `Network or connection error: ${error.message}`);
        return false; // Indicate failure
    }
}

// Function to reply to a message
function replyToMessage(msg) {
    closeViewMessageModal();
    openModal(); // This will call fetchFacultyEmail() again, which is good.
    document.getElementById('toEmail').value = msg.from_email;
    document.getElementById('subject').value = 'Re: ' + msg.subject;
    document.getElementById('messageContent').value = `\n\n-- Original Message --\nFrom: ${msg.from_name}\nSubject: ${msg.subject}\nDate: ${msg.datesent}\n\n${msg.content}`;
}

// Function to toggle selection of all checkboxes
function toggleSelectAll(selectAll) {
    document.querySelectorAll('.message-checkbox').forEach(cb => {
        cb.checked = selectAll.checked;
    });
}

// Function to center the modal content on the screen
function centerModal(modalContent) {
    modalContent.style.left = '50%';
    modalContent.style.top = '50%';
    modalContent.style.transform = 'translate(-50%, -50%)';
}

// Function to make the modal draggable
function makeDraggable(modalContent) {
    let isDragging = false, offsetX = 0, offsetY = 0;

    // Use the modal header or a specific drag handle
    const dragHandle = modalContent.querySelector('h2') || modalContent; // استخدم h2 كمقبض إذا كان موجودًا، وإلا كامل النافذة

    dragHandle.addEventListener('mousedown', e => {
        // Start dragging only if left mouse button is pressed and not on close button
        if (e.button === 0 && !e.target.classList.contains('close') && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA' && e.target.tagName !== 'BUTTON') {
            isDragging = true;
            offsetX = e.clientX - modalContent.getBoundingClientRect().left;
            offsetY = e.clientY - modalContent.getBoundingClientRect().top;
            modalContent.style.cursor = 'grabbing';
            modalContent.style.transition = 'none'; // Disable transition during drag
            e.preventDefault(); // Prevent default browser dragging behavior
        }
    });

    document.addEventListener('mouseup', () => {
        if (isDragging) {
            isDragging = false;
            modalContent.style.cursor = 'grab';
            modalContent.style.transition = 'transform 0.3s ease'; // Re-enable transition after drag
        }
    });

    document.addEventListener('mousemove', e => {
        if (!isDragging) return;
        modalContent.style.left = e.clientX - offsetX + 'px';
        modalContent.style.top = e.clientY - offsetY + 'px';
        modalContent.style.transform = 'none'; // Disable centering transform during drag
    });
}

// Event listener for DOM content loaded
document.addEventListener('DOMContentLoaded', () => {
    fetchMessages('inbox');
    // Set initial active button
    setActiveButton(document.getElementById('inboxBtn')); 

    // Make modals draggable
    makeDraggable(document.querySelector('#messageModal .modal-content'));
    makeDraggable(document.querySelector('#viewMessageModal .view-message-content'));
    makeDraggable(document.querySelector('#customMessageBox .message-box-content'));
    makeDraggable(document.querySelector('#confirmDeleteBox .confirm-box-content'));

    // Set initial fromEmail value (optional, as it will be fetched when openModal)
    document.getElementById('fromEmail').value = 'Loading...'; 
});
