<?php
// Start output buffering as early as possible to prevent partial JSON output on errors
ob_start();

// Enable error reporting for display and logging (for development)
error_reporting(E_ALL);
ini_set('display_errors', 1); // <--- TEMPORARY: Enable display of errors for debugging
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/php_error.log'); // Set a specific log file for PHP errors

error_log("PHP Script (masege.php) started: " . date('Y-m-d H:i:s')); // Log script start

// Set content type to JSON
header('Content-Type: application/json; charset=utf-8');

// Database connection parameters
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "facultymember";
$port = 3308;

// Establish database connection using MySQLi
$conn = new mysqli($servername, $username, $password, $dbname, $port);

// Check connection
if ($conn->connect_error) {
    ob_clean(); // Clean any buffered output before sending error JSON
    http_response_code(500); // Internal Server Error
    error_log("Database connection failed: " . $conn->connect_error); // Log the error
    echo json_encode(["status" => "error", "message" => "Database connection failed: " . $conn->connect_error]);
    exit();
}
error_log("Database connection successful."); // Log successful DB connection

// Determine the action based on request method
$action = '';
$facultyid_from_request = 0; // Initialize faculty ID for security checks

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $action = $_GET['action'] ?? '';
    $facultyid_from_request = intval($_GET['userId'] ?? $_GET['facultyid'] ?? 0); // Check both for consistency
} elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // *** NEW: Log all incoming POST data for debugging ***
    error_log("Received POST data: " . print_r($_POST, true)); 

    $action = $_POST['action'] ?? '';
    $facultyid_from_request = intval($_POST['facultyid'] ?? 0);
}

error_log("Received action: " . $action . ", Faculty ID from request: " . $facultyid_from_request); // Log received action and faculty ID

$response = ['status' => 'error', 'message' => 'Unknown action.'];

// Helper for consistent error response
function sendErrorResponse($message, $httpCode = 400) {
    ob_clean();
    http_response_code($httpCode);
    error_log("Error response sent: " . $message);
    echo json_encode(["status" => "error", "message" => $message]);
    exit();
}

// Function to fetch messages (inbox or sent)
function getMessages($conn, $facultyid, $folder) {
    if ($facultyid <= 0) {
        return ["status" => "error", "message" => "Invalid faculty ID."];
    }

    $sql = "";
    if ($folder === 'inbox') {
        $sql = "SELECT messageid AS id, name AS from_name, sender_email AS from_email, subject, content, datesent 
                FROM message WHERE facultyid = ? ORDER BY datesent DESC";
    } elseif ($folder === 'sent') {
        $sql = "SELECT sentid AS id, name AS to_name, recipient_email AS to_email, subject, content, sentdate AS datesent 
                FROM sent_message WHERE facultyid = ? ORDER BY sentdate DESC";
    } else {
        return ["status" => "error", "message" => "Unknown folder type."];
    }

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        return ["status" => "error", "message" => "Failed to prepare query: " . $conn->error];
    }
    $stmt->bind_param("i", $facultyid);
    $stmt->execute();
    $result = $stmt->get_result();

    $messages = [];
    while ($row = $result->fetch_assoc()) {
        $messages[] = $row;
    }
    $stmt->close();
    return ["status" => "success", "messages" => $messages];
}

// Function to send a new message (record as sent)
function sendMessage($conn, $facultyid, $toEmail, $fromEmail, $subject, $content) {
    if ($facultyid <= 0 || !$toEmail || !$fromEmail || !$subject || !$content) {
        return ["status" => "error", "message" => "All fields are required."];
    }

    // Basic extraction of recipient name from email, ideally this would be more robust
    $toName = explode('@', $toEmail)[0]; 
    $sentdate = date("Y-m-d H:i:s");

    $stmt = $conn->prepare("INSERT INTO sent_message (name, recipient_email, facultyid, content, sentdate, subject) 
                            VALUES (?, ?, ?, ?, ?, ?)");
    if (!$stmt) {
        return ["status" => "error", "message" => "Failed to prepare send message query: " . $conn->error];
    }
    $stmt->bind_param("ssisss", $toName, $toEmail, $facultyid, $content, $sentdate, $subject);

    if ($stmt->execute()) {
        $stmt->close();
        return ["status" => "success", "message" => "Message sent successfully."];
    } else {
        $stmt->close();
        return ["status" => "error", "message" => "Failed to send message: " . $stmt->error];
    }
}

// Function to get faculty email
function getFacultyEmail($conn, $facultyid) {
    if ($facultyid <= 0) {
        return ["status" => "error", "message" => "Invalid faculty ID to fetch email."];
    }

    $stmt = $conn->prepare("SELECT email FROM Faculty WHERE facultyid = ?");
    if (!$stmt) {
        return ["status" => "error", "message" => "Failed to prepare email fetch query: " . $conn->error];
    }
    $stmt->bind_param("i", $facultyid);
    $stmt->execute();
    $result = $stmt->get_result();
    $faculty = $result->fetch_assoc();
    $stmt->close();

    if ($faculty && isset($faculty['email'])) {
        return ["status" => "success", "email" => $faculty['email']];
    } else {
        return ["status" => "error", "message" => "Faculty email not found."];
    }
}

// Function to delete a message
function deleteMessage($conn, $messageId, $folder, $facultyid) { // Added facultyid for security
    if ($messageId <= 0 || ($folder !== 'inbox' && $folder !== 'sent') || $facultyid <= 0) {
        return ["status" => "error", "message" => "Invalid message ID, folder, or faculty ID."];
    }

    $sql = "";
    if ($folder === 'inbox') {
        // IMPORTANT SECURITY FIX: Ensure the message belongs to the deleting facultyid
        $sql = "DELETE FROM message WHERE messageid = ? AND facultyid = ?";
    } else { // 'sent'
        // IMPORTANT SECURITY FIX: Ensure the sent message belongs to the deleting facultyid
        $sql = "DELETE FROM sent_message WHERE sentid = ? AND facultyid = ?";
    }

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        return ["status" => "error", "message" => "Failed to prepare delete query: " . $conn->error];
    }
    // Bind both message ID and faculty ID for secure deletion
    $stmt->bind_param("ii", $messageId, $facultyid); 

    if ($stmt->execute()) {
        // Check if any rows were affected to ensure the message was actually found and deleted
        if ($stmt->affected_rows > 0) {
            $stmt->close();
            return ["status" => "success", "message" => "Message deleted successfully."];
        } else {
            $stmt->close();
            // Message not found or not owned by this faculty ID
            return ["status" => "error", "message" => "Message not found or unauthorized to delete."];
        }
    } else {
        $stmt->close();
        return ["status" => "error", "message" => "Failed to delete message: " . $stmt->error];
    }
}


// Main Request Handling Logic
try {
    if ($action === 'getMessages') {
        $folder = $_REQUEST['folder'] ?? 'inbox'; // Use $_REQUEST for folder
        $response = getMessages($conn, $facultyid_from_request, $folder);
    } elseif ($action === 'send') {
        // Using $_POST directly as we've switched to FormData expectation
        $toEmail = $_POST['toEmail'] ?? '';
        $fromEmail = $_POST['fromEmail'] ?? '';
        $subject = $_POST['subject'] ?? '';
        $content = $_POST['content'] ?? '';
        $response = sendMessage($conn, $facultyid_from_request, $toEmail, $fromEmail, $subject, $content);
    } elseif ($action === 'getFacultyEmail') {
        $response = getFacultyEmail($conn, $facultyid_from_request);
    } elseif ($action === 'deleteMessage') {
        $messageId = intval($_POST['messageId'] ?? 0); // Assuming POST for deleteMessage
        $folder = $_POST['folder'] ?? ''; // Assuming POST for deleteMessage
        // Pass facultyid_from_request for secure deletion
        $response = deleteMessage($conn, $messageId, $folder, $facultyid_from_request); 
    } else {
        sendErrorResponse("Unknown action: " . $action);
    }
} catch (Exception $e) {
    sendErrorResponse("An unexpected error occurred: " . $e->getMessage(), 500);
}

// Log the final response for debugging
error_log("Final response for action '" . $action . "': " . json_encode($response));

// Output the JSON response
echo json_encode($response);

// End output buffering and flush
ob_end_flush();

// Close database connection
$conn->close();
?>
