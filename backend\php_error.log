[01-Jul-2025 22:40:01 Europe/Berlin] <PERSON><PERSON> (masege.php) started: 2025-07-01 22:40:01
[01-Jul-2025 22:40:01 Europe/Berlin] Database connection successful.
[01-Jul-2025 22:40:01 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 22:40:01 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 22:43:13 Europe/Berlin] P<PERSON> (masege.php) started: 2025-07-01 22:43:13
[01-Jul-2025 22:43:13 Europe/Berlin] Database connection successful.
[01-Jul-2025 22:43:13 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 22:43:13 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 22:44:06 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 22:44:06
[01-Jul-2025 22:44:06 Europe/Berlin] Database connection successful.
[01-Jul-2025 22:44:06 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 22:44:06 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 22:44:36 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 22:44:36
[01-Jul-2025 22:44:36 Europe/Berlin] Database connection successful.
[01-Jul-2025 22:44:36 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 22:44:36 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 23:22:45 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 23:22:44
[01-Jul-2025 23:22:45 Europe/Berlin] Database connection successful.
[01-Jul-2025 23:22:45 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 23:22:45 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 23:51:02 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 23:51:01
[01-Jul-2025 23:51:02 Europe/Berlin] Database connection successful.
[01-Jul-2025 23:51:02 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 23:51:02 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 23:52:34 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 23:52:34
[01-Jul-2025 23:52:34 Europe/Berlin] Database connection successful.
[01-Jul-2025 23:52:34 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 23:52:34 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 23:52:43 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 23:52:43
[01-Jul-2025 23:52:43 Europe/Berlin] Database connection successful.
[01-Jul-2025 23:52:43 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 23:52:43 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 23:52:51 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 23:52:51
[01-Jul-2025 23:52:51 Europe/Berlin] Database connection successful.
[01-Jul-2025 23:52:51 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 23:52:51 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 00:00:11 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 00:00:11
[02-Jul-2025 00:00:11 Europe/Berlin] Database connection successful.
[02-Jul-2025 00:00:11 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 00:00:11 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 00:01:50 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 00:01:50
[02-Jul-2025 00:01:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 00:01:50 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 00:01:50 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 00:57:04 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 00:57:04
[02-Jul-2025 00:57:04 Europe/Berlin] Database connection successful.
[02-Jul-2025 00:57:04 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 00:57:04 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:01:47 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:01:47
[02-Jul-2025 01:01:47 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:01:47 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:01:47 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:04:43 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:04:43
[02-Jul-2025 01:04:43 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:04:43 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:04:43 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:06:54 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:06:54
[02-Jul-2025 01:06:54 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:06:54 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:06:54 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:07:11 Europe/Berlin] PHP Script started: 2025-07-02 01:07:11
[02-Jul-2025 01:07:11 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:07:14 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:07:14
[02-Jul-2025 01:07:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:07:14 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:07:14 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:07:14 Europe/Berlin] PHP Script started: 2025-07-02 01:07:14
[02-Jul-2025 01:07:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:07:14 Europe/Berlin] PHP Script started: 2025-07-02 01:07:14
[02-Jul-2025 01:07:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:07:15 Europe/Berlin] PHP Script started: 2025-07-02 01:07:15
[02-Jul-2025 01:07:15 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:07:15 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 01:07:15 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 01:08:08 Europe/Berlin] PHP Script started: 2025-07-02 01:08:08
[02-Jul-2025 01:08:08 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:08 Europe/Berlin] DEBUG: updateFacultyCertificateImage called for facultyId: 1
[02-Jul-2025 01:08:08 Europe/Berlin] DEBUG: certificate imageData length: 1996243 bytes.
[02-Jul-2025 01:08:08 Europe/Berlin] DEBUG: Prepare statement successful for certificate.
[02-Jul-2025 01:08:08 Europe/Berlin] DEBUG: bind_param executed for certificate.
[02-Jul-2025 01:08:08 Europe/Berlin] ERROR: Exception in updateFacultyCertificateImage: Got a packet bigger than 'max_allowed_packet' bytes
[02-Jul-2025 01:08:08 Europe/Berlin] PHP Script started: 2025-07-02 01:08:08
[02-Jul-2025 01:08:08 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:20 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:08:20
[02-Jul-2025 01:08:20 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:20 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:08:20 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:08:20 Europe/Berlin] PHP Script started: 2025-07-02 01:08:20
[02-Jul-2025 01:08:20 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:21 Europe/Berlin] PHP Script started: 2025-07-02 01:08:21
[02-Jul-2025 01:08:21 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:21 Europe/Berlin] PHP Script started: 2025-07-02 01:08:21
[02-Jul-2025 01:08:21 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:21 Europe/Berlin] PHP Script started: 2025-07-02 01:08:21
[02-Jul-2025 01:08:21 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:21 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 01:08:21 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 01:08:32 Europe/Berlin] PHP Script started: 2025-07-02 01:08:32
[02-Jul-2025 01:08:32 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:32 Europe/Berlin] DEBUG: updateFacultyCertificateImage called for facultyId: 1
[02-Jul-2025 01:08:32 Europe/Berlin] DEBUG: certificate imageData length: 1996243 bytes.
[02-Jul-2025 01:08:32 Europe/Berlin] DEBUG: Prepare statement successful for certificate.
[02-Jul-2025 01:08:32 Europe/Berlin] DEBUG: bind_param executed for certificate.
[02-Jul-2025 01:08:32 Europe/Berlin] ERROR: Exception in updateFacultyCertificateImage: Got a packet bigger than 'max_allowed_packet' bytes
[02-Jul-2025 01:08:32 Europe/Berlin] PHP Warning:  main(): Error occurred while closing statement in C:\xampp\htdocs\faculty_web\backend\faculty_control.php on line 889
[02-Jul-2025 01:08:32 Europe/Berlin] PHP Script started: 2025-07-02 01:08:32
[02-Jul-2025 01:08:32 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:11:44 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:11:44
[02-Jul-2025 01:11:44 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:11:44 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:11:44 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:16:14 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:16:14
[02-Jul-2025 01:16:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:16:14 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:16:14 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:16:27 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:16:27
[02-Jul-2025 01:16:27 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:16:27 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:16:27 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:16:40 Europe/Berlin] PHP Script started: 2025-07-02 01:16:40
[02-Jul-2025 01:16:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:16:40 Europe/Berlin] getLecturesByAssignment called with assignmentId: 1
[02-Jul-2025 01:16:40 Europe/Berlin] getLecturesByAssignment: Prepared statement successfully.
[02-Jul-2025 01:16:40 Europe/Berlin] getLecturesByAssignment: Parameters bound.
[02-Jul-2025 01:16:40 Europe/Berlin] getLecturesByAssignment: Execute command sent.
[02-Jul-2025 01:16:40 Europe/Berlin] getLecturesByAssignment: Got result set.
[02-Jul-2025 01:16:40 Europe/Berlin] getLecturesByAssignment: Successfully fetched 2 lectures.
[02-Jul-2025 01:18:03 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:18:03
[02-Jul-2025 01:18:03 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:18:03 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:18:03 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:18:33 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:18:33
[02-Jul-2025 01:18:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:18:33 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:18:33 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:19:29 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:19:29
[02-Jul-2025 01:19:29 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:19:29 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:19:29 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:20:35 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:20:35
[02-Jul-2025 01:20:35 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:20:35 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:20:35 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:25:40 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:25:40
[02-Jul-2025 01:25:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:25:40 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:25:40 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:27:19 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:27:19
[02-Jul-2025 01:27:19 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:27:19 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:27:19 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:36:51 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:36:51
[02-Jul-2025 01:36:51 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:36:51 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:36:51 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:37:38 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:37:38
[02-Jul-2025 01:37:38 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:37:38 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:37:38 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
