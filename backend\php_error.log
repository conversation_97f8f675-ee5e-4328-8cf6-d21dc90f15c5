[02-Jul-2025 13:54:06 Europe/Berlin] PHP Script started: 2025-07-02 13:54:06
[02-Jul-2025 13:54:06 Europe/Berlin] Database connection successful.
[02-Jul-2025 13:54:06 Europe/Berlin] getLecturesByAssignment called with assignmentId: 1
[02-Jul-2025 13:54:06 Europe/Berlin] Error fetching lectures by assignment: Unknown column 'file_extension' in 'field list'
[02-Jul-2025 13:54:09 Europe/Berlin] PHP Script started: 2025-07-02 13:54:09
[02-Jul-2025 13:54:09 Europe/Berlin] Database connection successful.
[02-Jul-2025 13:54:09 Europe/Berlin] getLecturesByAssignment called with assignmentId: 3
[02-Jul-2025 13:54:09 Europe/Berlin] Error fetching lectures by assignment: Unknown column 'file_extension' in 'field list'
[02-Jul-2025 13:54:14 Europe/Berlin] PHP Script started: 2025-07-02 13:54:14
[02-Jul-2025 13:54:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 13:54:14 Europe/Berlin] getLecturesByAssignment called with assignmentId: 1
[02-Jul-2025 13:54:14 Europe/Berlin] Error fetching lectures by assignment: Unknown column 'file_extension' in 'field list'
[02-Jul-2025 13:54:21 Europe/Berlin] PHP Script started: 2025-07-02 13:54:21
[02-Jul-2025 13:54:21 Europe/Berlin] Database connection successful.
[02-Jul-2025 13:54:23 Europe/Berlin] PHP Script started: 2025-07-02 13:54:23
[02-Jul-2025 13:54:23 Europe/Berlin] Database connection successful.
[02-Jul-2025 13:54:23 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 13:54:23
[02-Jul-2025 13:54:23 Europe/Berlin] Database connection successful.
[02-Jul-2025 13:54:23 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 13:54:23 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 13:54:23 Europe/Berlin] PHP Script started: 2025-07-02 13:54:23
[02-Jul-2025 13:54:23 Europe/Berlin] Database connection successful.
[02-Jul-2025 13:54:23 Europe/Berlin] PHP Script started: 2025-07-02 13:54:23
[02-Jul-2025 13:54:23 Europe/Berlin] Database connection successful.
[02-Jul-2025 13:54:23 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 13:54:23 Europe/Berlin] Error fetching faculty research: Unknown column 'file_extension' in 'field list'
[02-Jul-2025 13:54:27 Europe/Berlin] PHP Script started: 2025-07-02 13:54:27
[02-Jul-2025 13:54:27 Europe/Berlin] Database connection successful.
[02-Jul-2025 13:54:27 Europe/Berlin] getLecturesByAssignment called with assignmentId: 1
[02-Jul-2025 13:54:27 Europe/Berlin] Error fetching lectures by assignment: Unknown column 'file_extension' in 'field list'
[02-Jul-2025 13:54:28 Europe/Berlin] PHP Script started: 2025-07-02 13:54:28
[02-Jul-2025 13:54:28 Europe/Berlin] Database connection successful.
[02-Jul-2025 13:54:28 Europe/Berlin] getLecturesByAssignment called with assignmentId: 3
[02-Jul-2025 13:54:28 Europe/Berlin] Error fetching lectures by assignment: Unknown column 'file_extension' in 'field list'
[02-Jul-2025 13:55:20 Europe/Berlin] PHP Script started: 2025-07-02 13:55:20
[02-Jul-2025 13:55:20 Europe/Berlin] Database connection successful.
[02-Jul-2025 13:55:20 Europe/Berlin] getLecturesByAssignment called with assignmentId: 1
[02-Jul-2025 13:55:20 Europe/Berlin] Error fetching lectures by assignment: Unknown column 'file_extension' in 'field list'
[02-Jul-2025 14:00:33 Europe/Berlin] PHP Script started: 2025-07-02 14:00:33
[02-Jul-2025 14:00:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:00:33 Europe/Berlin] PHP Script started: 2025-07-02 14:00:33
[02-Jul-2025 14:00:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:00:33 Europe/Berlin] PHP Script started: 2025-07-02 14:00:33
[02-Jul-2025 14:00:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:00:33 Europe/Berlin] PHP Script started: 2025-07-02 14:00:33
[02-Jul-2025 14:00:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:00:33 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:00:33 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:00:34 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:00:34
[02-Jul-2025 14:00:34 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:00:34 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:00:34 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:00:36 Europe/Berlin] PHP Script started: 2025-07-02 14:00:36
[02-Jul-2025 14:00:36 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:00:36 Europe/Berlin] getLecturesByAssignment called with assignmentId: 1
[02-Jul-2025 14:00:36 Europe/Berlin] getLecturesByAssignment: Prepared statement successfully.
[02-Jul-2025 14:00:36 Europe/Berlin] getLecturesByAssignment: Parameters bound.
[02-Jul-2025 14:00:36 Europe/Berlin] getLecturesByAssignment: Execute command sent.
[02-Jul-2025 14:00:36 Europe/Berlin] getLecturesByAssignment: Got result set.
[02-Jul-2025 14:00:36 Europe/Berlin] getLecturesByAssignment: Successfully fetched 2 lectures.
[02-Jul-2025 14:03:47 Europe/Berlin] PHP Script started: 2025-07-02 14:03:47
[02-Jul-2025 14:03:47 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:03:49 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:03:49
[02-Jul-2025 14:03:49 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:03:49 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:03:49 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:03:50 Europe/Berlin] PHP Script started: 2025-07-02 14:03:50
[02-Jul-2025 14:03:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:03:50 Europe/Berlin] PHP Script started: 2025-07-02 14:03:50
[02-Jul-2025 14:03:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:03:50 Europe/Berlin] PHP Script started: 2025-07-02 14:03:50
[02-Jul-2025 14:03:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:03:50 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:03:50 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:03:52 Europe/Berlin] PHP Script started: 2025-07-02 14:03:52
[02-Jul-2025 14:03:52 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:03:52 Europe/Berlin] getLecturesByAssignment called with assignmentId: 1
[02-Jul-2025 14:03:52 Europe/Berlin] getLecturesByAssignment: Prepared statement successfully.
[02-Jul-2025 14:03:52 Europe/Berlin] getLecturesByAssignment: Parameters bound.
[02-Jul-2025 14:03:52 Europe/Berlin] getLecturesByAssignment: Execute command sent.
[02-Jul-2025 14:03:52 Europe/Berlin] getLecturesByAssignment: Got result set.
[02-Jul-2025 14:03:52 Europe/Berlin] getLecturesByAssignment: Successfully fetched 2 lectures.
[02-Jul-2025 14:04:06 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:04:06
[02-Jul-2025 14:04:06 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:04:06 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:04:06 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:05:06 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:05:06
[02-Jul-2025 14:05:06 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:05:06 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:05:06 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:05:17 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:05:17
[02-Jul-2025 14:05:17 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:05:17 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:05:17 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":4,"to_name":"ali","to_email":"<EMAIL>","subject":"Re: try reply","content":"jfdajhvkk\n\n-- \u0627\u0644\u0631\u0633\u0627\u0644\u0629 \u0627\u0644\u0623\u0635\u0644\u064a\u0629 --\n\u0645\u0646: ali\n\u0627\u0644\u0645\u0648\u0636\u0648\u0639: try\n\u0627\u0644\u062a\u0627\u0631\u064a\u062e: 2025-06-28 02:55:50\n\ntry inbox masseges","datesent":"2025-06-29 15:48:21"},{"id":3,"to_name":"sader","to_email":"<EMAIL>","subject":"try scound","content":"chvj jkjhkj","datesent":"2025-06-29 15:47:49"},{"id":1,"to_name":"ahmed","to_email":"<EMAIL>","subject":"try send","content":"try send folder","datesent":"2025-06-28 02:57:01"}]}
[02-Jul-2025 14:13:55 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:13:55
[02-Jul-2025 14:13:55 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:13:55 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:13:55 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:14:16 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:14:16
[02-Jul-2025 14:14:16 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:14:16 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:14:16 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:14:25 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:14:25
[02-Jul-2025 14:14:25 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:14:25 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:14:25 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:14:45 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:14:45
[02-Jul-2025 14:14:45 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:14:45 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:14:45 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:15:15 Europe/Berlin] PHP Script started: 2025-07-02 14:15:15
[02-Jul-2025 14:15:15 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:17 Europe/Berlin] PHP Script started: 2025-07-02 14:15:17
[02-Jul-2025 14:15:17 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:17 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:15:17
[02-Jul-2025 14:15:17 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:17 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:15:17 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:15:18 Europe/Berlin] PHP Script started: 2025-07-02 14:15:18
[02-Jul-2025 14:15:18 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:18 Europe/Berlin] PHP Script started: 2025-07-02 14:15:18
[02-Jul-2025 14:15:18 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:18 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:15:18 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:15:22 Europe/Berlin] PHP Script started: 2025-07-02 14:15:22
[02-Jul-2025 14:15:22 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:23 Europe/Berlin] PHP Script started: 2025-07-02 14:15:23
[02-Jul-2025 14:15:23 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:23 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:15:23
[02-Jul-2025 14:15:23 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:23 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:15:23 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:15:23 Europe/Berlin] PHP Script started: 2025-07-02 14:15:23
[02-Jul-2025 14:15:23 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:23 Europe/Berlin] PHP Script started: 2025-07-02 14:15:23
[02-Jul-2025 14:15:23 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:23 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:15:23 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:15:27 Europe/Berlin] PHP Script started: 2025-07-02 14:15:27
[02-Jul-2025 14:15:27 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:28 Europe/Berlin] PHP Script started: 2025-07-02 14:15:28
[02-Jul-2025 14:15:28 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:28 Europe/Berlin] PHP Script started: 2025-07-02 14:15:28
[02-Jul-2025 14:15:28 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:28 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:15:28
[02-Jul-2025 14:15:28 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:28 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:15:28 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:15:28 Europe/Berlin] PHP Script started: 2025-07-02 14:15:28
[02-Jul-2025 14:15:28 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:28 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:15:28 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:15:43 Europe/Berlin] PHP Script started: 2025-07-02 14:15:43
[02-Jul-2025 14:15:43 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:44 Europe/Berlin] PHP Script started: 2025-07-02 14:15:44
[02-Jul-2025 14:15:44 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:44 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:15:44
[02-Jul-2025 14:15:44 Europe/Berlin] PHP Script started: 2025-07-02 14:15:44
[02-Jul-2025 14:15:44 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:44 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:15:44 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:15:44 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:44 Europe/Berlin] PHP Script started: 2025-07-02 14:15:44
[02-Jul-2025 14:15:44 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:44 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:15:44 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:15:49 Europe/Berlin] PHP Script started: 2025-07-02 14:15:49
[02-Jul-2025 14:15:49 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:49 Europe/Berlin] PHP Script started: 2025-07-02 14:15:49
[02-Jul-2025 14:15:49 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:50 Europe/Berlin] PHP Script started: 2025-07-02 14:15:50
[02-Jul-2025 14:15:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:50 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:15:50
[02-Jul-2025 14:15:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:50 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:15:50 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:15:50 Europe/Berlin] PHP Script started: 2025-07-02 14:15:50
[02-Jul-2025 14:15:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:50 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:15:50 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:15:56 Europe/Berlin] PHP Script started: 2025-07-02 14:15:56
[02-Jul-2025 14:15:56 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:56 Europe/Berlin] PHP Script started: 2025-07-02 14:15:56
[02-Jul-2025 14:15:56 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:56 Europe/Berlin] PHP Script started: 2025-07-02 14:15:56
[02-Jul-2025 14:15:56 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:56 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:15:56
[02-Jul-2025 14:15:56 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:56 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:15:56 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:15:57 Europe/Berlin] PHP Script started: 2025-07-02 14:15:57
[02-Jul-2025 14:15:57 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:15:57 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:15:57 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:17:02 Europe/Berlin] PHP Script started: 2025-07-02 14:17:02
[02-Jul-2025 14:17:02 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:17:02 Europe/Berlin] PHP Script started: 2025-07-02 14:17:02
[02-Jul-2025 14:17:02 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:17:02 Europe/Berlin] PHP Script started: 2025-07-02 14:17:02
[02-Jul-2025 14:17:02 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:17:03 Europe/Berlin] PHP Script started: 2025-07-02 14:17:03
[02-Jul-2025 14:17:03 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:17:03 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:17:03 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:17:03 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:17:03
[02-Jul-2025 14:17:04 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:17:04 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:17:04 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:17:09 Europe/Berlin] PHP Script started: 2025-07-02 14:17:09
[02-Jul-2025 14:17:09 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:17:09 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:17:09
[02-Jul-2025 14:17:09 Europe/Berlin] PHP Script started: 2025-07-02 14:17:09
[02-Jul-2025 14:17:09 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:17:09 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:17:09 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:17:09 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:17:10 Europe/Berlin] PHP Script started: 2025-07-02 14:17:10
[02-Jul-2025 14:17:10 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:17:10 Europe/Berlin] PHP Script started: 2025-07-02 14:17:10
[02-Jul-2025 14:17:10 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:17:10 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:17:10 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:18:31 Europe/Berlin] PHP Script started: 2025-07-02 14:18:31
[02-Jul-2025 14:18:31 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:18:33 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:18:33
[02-Jul-2025 14:18:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:18:33 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:18:33 Europe/Berlin] PHP Script started: 2025-07-02 14:18:33
[02-Jul-2025 14:18:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:18:33 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:18:33 Europe/Berlin] PHP Script started: 2025-07-02 14:18:33
[02-Jul-2025 14:18:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:18:34 Europe/Berlin] PHP Script started: 2025-07-02 14:18:34
[02-Jul-2025 14:18:34 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:18:34 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:18:34 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:20:51 Europe/Berlin] PHP Script started: 2025-07-02 14:20:51
[02-Jul-2025 14:20:51 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:20:51 Europe/Berlin] PHP Script started: 2025-07-02 14:20:51
[02-Jul-2025 14:20:51 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:20:51 Europe/Berlin] PHP Script started: 2025-07-02 14:20:51
[02-Jul-2025 14:20:51 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:20:51 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:20:51
[02-Jul-2025 14:20:51 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:20:51 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:20:51 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:20:52 Europe/Berlin] PHP Script started: 2025-07-02 14:20:52
[02-Jul-2025 14:20:52 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:20:52 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:20:52 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:23:06 Europe/Berlin] PHP Script started: 2025-07-02 14:23:06
[02-Jul-2025 14:23:06 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:23:07 Europe/Berlin] PHP Script started: 2025-07-02 14:23:07
[02-Jul-2025 14:23:07 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:23:07 Europe/Berlin] PHP Script started: 2025-07-02 14:23:07
[02-Jul-2025 14:23:07 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:23:07 Europe/Berlin] PHP Script started: 2025-07-02 14:23:07
[02-Jul-2025 14:23:07 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:23:07 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:23:07 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:23:07 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:23:07
[02-Jul-2025 14:23:07 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:23:07 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:23:07 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:24:01 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:24:01
[02-Jul-2025 14:24:01 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:24:01 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:24:01 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:24:16 Europe/Berlin] PHP Script started: 2025-07-02 14:24:16
[02-Jul-2025 14:24:16 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:24:18 Europe/Berlin] PHP Script started: 2025-07-02 14:24:18
[02-Jul-2025 14:24:18 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:24:18 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:24:18
[02-Jul-2025 14:24:18 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:24:18 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:24:18 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:24:18 Europe/Berlin] PHP Script started: 2025-07-02 14:24:18
[02-Jul-2025 14:24:18 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:24:18 Europe/Berlin] PHP Script started: 2025-07-02 14:24:18
[02-Jul-2025 14:24:18 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:24:18 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:24:18 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:25:07 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:25:07
[02-Jul-2025 14:25:07 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:25:07 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:25:07 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:25:36 Europe/Berlin] PHP Script started: 2025-07-02 14:25:36
[02-Jul-2025 14:25:36 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:25:37 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:25:37
[02-Jul-2025 14:25:37 Europe/Berlin] PHP Script started: 2025-07-02 14:25:37
[02-Jul-2025 14:25:37 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:25:37 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:25:37 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:25:37 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:25:37 Europe/Berlin] PHP Script started: 2025-07-02 14:25:37
[02-Jul-2025 14:25:37 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:25:38 Europe/Berlin] PHP Script started: 2025-07-02 14:25:38
[02-Jul-2025 14:25:38 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:25:38 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:25:38 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:27:47 Europe/Berlin] PHP Script started: 2025-07-02 14:27:47
[02-Jul-2025 14:27:47 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:27:48 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:27:48
[02-Jul-2025 14:27:48 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:27:48 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:27:48 Europe/Berlin] PHP Script started: 2025-07-02 14:27:48
[02-Jul-2025 14:27:48 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:27:48 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:27:49 Europe/Berlin] PHP Script started: 2025-07-02 14:27:49
[02-Jul-2025 14:27:49 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:27:49 Europe/Berlin] PHP Script started: 2025-07-02 14:27:49
[02-Jul-2025 14:27:49 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:27:49 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:27:49 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:27:56 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:27:56
[02-Jul-2025 14:27:56 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:27:56 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:27:56 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:29:06 Europe/Berlin] PHP Script started: 2025-07-02 14:29:06
[02-Jul-2025 14:29:06 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:29:07 Europe/Berlin] PHP Script started: 2025-07-02 14:29:07
[02-Jul-2025 14:29:07 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:29:07 Europe/Berlin] PHP Script started: 2025-07-02 14:29:07
[02-Jul-2025 14:29:07 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:29:07 Europe/Berlin] PHP Script started: 2025-07-02 14:29:07
[02-Jul-2025 14:29:07 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:29:07 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:29:07 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:29:07 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:29:07
[02-Jul-2025 14:29:07 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:29:07 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:29:07 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:30:35 Europe/Berlin] PHP Script started: 2025-07-02 14:30:35
[02-Jul-2025 14:30:35 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:35 Europe/Berlin] PHP Script started: 2025-07-02 14:30:35
[02-Jul-2025 14:30:35 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:35 Europe/Berlin] PHP Script started: 2025-07-02 14:30:35
[02-Jul-2025 14:30:35 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:35 Europe/Berlin] PHP Script started: 2025-07-02 14:30:35
[02-Jul-2025 14:30:35 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:35 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:30:35 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:30:35 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:30:36
[02-Jul-2025 14:30:35 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:35 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:30:36 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:30:47 Europe/Berlin] PHP Script started: 2025-07-02 14:30:47
[02-Jul-2025 14:30:47 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:47 Europe/Berlin] PHP Script started: 2025-07-02 14:30:47
[02-Jul-2025 14:30:47 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:47 Europe/Berlin] PHP Script started: 2025-07-02 14:30:47
[02-Jul-2025 14:30:47 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:48 Europe/Berlin] PHP Script started: 2025-07-02 14:30:48
[02-Jul-2025 14:30:48 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:48 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:30:48 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:30:48 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:30:48
[02-Jul-2025 14:30:48 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:48 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:30:48 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:30:49 Europe/Berlin] PHP Script started: 2025-07-02 14:30:49
[02-Jul-2025 14:30:49 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:49 Europe/Berlin] PHP Script started: 2025-07-02 14:30:49
[02-Jul-2025 14:30:49 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:49 Europe/Berlin] PHP Script started: 2025-07-02 14:30:49
[02-Jul-2025 14:30:49 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:49 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:30:49
[02-Jul-2025 14:30:49 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:49 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:30:50 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:30:50 Europe/Berlin] PHP Script started: 2025-07-02 14:30:50
[02-Jul-2025 14:30:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:30:50 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:30:50 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:31:00 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:31:00
[02-Jul-2025 14:31:00 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:31:00 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:31:00 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:32:15 Europe/Berlin] PHP Script started: 2025-07-02 14:32:15
[02-Jul-2025 14:32:15 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:16 Europe/Berlin] PHP Script started: 2025-07-02 14:32:16
[02-Jul-2025 14:32:16 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:16 Europe/Berlin] PHP Script started: 2025-07-02 14:32:16
[02-Jul-2025 14:32:16 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:16 Europe/Berlin] PHP Script started: 2025-07-02 14:32:16
[02-Jul-2025 14:32:16 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:16 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:32:16 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:32:16 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:32:16
[02-Jul-2025 14:32:16 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:16 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:32:16 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:32:19 Europe/Berlin] PHP Script started: 2025-07-02 14:32:19
[02-Jul-2025 14:32:19 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:20 Europe/Berlin] PHP Script started: 2025-07-02 14:32:20
[02-Jul-2025 14:32:20 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:20 Europe/Berlin] PHP Script started: 2025-07-02 14:32:20
[02-Jul-2025 14:32:20 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:20 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:32:20
[02-Jul-2025 14:32:20 Europe/Berlin] PHP Script started: 2025-07-02 14:32:20
[02-Jul-2025 14:32:20 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:20 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:32:20 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:20 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:32:20 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:32:20 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:32:22 Europe/Berlin] PHP Script started: 2025-07-02 14:32:22
[02-Jul-2025 14:32:22 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:22 Europe/Berlin] PHP Script started: 2025-07-02 14:32:22
[02-Jul-2025 14:32:22 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:22 Europe/Berlin] PHP Script started: 2025-07-02 14:32:22
[02-Jul-2025 14:32:22 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:32:22
[02-Jul-2025 14:32:22 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:22 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:32:22 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:32:22 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:23 Europe/Berlin] PHP Script started: 2025-07-02 14:32:23
[02-Jul-2025 14:32:23 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:23 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:32:23 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:32:44 Europe/Berlin] PHP Script started: 2025-07-02 14:32:44
[02-Jul-2025 14:32:44 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:45 Europe/Berlin] PHP Script started: 2025-07-02 14:32:45
[02-Jul-2025 14:32:45 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:45 Europe/Berlin] PHP Script started: 2025-07-02 14:32:45
[02-Jul-2025 14:32:45 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:45 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:32:45
[02-Jul-2025 14:32:45 Europe/Berlin] PHP Script started: 2025-07-02 14:32:45
[02-Jul-2025 14:32:45 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:45 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:32:45 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:32:45 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 14:32:45 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 14:32:45 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 14:33:07 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 14:33:07
[02-Jul-2025 14:33:07 Europe/Berlin] Database connection successful.
[02-Jul-2025 14:33:07 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 14:33:07 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 21:58:20 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 21:58:20
[02-Jul-2025 21:58:20 Europe/Berlin] Database connection successful.
[02-Jul-2025 21:58:20 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 21:58:20 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 21:58:35 Europe/Berlin] PHP Script started: 2025-07-02 21:58:35
[02-Jul-2025 21:58:35 Europe/Berlin] Database connection successful.
[02-Jul-2025 21:58:37 Europe/Berlin] PHP Script started: 2025-07-02 21:58:37
[02-Jul-2025 21:58:37 Europe/Berlin] Database connection successful.
[02-Jul-2025 21:58:37 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 21:58:37
[02-Jul-2025 21:58:37 Europe/Berlin] Database connection successful.
[02-Jul-2025 21:58:37 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 21:58:37 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 21:58:37 Europe/Berlin] PHP Script started: 2025-07-02 21:58:37
[02-Jul-2025 21:58:37 Europe/Berlin] Database connection successful.
[02-Jul-2025 21:58:38 Europe/Berlin] PHP Script started: 2025-07-02 21:58:38
[02-Jul-2025 21:58:38 Europe/Berlin] Database connection successful.
[02-Jul-2025 21:58:38 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 21:58:38 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 21:58:51 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 21:58:51
[02-Jul-2025 21:58:51 Europe/Berlin] Database connection successful.
[02-Jul-2025 21:58:51 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 21:58:51 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 21:59:30 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 21:59:30
[02-Jul-2025 21:59:30 Europe/Berlin] Database connection successful.
[02-Jul-2025 21:59:30 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 21:59:30 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 21:59:32 Europe/Berlin] PHP Script started: 2025-07-02 21:59:32
[02-Jul-2025 21:59:32 Europe/Berlin] Database connection successful.
[02-Jul-2025 21:59:33 Europe/Berlin] PHP Script started: 2025-07-02 21:59:33
[02-Jul-2025 21:59:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 21:59:33 Europe/Berlin] PHP Script started: 2025-07-02 21:59:33
[02-Jul-2025 21:59:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 21:59:33 Europe/Berlin] PHP Script started: 2025-07-02 21:59:33
[02-Jul-2025 21:59:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 21:59:33 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 21:59:33 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 21:59:41 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 21:59:41
[02-Jul-2025 21:59:41 Europe/Berlin] Database connection successful.
[02-Jul-2025 21:59:41 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 21:59:41 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 22:02:12 Europe/Berlin] PHP Script started: 2025-07-02 22:02:12
[02-Jul-2025 22:02:12 Europe/Berlin] Database connection successful.
[02-Jul-2025 22:02:14 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 22:02:14
[02-Jul-2025 22:02:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 22:02:14 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 22:02:14 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 22:02:14 Europe/Berlin] PHP Script started: 2025-07-02 22:02:14
[02-Jul-2025 22:02:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 22:02:14 Europe/Berlin] PHP Script started: 2025-07-02 22:02:14
[02-Jul-2025 22:02:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 22:02:14 Europe/Berlin] PHP Script started: 2025-07-02 22:02:14
[02-Jul-2025 22:02:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 22:02:14 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 22:02:14 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 22:02:20 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 22:02:20
[02-Jul-2025 22:02:20 Europe/Berlin] Database connection successful.
[02-Jul-2025 22:02:20 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 22:02:20 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 22:02:47 Europe/Berlin] PHP Script started: 2025-07-02 22:02:47
[02-Jul-2025 22:02:47 Europe/Berlin] Database connection successful.
[02-Jul-2025 22:02:47 Europe/Berlin] getLecturesByAssignment called with assignmentId: 1
[02-Jul-2025 22:02:47 Europe/Berlin] getLecturesByAssignment: Prepared statement successfully.
[02-Jul-2025 22:02:47 Europe/Berlin] getLecturesByAssignment: Parameters bound.
[02-Jul-2025 22:02:47 Europe/Berlin] getLecturesByAssignment: Execute command sent.
[02-Jul-2025 22:02:47 Europe/Berlin] getLecturesByAssignment: Got result set.
[02-Jul-2025 22:02:47 Europe/Berlin] getLecturesByAssignment: Successfully fetched 2 lectures.
[02-Jul-2025 22:02:55 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 22:02:55
[02-Jul-2025 22:02:55 Europe/Berlin] Database connection successful.
[02-Jul-2025 22:02:55 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 22:02:55 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[03-Jul-2025 16:17:34 Europe/Berlin] PHP Script (masege.php) started: 2025-07-03 16:17:34
[03-Jul-2025 16:17:34 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:17:34 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[03-Jul-2025 16:17:34 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[03-Jul-2025 16:17:56 Europe/Berlin] PHP Script (masege.php) started: 2025-07-03 16:17:56
[03-Jul-2025 16:17:56 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:17:56 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[03-Jul-2025 16:17:56 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[03-Jul-2025 16:18:22 Europe/Berlin] PHP Script started: 2025-07-03 16:18:22
[03-Jul-2025 16:18:22 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:18:24 Europe/Berlin] PHP Script started: 2025-07-03 16:18:24
[03-Jul-2025 16:18:24 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:18:24 Europe/Berlin] PHP Script (masege.php) started: 2025-07-03 16:18:24
[03-Jul-2025 16:18:24 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:18:24 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[03-Jul-2025 16:18:24 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[03-Jul-2025 16:18:25 Europe/Berlin] PHP Script started: 2025-07-03 16:18:25
[03-Jul-2025 16:18:25 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:18:25 Europe/Berlin] PHP Script started: 2025-07-03 16:18:25
[03-Jul-2025 16:18:25 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:18:25 Europe/Berlin] getFacultyResearch called with facultyId: 1
[03-Jul-2025 16:18:25 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[03-Jul-2025 16:18:38 Europe/Berlin] PHP Script (masege.php) started: 2025-07-03 16:18:38
[03-Jul-2025 16:18:38 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:18:38 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[03-Jul-2025 16:18:38 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[03-Jul-2025 16:19:04 Europe/Berlin] PHP Script (masege.php) started: 2025-07-03 16:19:04
[03-Jul-2025 16:19:04 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:19:04 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[03-Jul-2025 16:19:04 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[03-Jul-2025 16:19:45 Europe/Berlin] PHP Script (masege.php) started: 2025-07-03 16:19:45
[03-Jul-2025 16:19:45 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:19:45 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[03-Jul-2025 16:19:45 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[03-Jul-2025 16:22:13 Europe/Berlin] PHP Script (masege.php) started: 2025-07-03 16:22:13
[03-Jul-2025 16:22:13 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:22:13 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[03-Jul-2025 16:22:13 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[03-Jul-2025 16:22:28 Europe/Berlin] PHP Script (masege.php) started: 2025-07-03 16:22:28
[03-Jul-2025 16:22:28 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:22:28 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[03-Jul-2025 16:22:28 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[03-Jul-2025 16:30:34 Europe/Berlin] PHP Script started: 2025-07-03 16:30:34
[03-Jul-2025 16:30:34 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:30:36 Europe/Berlin] PHP Script started: 2025-07-03 16:30:36
[03-Jul-2025 16:30:36 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:30:36 Europe/Berlin] PHP Script (masege.php) started: 2025-07-03 16:30:36
[03-Jul-2025 16:30:36 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:30:36 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[03-Jul-2025 16:30:36 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[03-Jul-2025 16:30:38 Europe/Berlin] PHP Script started: 2025-07-03 16:30:38
[03-Jul-2025 16:30:38 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:30:38 Europe/Berlin] PHP Script started: 2025-07-03 16:30:38
[03-Jul-2025 16:30:38 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:30:38 Europe/Berlin] getFacultyResearch called with facultyId: 1
[03-Jul-2025 16:30:38 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[03-Jul-2025 16:30:46 Europe/Berlin] PHP Script (masege.php) started: 2025-07-03 16:30:46
[03-Jul-2025 16:30:46 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:30:46 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[03-Jul-2025 16:30:46 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[03-Jul-2025 16:30:49 Europe/Berlin] PHP Script (masege.php) started: 2025-07-03 16:30:49
[03-Jul-2025 16:30:49 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:30:49 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[03-Jul-2025 16:30:49 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":4,"to_name":"ali","to_email":"<EMAIL>","subject":"Re: try reply","content":"jfdajhvkk\n\n-- \u0627\u0644\u0631\u0633\u0627\u0644\u0629 \u0627\u0644\u0623\u0635\u0644\u064a\u0629 --\n\u0645\u0646: ali\n\u0627\u0644\u0645\u0648\u0636\u0648\u0639: try\n\u0627\u0644\u062a\u0627\u0631\u064a\u062e: 2025-06-28 02:55:50\n\ntry inbox masseges","datesent":"2025-06-29 15:48:21"},{"id":3,"to_name":"sader","to_email":"<EMAIL>","subject":"try scound","content":"chvj jkjhkj","datesent":"2025-06-29 15:47:49"},{"id":1,"to_name":"ahmed","to_email":"<EMAIL>","subject":"try send","content":"try send folder","datesent":"2025-06-28 02:57:01"}]}
[03-Jul-2025 16:31:02 Europe/Berlin] PHP Script (masege.php) started: 2025-07-03 16:31:02
[03-Jul-2025 16:31:02 Europe/Berlin] Database connection successful.
[03-Jul-2025 16:31:02 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[03-Jul-2025 16:31:02 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
