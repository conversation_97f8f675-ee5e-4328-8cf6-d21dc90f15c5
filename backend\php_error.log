[01-Jul-2025 22:40:01 Europe/Berlin] <PERSON><PERSON> (masege.php) started: 2025-07-01 22:40:01
[01-Jul-2025 22:40:01 Europe/Berlin] Database connection successful.
[01-Jul-2025 22:40:01 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 22:40:01 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 22:43:13 Europe/Berlin] P<PERSON> (masege.php) started: 2025-07-01 22:43:13
[01-Jul-2025 22:43:13 Europe/Berlin] Database connection successful.
[01-Jul-2025 22:43:13 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 22:43:13 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 22:44:06 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 22:44:06
[01-Jul-2025 22:44:06 Europe/Berlin] Database connection successful.
[01-Jul-2025 22:44:06 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 22:44:06 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 22:44:36 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 22:44:36
[01-Jul-2025 22:44:36 Europe/Berlin] Database connection successful.
[01-Jul-2025 22:44:36 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 22:44:36 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 23:22:45 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 23:22:44
[01-Jul-2025 23:22:45 Europe/Berlin] Database connection successful.
[01-Jul-2025 23:22:45 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 23:22:45 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
