[01-Jul-2025 22:40:01 Europe/Berlin] <PERSON><PERSON> (masege.php) started: 2025-07-01 22:40:01
[01-Jul-2025 22:40:01 Europe/Berlin] Database connection successful.
[01-Jul-2025 22:40:01 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 22:40:01 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 22:43:13 Europe/Berlin] P<PERSON> (masege.php) started: 2025-07-01 22:43:13
[01-Jul-2025 22:43:13 Europe/Berlin] Database connection successful.
[01-Jul-2025 22:43:13 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 22:43:13 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 22:44:06 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 22:44:06
[01-Jul-2025 22:44:06 Europe/Berlin] Database connection successful.
[01-Jul-2025 22:44:06 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 22:44:06 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 22:44:36 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 22:44:36
[01-Jul-2025 22:44:36 Europe/Berlin] Database connection successful.
[01-Jul-2025 22:44:36 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 22:44:36 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 23:22:45 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 23:22:44
[01-Jul-2025 23:22:45 Europe/Berlin] Database connection successful.
[01-Jul-2025 23:22:45 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 23:22:45 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 23:51:02 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 23:51:01
[01-Jul-2025 23:51:02 Europe/Berlin] Database connection successful.
[01-Jul-2025 23:51:02 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 23:51:02 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 23:52:34 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 23:52:34
[01-Jul-2025 23:52:34 Europe/Berlin] Database connection successful.
[01-Jul-2025 23:52:34 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 23:52:34 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 23:52:43 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 23:52:43
[01-Jul-2025 23:52:43 Europe/Berlin] Database connection successful.
[01-Jul-2025 23:52:43 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 23:52:43 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[01-Jul-2025 23:52:51 Europe/Berlin] PHP Script (masege.php) started: 2025-07-01 23:52:51
[01-Jul-2025 23:52:51 Europe/Berlin] Database connection successful.
[01-Jul-2025 23:52:51 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[01-Jul-2025 23:52:51 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 00:00:11 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 00:00:11
[02-Jul-2025 00:00:11 Europe/Berlin] Database connection successful.
[02-Jul-2025 00:00:11 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 00:00:11 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 00:01:50 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 00:01:50
[02-Jul-2025 00:01:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 00:01:50 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 00:01:50 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 00:57:04 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 00:57:04
[02-Jul-2025 00:57:04 Europe/Berlin] Database connection successful.
[02-Jul-2025 00:57:04 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 00:57:04 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:01:47 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:01:47
[02-Jul-2025 01:01:47 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:01:47 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:01:47 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:04:43 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:04:43
[02-Jul-2025 01:04:43 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:04:43 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:04:43 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:06:54 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:06:54
[02-Jul-2025 01:06:54 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:06:54 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:06:54 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:07:11 Europe/Berlin] PHP Script started: 2025-07-02 01:07:11
[02-Jul-2025 01:07:11 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:07:14 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:07:14
[02-Jul-2025 01:07:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:07:14 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:07:14 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:07:14 Europe/Berlin] PHP Script started: 2025-07-02 01:07:14
[02-Jul-2025 01:07:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:07:14 Europe/Berlin] PHP Script started: 2025-07-02 01:07:14
[02-Jul-2025 01:07:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:07:15 Europe/Berlin] PHP Script started: 2025-07-02 01:07:15
[02-Jul-2025 01:07:15 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:07:15 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 01:07:15 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 01:08:08 Europe/Berlin] PHP Script started: 2025-07-02 01:08:08
[02-Jul-2025 01:08:08 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:08 Europe/Berlin] DEBUG: updateFacultyCertificateImage called for facultyId: 1
[02-Jul-2025 01:08:08 Europe/Berlin] DEBUG: certificate imageData length: 1996243 bytes.
[02-Jul-2025 01:08:08 Europe/Berlin] DEBUG: Prepare statement successful for certificate.
[02-Jul-2025 01:08:08 Europe/Berlin] DEBUG: bind_param executed for certificate.
[02-Jul-2025 01:08:08 Europe/Berlin] ERROR: Exception in updateFacultyCertificateImage: Got a packet bigger than 'max_allowed_packet' bytes
[02-Jul-2025 01:08:08 Europe/Berlin] PHP Script started: 2025-07-02 01:08:08
[02-Jul-2025 01:08:08 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:20 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:08:20
[02-Jul-2025 01:08:20 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:20 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:08:20 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:08:20 Europe/Berlin] PHP Script started: 2025-07-02 01:08:20
[02-Jul-2025 01:08:20 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:21 Europe/Berlin] PHP Script started: 2025-07-02 01:08:21
[02-Jul-2025 01:08:21 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:21 Europe/Berlin] PHP Script started: 2025-07-02 01:08:21
[02-Jul-2025 01:08:21 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:21 Europe/Berlin] PHP Script started: 2025-07-02 01:08:21
[02-Jul-2025 01:08:21 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:21 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 01:08:21 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 01:08:32 Europe/Berlin] PHP Script started: 2025-07-02 01:08:32
[02-Jul-2025 01:08:32 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:08:32 Europe/Berlin] DEBUG: updateFacultyCertificateImage called for facultyId: 1
[02-Jul-2025 01:08:32 Europe/Berlin] DEBUG: certificate imageData length: 1996243 bytes.
[02-Jul-2025 01:08:32 Europe/Berlin] DEBUG: Prepare statement successful for certificate.
[02-Jul-2025 01:08:32 Europe/Berlin] DEBUG: bind_param executed for certificate.
[02-Jul-2025 01:08:32 Europe/Berlin] ERROR: Exception in updateFacultyCertificateImage: Got a packet bigger than 'max_allowed_packet' bytes
[02-Jul-2025 01:08:32 Europe/Berlin] PHP Warning:  main(): Error occurred while closing statement in C:\xampp\htdocs\faculty_web\backend\faculty_control.php on line 889
[02-Jul-2025 01:08:32 Europe/Berlin] PHP Script started: 2025-07-02 01:08:32
[02-Jul-2025 01:08:32 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:11:44 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:11:44
[02-Jul-2025 01:11:44 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:11:44 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:11:44 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:16:14 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:16:14
[02-Jul-2025 01:16:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:16:14 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:16:14 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:16:27 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:16:27
[02-Jul-2025 01:16:27 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:16:27 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:16:27 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:16:40 Europe/Berlin] PHP Script started: 2025-07-02 01:16:40
[02-Jul-2025 01:16:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:16:40 Europe/Berlin] getLecturesByAssignment called with assignmentId: 1
[02-Jul-2025 01:16:40 Europe/Berlin] getLecturesByAssignment: Prepared statement successfully.
[02-Jul-2025 01:16:40 Europe/Berlin] getLecturesByAssignment: Parameters bound.
[02-Jul-2025 01:16:40 Europe/Berlin] getLecturesByAssignment: Execute command sent.
[02-Jul-2025 01:16:40 Europe/Berlin] getLecturesByAssignment: Got result set.
[02-Jul-2025 01:16:40 Europe/Berlin] getLecturesByAssignment: Successfully fetched 2 lectures.
[02-Jul-2025 01:18:03 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:18:03
[02-Jul-2025 01:18:03 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:18:03 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:18:03 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:18:33 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:18:33
[02-Jul-2025 01:18:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:18:33 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:18:33 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:19:29 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:19:29
[02-Jul-2025 01:19:29 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:19:29 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:19:29 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:20:35 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:20:35
[02-Jul-2025 01:20:35 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:20:35 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:20:35 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:25:40 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:25:40
[02-Jul-2025 01:25:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:25:40 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:25:40 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:27:19 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:27:19
[02-Jul-2025 01:27:19 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:27:19 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:27:19 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:36:51 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:36:51
[02-Jul-2025 01:36:51 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:36:51 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:36:51 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:37:38 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:37:38
[02-Jul-2025 01:37:38 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:37:38 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:37:38 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:42:55 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:42:55
[02-Jul-2025 01:42:55 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:42:55 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:42:55 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:43:24 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:43:24
[02-Jul-2025 01:43:24 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:43:24 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:43:24 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:45:21 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:45:21
[02-Jul-2025 01:45:21 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:45:21 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:45:21 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:48:08 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:48:08
[02-Jul-2025 01:48:08 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:48:08 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:48:08 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:51:16 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:51:16
[02-Jul-2025 01:51:16 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:51:16 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:51:16 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 01:52:08 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 01:52:08
[02-Jul-2025 01:52:08 Europe/Berlin] Database connection successful.
[02-Jul-2025 01:52:08 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 01:52:08 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:03:16 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:03:16
[02-Jul-2025 02:03:16 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:03:16 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:03:16 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:03:53 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:03:53
[02-Jul-2025 02:03:53 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:03:53 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:03:53 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:04:06 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:04:06
[02-Jul-2025 02:04:06 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:04:06 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:04:06 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:07:16 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:07:16
[02-Jul-2025 02:07:16 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:07:16 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:07:16 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:08:02 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:08:02
[02-Jul-2025 02:08:02 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:08:02 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:08:02 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:10:59 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:10:59
[02-Jul-2025 02:10:59 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:10:59 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:10:59 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:11:05 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:11:05
[02-Jul-2025 02:11:05 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:11:05 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:11:05 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:12:23 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:12:23
[02-Jul-2025 02:12:23 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:12:23 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:12:23 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:12:37 Europe/Berlin] PHP Script started: 2025-07-02 02:12:37
[02-Jul-2025 02:12:37 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:12:39 Europe/Berlin] PHP Script started: 2025-07-02 02:12:39
[02-Jul-2025 02:12:39 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:12:39 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:12:39
[02-Jul-2025 02:12:39 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:12:39 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:12:39 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:12:40 Europe/Berlin] PHP Script started: 2025-07-02 02:12:40
[02-Jul-2025 02:12:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:12:40 Europe/Berlin] PHP Script started: 2025-07-02 02:12:40
[02-Jul-2025 02:12:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:12:40 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 02:12:40 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 02:12:49 Europe/Berlin] PHP Script started: 2025-07-02 02:12:49
[02-Jul-2025 02:12:49 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:12:49 Europe/Berlin] getDepartmentMaterials called with departmentId: 1
[02-Jul-2025 02:12:49 Europe/Berlin] getDepartmentMaterials: Successfully fetched 4 materials.
[02-Jul-2025 02:12:52 Europe/Berlin] PHP Script started: 2025-07-02 02:12:52
[02-Jul-2025 02:12:52 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:12:52 Europe/Berlin] getLecturesByAssignment called with assignmentId: 1
[02-Jul-2025 02:12:52 Europe/Berlin] getLecturesByAssignment: Prepared statement successfully.
[02-Jul-2025 02:12:52 Europe/Berlin] getLecturesByAssignment: Parameters bound.
[02-Jul-2025 02:12:52 Europe/Berlin] getLecturesByAssignment: Execute command sent.
[02-Jul-2025 02:12:52 Europe/Berlin] getLecturesByAssignment: Got result set.
[02-Jul-2025 02:12:52 Europe/Berlin] getLecturesByAssignment: Successfully fetched 2 lectures.
[02-Jul-2025 02:12:54 Europe/Berlin] PHP Script started: 2025-07-02 02:12:54
[02-Jul-2025 02:12:54 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:12:54 Europe/Berlin] getLecturesByAssignment called with assignmentId: 2
[02-Jul-2025 02:12:54 Europe/Berlin] getLecturesByAssignment: Prepared statement successfully.
[02-Jul-2025 02:12:54 Europe/Berlin] getLecturesByAssignment: Parameters bound.
[02-Jul-2025 02:12:54 Europe/Berlin] getLecturesByAssignment: Execute command sent.
[02-Jul-2025 02:12:54 Europe/Berlin] getLecturesByAssignment: Got result set.
[02-Jul-2025 02:12:54 Europe/Berlin] getLecturesByAssignment: Successfully fetched 2 lectures.
[02-Jul-2025 02:14:07 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:14:07
[02-Jul-2025 02:14:07 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:14:07 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:14:07 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:14:24 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:14:24
[02-Jul-2025 02:14:24 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:14:24 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:14:24 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:14:27 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:14:27
[02-Jul-2025 02:14:27 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:14:27 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:14:27 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:14:50 Europe/Berlin] PHP Script started: 2025-07-02 02:14:50
[02-Jul-2025 02:14:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:14:52 Europe/Berlin] PHP Script started: 2025-07-02 02:14:52
[02-Jul-2025 02:14:52 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:14:52 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:14:52
[02-Jul-2025 02:14:52 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:14:52 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:14:52 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:14:53 Europe/Berlin] PHP Script started: 2025-07-02 02:14:53
[02-Jul-2025 02:14:53 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:14:53 Europe/Berlin] PHP Script started: 2025-07-02 02:14:53
[02-Jul-2025 02:14:53 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:14:53 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 02:14:53 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 02:15:19 Europe/Berlin] PHP Script started: 2025-07-02 02:15:19
[02-Jul-2025 02:15:19 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:15:19 Europe/Berlin] DEBUG: updateFacultyCertificateImage called for facultyId: 1
[02-Jul-2025 02:15:19 Europe/Berlin] DEBUG: certificate imageData length: 1996243 bytes.
[02-Jul-2025 02:15:19 Europe/Berlin] DEBUG: Prepare statement successful for certificate.
[02-Jul-2025 02:15:19 Europe/Berlin] DEBUG: bind_param executed for certificate.
[02-Jul-2025 02:15:19 Europe/Berlin] ERROR: Exception in updateFacultyCertificateImage: Got a packet bigger than 'max_allowed_packet' bytes
[02-Jul-2025 02:15:19 Europe/Berlin] PHP Script started: 2025-07-02 02:15:19
[02-Jul-2025 02:15:19 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:15:25 Europe/Berlin] PHP Script started: 2025-07-02 02:15:25
[02-Jul-2025 02:15:25 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:15:25 Europe/Berlin] PHP Script started: 2025-07-02 02:15:25
[02-Jul-2025 02:15:25 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:15:25
[02-Jul-2025 02:15:25 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:15:25 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:15:25 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:15:25 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:15:25 Europe/Berlin] PHP Script started: 2025-07-02 02:15:25
[02-Jul-2025 02:15:25 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:15:26 Europe/Berlin] PHP Script started: 2025-07-02 02:15:26
[02-Jul-2025 02:15:26 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:15:26 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 02:15:26 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 02:16:31 Europe/Berlin] PHP Script started: 2025-07-02 02:16:31
[02-Jul-2025 02:16:31 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:16:32 Europe/Berlin] PHP Script started: 2025-07-02 02:16:32
[02-Jul-2025 02:16:32 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:16:40 Europe/Berlin] PHP Script started: 2025-07-02 02:16:40
[02-Jul-2025 02:16:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:16:40 Europe/Berlin] PHP Script started: 2025-07-02 02:16:40
[02-Jul-2025 02:16:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:16:54 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:16:54
[02-Jul-2025 02:16:54 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:16:54 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:16:54 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:17:00 Europe/Berlin] PHP Script started: 2025-07-02 02:17:00
[02-Jul-2025 02:17:00 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:00 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:17:00
[02-Jul-2025 02:17:00 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:00 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:17:00 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:17:01 Europe/Berlin] PHP Script started: 2025-07-02 02:17:01
[02-Jul-2025 02:17:01 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:01 Europe/Berlin] PHP Script started: 2025-07-02 02:17:01
[02-Jul-2025 02:17:01 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:01 Europe/Berlin] PHP Script started: 2025-07-02 02:17:01
[02-Jul-2025 02:17:01 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:01 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 02:17:01 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 02:17:20 Europe/Berlin] PHP Script started: 2025-07-02 02:17:20
[02-Jul-2025 02:17:20 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:20 Europe/Berlin] PHP Script started: 2025-07-02 02:17:20
[02-Jul-2025 02:17:20 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:26 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:17:26
[02-Jul-2025 02:17:26 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:26 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:17:26 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:17:26 Europe/Berlin] PHP Script started: 2025-07-02 02:17:26
[02-Jul-2025 02:17:26 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:27 Europe/Berlin] PHP Script started: 2025-07-02 02:17:27
[02-Jul-2025 02:17:27 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:27 Europe/Berlin] PHP Script started: 2025-07-02 02:17:27
[02-Jul-2025 02:17:27 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:27 Europe/Berlin] PHP Script started: 2025-07-02 02:17:27
[02-Jul-2025 02:17:27 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:27 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 02:17:27 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 02:17:48 Europe/Berlin] PHP Script started: 2025-07-02 02:17:48
[02-Jul-2025 02:17:48 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:48 Europe/Berlin] getLecturesByAssignment called with assignmentId: 3
[02-Jul-2025 02:17:48 Europe/Berlin] getLecturesByAssignment: Prepared statement successfully.
[02-Jul-2025 02:17:48 Europe/Berlin] getLecturesByAssignment: Parameters bound.
[02-Jul-2025 02:17:48 Europe/Berlin] getLecturesByAssignment: Execute command sent.
[02-Jul-2025 02:17:48 Europe/Berlin] getLecturesByAssignment: Got result set.
[02-Jul-2025 02:17:48 Europe/Berlin] getLecturesByAssignment: Successfully fetched 1 lectures.
[02-Jul-2025 02:17:52 Europe/Berlin] PHP Script started: 2025-07-02 02:17:52
[02-Jul-2025 02:17:52 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:52 Europe/Berlin] toggleArchiveMaterial: Called with MaterialID: 6, New Status: 0
[02-Jul-2025 02:17:52 Europe/Berlin] toggleArchiveMaterial: Status updated successfully for MaterialID: 6 to 0
[02-Jul-2025 02:17:52 Europe/Berlin] PHP Script started: 2025-07-02 02:17:52
[02-Jul-2025 02:17:52 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:56 Europe/Berlin] PHP Script started: 2025-07-02 02:17:56
[02-Jul-2025 02:17:56 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:57 Europe/Berlin] PHP Script started: 2025-07-02 02:17:57
[02-Jul-2025 02:17:57 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:57 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:17:57
[02-Jul-2025 02:17:57 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:57 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:17:57 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:17:57 Europe/Berlin] PHP Script started: 2025-07-02 02:17:57
[02-Jul-2025 02:17:57 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:57 Europe/Berlin] PHP Script started: 2025-07-02 02:17:57
[02-Jul-2025 02:17:57 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:17:58 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 02:17:58 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 02:18:03 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:18:03
[02-Jul-2025 02:18:03 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:18:03 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:18:03 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:20:44 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:20:44
[02-Jul-2025 02:20:44 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:20:44 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:20:44 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:25:07 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:25:07
[02-Jul-2025 02:25:07 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:25:07 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:25:07 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:25:14 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:25:14
[02-Jul-2025 02:25:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:25:14 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:25:14 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:25:23 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:25:23
[02-Jul-2025 02:25:23 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:25:23 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:25:23 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:25:40 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:25:40
[02-Jul-2025 02:25:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:25:40 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:25:40 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:27:28 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:27:28
[02-Jul-2025 02:27:28 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:27:28 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:27:28 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:27:37 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:27:37
[02-Jul-2025 02:27:37 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:27:37 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:27:37 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:30:47 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:30:47
[02-Jul-2025 02:30:47 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:30:47 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:30:47 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:34:07 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:34:07
[02-Jul-2025 02:34:07 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:34:07 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:34:07 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:34:32 Europe/Berlin] PHP Script started: 2025-07-02 02:34:32
[02-Jul-2025 02:34:32 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:34:32 Europe/Berlin] getLecturesByAssignment called with assignmentId: 3
[02-Jul-2025 02:34:32 Europe/Berlin] getLecturesByAssignment: Prepared statement successfully.
[02-Jul-2025 02:34:32 Europe/Berlin] getLecturesByAssignment: Parameters bound.
[02-Jul-2025 02:34:32 Europe/Berlin] getLecturesByAssignment: Execute command sent.
[02-Jul-2025 02:34:32 Europe/Berlin] getLecturesByAssignment: Got result set.
[02-Jul-2025 02:34:32 Europe/Berlin] getLecturesByAssignment: Successfully fetched 1 lectures.
[02-Jul-2025 02:34:33 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:34:33
[02-Jul-2025 02:34:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:34:33 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:34:33 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:34:33 Europe/Berlin] PHP Script started: 2025-07-02 02:34:33
[02-Jul-2025 02:34:33 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:34:33 Europe/Berlin] toggleArchiveMaterial: Called with MaterialID: 6, New Status: 1
[02-Jul-2025 02:34:34 Europe/Berlin] toggleArchiveMaterial: Status updated successfully for MaterialID: 6 to 1
[02-Jul-2025 02:34:34 Europe/Berlin] PHP Script started: 2025-07-02 02:34:34
[02-Jul-2025 02:34:34 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:34:37 Europe/Berlin] PHP Script started: 2025-07-02 02:34:37
[02-Jul-2025 02:34:37 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:34:37 Europe/Berlin] getLecturesByAssignment called with assignmentId: 1
[02-Jul-2025 02:34:37 Europe/Berlin] getLecturesByAssignment: Prepared statement successfully.
[02-Jul-2025 02:34:37 Europe/Berlin] getLecturesByAssignment: Parameters bound.
[02-Jul-2025 02:34:38 Europe/Berlin] getLecturesByAssignment: Execute command sent.
[02-Jul-2025 02:34:38 Europe/Berlin] getLecturesByAssignment: Got result set.
[02-Jul-2025 02:34:38 Europe/Berlin] getLecturesByAssignment: Successfully fetched 2 lectures.
[02-Jul-2025 02:34:39 Europe/Berlin] PHP Script started: 2025-07-02 02:34:39
[02-Jul-2025 02:34:39 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:34:39 Europe/Berlin] getLecturesByAssignment called with assignmentId: 2
[02-Jul-2025 02:34:39 Europe/Berlin] getLecturesByAssignment: Prepared statement successfully.
[02-Jul-2025 02:34:39 Europe/Berlin] getLecturesByAssignment: Parameters bound.
[02-Jul-2025 02:34:39 Europe/Berlin] getLecturesByAssignment: Execute command sent.
[02-Jul-2025 02:34:39 Europe/Berlin] getLecturesByAssignment: Got result set.
[02-Jul-2025 02:34:39 Europe/Berlin] getLecturesByAssignment: Successfully fetched 2 lectures.
[02-Jul-2025 02:34:58 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:34:58
[02-Jul-2025 02:34:58 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:34:58 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:34:58 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:37:19 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:37:19
[02-Jul-2025 02:37:19 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:37:19 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:37:19 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:37:57 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:37:57
[02-Jul-2025 02:37:57 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:37:57 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:37:57 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:38:36 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:38:36
[02-Jul-2025 02:38:36 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:38:36 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:38:36 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:41:29 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:41:29
[02-Jul-2025 02:41:29 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:41:29 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:41:29 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:42:55 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:42:55
[02-Jul-2025 02:42:55 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:42:55 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:42:55 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:43:14 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:43:14
[02-Jul-2025 02:43:14 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:43:14 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:43:14 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:45:46 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:45:46
[02-Jul-2025 02:45:46 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:45:46 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:45:46 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:46:42 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:46:42
[02-Jul-2025 02:46:42 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:46:42 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:46:42 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:47:23 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:47:23
[02-Jul-2025 02:47:23 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:47:23 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:47:23 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:49:22 Europe/Berlin] PHP Script started: 2025-07-02 02:49:22
[02-Jul-2025 02:49:22 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:49:22 Europe/Berlin] DEBUG: updateFacultyCertificateImage called for facultyId: 1
[02-Jul-2025 02:49:22 Europe/Berlin] DEBUG: certificate imageData length: 1996243 bytes.
[02-Jul-2025 02:49:22 Europe/Berlin] DEBUG: Prepare statement successful for certificate.
[02-Jul-2025 02:49:22 Europe/Berlin] DEBUG: bind_param executed for certificate.
[02-Jul-2025 02:49:22 Europe/Berlin] ERROR: Exception in updateFacultyCertificateImage: Got a packet bigger than 'max_allowed_packet' bytes
[02-Jul-2025 02:49:22 Europe/Berlin] PHP Warning:  main(): Error occurred while closing statement in C:\xampp\htdocs\faculty_web\backend\faculty_control.php on line 889
[02-Jul-2025 02:49:22 Europe/Berlin] PHP Script started: 2025-07-02 02:49:22
[02-Jul-2025 02:49:22 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:49:38 Europe/Berlin] PHP Script started: 2025-07-02 02:49:38
[02-Jul-2025 02:49:38 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:49:40 Europe/Berlin] PHP Script started: 2025-07-02 02:49:40
[02-Jul-2025 02:49:40 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:49:40
[02-Jul-2025 02:49:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:49:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:49:40 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:49:40 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:49:40 Europe/Berlin] PHP Script started: 2025-07-02 02:49:40
[02-Jul-2025 02:49:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:49:41 Europe/Berlin] PHP Script started: 2025-07-02 02:49:41
[02-Jul-2025 02:49:41 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:49:41 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 02:49:41 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 02:49:50 Europe/Berlin] PHP Script started: 2025-07-02 02:49:50
[02-Jul-2025 02:49:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:49:50 Europe/Berlin] PHP Script started: 2025-07-02 02:49:50
[02-Jul-2025 02:49:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:51:52 Europe/Berlin] PHP Script started: 2025-07-02 02:51:52
[02-Jul-2025 02:51:52 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:51:52 Europe/Berlin] DEBUG: updateFacultyCertificateImage called for facultyId: 1
[02-Jul-2025 02:51:52 Europe/Berlin] DEBUG: certificate imageData length: 8846 bytes.
[02-Jul-2025 02:51:52 Europe/Berlin] DEBUG: Prepare statement successful for certificate.
[02-Jul-2025 02:51:52 Europe/Berlin] DEBUG: bind_param executed for certificate.
[02-Jul-2025 02:51:53 Europe/Berlin] DEBUG: Execute statement successful for certificate. Rows affected: 1
[02-Jul-2025 02:51:53 Europe/Berlin] PHP Script started: 2025-07-02 02:51:53
[02-Jul-2025 02:51:53 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:52:43 Europe/Berlin] PHP Script started: 2025-07-02 02:52:43
[02-Jul-2025 02:52:43 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:52:43 Europe/Berlin] DEBUG: updateFacultyCertificateImage called for facultyId: 1
[02-Jul-2025 02:52:43 Europe/Berlin] DEBUG: certificate imageData length: 138902 bytes.
[02-Jul-2025 02:52:43 Europe/Berlin] DEBUG: Prepare statement successful for certificate.
[02-Jul-2025 02:52:43 Europe/Berlin] DEBUG: bind_param executed for certificate.
[02-Jul-2025 02:52:43 Europe/Berlin] DEBUG: Execute statement successful for certificate. Rows affected: 1
[02-Jul-2025 02:52:43 Europe/Berlin] PHP Script started: 2025-07-02 02:52:43
[02-Jul-2025 02:52:43 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:52:54 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:52:54
[02-Jul-2025 02:52:54 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:52:54 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:52:54 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:53:12 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:53:12
[02-Jul-2025 02:53:12 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:53:12 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:53:12 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:53:26 Europe/Berlin] PHP Script started: 2025-07-02 02:53:26
[02-Jul-2025 02:53:26 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:53:26 Europe/Berlin] PHP Script started: 2025-07-02 02:53:26
[02-Jul-2025 02:53:26 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:53:42 Europe/Berlin] PHP Script started: 2025-07-02 02:53:42
[02-Jul-2025 02:53:42 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:53:42 Europe/Berlin] DEBUG: updateFacultyCertificateImage called for facultyId: 1
[02-Jul-2025 02:53:42 Europe/Berlin] DEBUG: certificate imageData length: 8846 bytes.
[02-Jul-2025 02:53:42 Europe/Berlin] DEBUG: Prepare statement successful for certificate.
[02-Jul-2025 02:53:42 Europe/Berlin] DEBUG: bind_param executed for certificate.
[02-Jul-2025 02:53:42 Europe/Berlin] DEBUG: Execute statement successful for certificate. Rows affected: 1
[02-Jul-2025 02:53:42 Europe/Berlin] PHP Script started: 2025-07-02 02:53:42
[02-Jul-2025 02:53:42 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:53:49 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:53:49
[02-Jul-2025 02:53:49 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:53:49 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:53:49 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:57:54 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:57:54
[02-Jul-2025 02:57:54 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:57:54 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:57:54 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:58:31 Europe/Berlin] PHP Script started: 2025-07-02 02:58:31
[02-Jul-2025 02:58:31 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:58:31 Europe/Berlin] getLecturesByAssignment called with assignmentId: 1
[02-Jul-2025 02:58:31 Europe/Berlin] getLecturesByAssignment: Prepared statement successfully.
[02-Jul-2025 02:58:31 Europe/Berlin] getLecturesByAssignment: Parameters bound.
[02-Jul-2025 02:58:31 Europe/Berlin] getLecturesByAssignment: Execute command sent.
[02-Jul-2025 02:58:31 Europe/Berlin] getLecturesByAssignment: Got result set.
[02-Jul-2025 02:58:31 Europe/Berlin] getLecturesByAssignment: Successfully fetched 2 lectures.
[02-Jul-2025 02:59:52 Europe/Berlin] PHP Script started: 2025-07-02 02:59:52
[02-Jul-2025 02:59:52 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:59:54 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 02:59:54
[02-Jul-2025 02:59:54 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:59:54 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 02:59:54 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 02:59:54 Europe/Berlin] PHP Script started: 2025-07-02 02:59:54
[02-Jul-2025 02:59:54 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:59:54 Europe/Berlin] PHP Script started: 2025-07-02 02:59:54
[02-Jul-2025 02:59:54 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:59:55 Europe/Berlin] PHP Script started: 2025-07-02 02:59:55
[02-Jul-2025 02:59:55 Europe/Berlin] Database connection successful.
[02-Jul-2025 02:59:55 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 02:59:55 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 03:00:03 Europe/Berlin] PHP Script started: 2025-07-02 03:00:03
[02-Jul-2025 03:00:03 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:00:03 Europe/Berlin] DEBUG: updateFacultyCertificateImage called for facultyId: 1
[02-Jul-2025 03:00:03 Europe/Berlin] DEBUG: certificate imageData length: 423216 bytes.
[02-Jul-2025 03:00:03 Europe/Berlin] DEBUG: Prepare statement successful for certificate.
[02-Jul-2025 03:00:03 Europe/Berlin] DEBUG: bind_param executed for certificate.
[02-Jul-2025 03:00:05 Europe/Berlin] DEBUG: Execute statement successful for certificate. Rows affected: 1
[02-Jul-2025 03:00:05 Europe/Berlin] PHP Script started: 2025-07-02 03:00:05
[02-Jul-2025 03:00:05 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:00:22 Europe/Berlin] PHP Script started: 2025-07-02 03:00:22
[02-Jul-2025 03:00:22 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:00:22 Europe/Berlin] DEBUG: updateFacultyCertificateImage called for facultyId: 1
[02-Jul-2025 03:00:22 Europe/Berlin] DEBUG: certificate imageData length: 1996243 bytes.
[02-Jul-2025 03:00:22 Europe/Berlin] DEBUG: Prepare statement successful for certificate.
[02-Jul-2025 03:00:22 Europe/Berlin] DEBUG: bind_param executed for certificate.
[02-Jul-2025 03:00:22 Europe/Berlin] ERROR: Exception in updateFacultyCertificateImage: Got a packet bigger than 'max_allowed_packet' bytes
[02-Jul-2025 03:00:22 Europe/Berlin] PHP Script started: 2025-07-02 03:00:22
[02-Jul-2025 03:00:22 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:00:50 Europe/Berlin] PHP Script started: 2025-07-02 03:00:50
[02-Jul-2025 03:00:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:00:50 Europe/Berlin] DEBUG: updateFacultyCertificateImage called for facultyId: 1
[02-Jul-2025 03:00:50 Europe/Berlin] DEBUG: certificate imageData length: 8846 bytes.
[02-Jul-2025 03:00:50 Europe/Berlin] DEBUG: Prepare statement successful for certificate.
[02-Jul-2025 03:00:50 Europe/Berlin] DEBUG: bind_param executed for certificate.
[02-Jul-2025 03:00:50 Europe/Berlin] DEBUG: Execute statement successful for certificate. Rows affected: 1
[02-Jul-2025 03:00:50 Europe/Berlin] PHP Script started: 2025-07-02 03:00:50
[02-Jul-2025 03:00:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:05:47 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:05:47
[02-Jul-2025 03:05:47 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:05:47 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:05:47 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:07:32 Europe/Berlin] PHP Script started: 2025-07-02 03:07:32
[02-Jul-2025 03:07:32 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:07:34 Europe/Berlin] PHP Script started: 2025-07-02 03:07:34
[02-Jul-2025 03:07:34 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:07:34 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:07:34
[02-Jul-2025 03:07:34 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:07:34 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:07:34 Europe/Berlin] PHP Script started: 2025-07-02 03:07:34
[02-Jul-2025 03:07:34 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:07:34 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:07:35 Europe/Berlin] PHP Script started: 2025-07-02 03:07:35
[02-Jul-2025 03:07:35 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:07:35 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 03:07:35 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 03:10:16 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:10:16
[02-Jul-2025 03:10:16 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:10:16 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:10:16 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:11:39 Europe/Berlin] PHP Script started: 2025-07-02 03:11:39
[02-Jul-2025 03:11:39 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:11:40 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:11:40
[02-Jul-2025 03:11:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:11:40 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:11:40 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:11:40 Europe/Berlin] PHP Script started: 2025-07-02 03:11:40
[02-Jul-2025 03:11:40 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:11:41 Europe/Berlin] PHP Script started: 2025-07-02 03:11:41
[02-Jul-2025 03:11:41 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:11:41 Europe/Berlin] PHP Script started: 2025-07-02 03:11:41
[02-Jul-2025 03:11:41 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:11:41 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 03:11:41 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 03:12:04 Europe/Berlin] PHP Script started: 2025-07-02 03:12:04
[02-Jul-2025 03:12:04 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:12:05 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:12:05
[02-Jul-2025 03:12:05 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:12:05 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:12:05 Europe/Berlin] PHP Script started: 2025-07-02 03:12:05
[02-Jul-2025 03:12:05 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:12:05 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:12:05 Europe/Berlin] PHP Script started: 2025-07-02 03:12:05
[02-Jul-2025 03:12:05 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:12:06 Europe/Berlin] PHP Script started: 2025-07-02 03:12:06
[02-Jul-2025 03:12:06 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:12:06 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 03:12:06 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 03:12:53 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:12:53
[02-Jul-2025 03:12:53 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:12:53 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:12:53 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:13:17 Europe/Berlin] PHP Script started: 2025-07-02 03:13:17
[02-Jul-2025 03:13:17 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:13:17 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:13:17
[02-Jul-2025 03:13:18 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:13:18 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:13:18 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:13:18 Europe/Berlin] PHP Script started: 2025-07-02 03:13:18
[02-Jul-2025 03:13:18 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:13:18 Europe/Berlin] PHP Script started: 2025-07-02 03:13:18
[02-Jul-2025 03:13:18 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:13:18 Europe/Berlin] PHP Script started: 2025-07-02 03:13:18
[02-Jul-2025 03:13:18 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:13:18 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 03:13:18 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 03:13:24 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:13:24
[02-Jul-2025 03:13:24 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:13:24 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:13:24 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:14:47 Europe/Berlin] PHP Script started: 2025-07-02 03:14:47
[02-Jul-2025 03:14:47 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:14:49 Europe/Berlin] PHP Script started: 2025-07-02 03:14:49
[02-Jul-2025 03:14:49 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:14:49 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:14:49
[02-Jul-2025 03:14:49 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:14:49 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:14:49 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:14:50 Europe/Berlin] PHP Script started: 2025-07-02 03:14:50
[02-Jul-2025 03:14:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:14:50 Europe/Berlin] PHP Script started: 2025-07-02 03:14:50
[02-Jul-2025 03:14:50 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:14:50 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 03:14:50 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 03:16:47 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:16:47
[02-Jul-2025 03:16:47 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:16:47 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:16:47 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:18:06 Europe/Berlin] PHP Script started: 2025-07-02 03:18:06
[02-Jul-2025 03:18:06 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:18:08 Europe/Berlin] PHP Script started: 2025-07-02 03:18:08
[02-Jul-2025 03:18:08 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:18:09 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:18:09
[02-Jul-2025 03:18:09 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:18:09 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:18:09 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:18:10 Europe/Berlin] PHP Script started: 2025-07-02 03:18:10
[02-Jul-2025 03:18:10 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:18:10 Europe/Berlin] PHP Script started: 2025-07-02 03:18:10
[02-Jul-2025 03:18:10 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:18:10 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 03:18:10 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 03:18:22 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:18:22
[02-Jul-2025 03:18:22 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:18:22 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:18:22 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:19:28 Europe/Berlin] PHP Script started: 2025-07-02 03:19:28
[02-Jul-2025 03:19:28 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:19:29 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:19:29
[02-Jul-2025 03:19:29 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:19:29 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:19:29 Europe/Berlin] PHP Script started: 2025-07-02 03:19:29
[02-Jul-2025 03:19:29 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:19:29 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:19:29 Europe/Berlin] PHP Script started: 2025-07-02 03:19:29
[02-Jul-2025 03:19:29 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:19:30 Europe/Berlin] PHP Script started: 2025-07-02 03:19:30
[02-Jul-2025 03:19:30 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:19:30 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 03:19:30 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 03:19:35 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:19:35
[02-Jul-2025 03:19:35 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:19:35 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:19:35 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:20:19 Europe/Berlin] PHP Script started: 2025-07-02 03:20:19
[02-Jul-2025 03:20:19 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:20:23 Europe/Berlin] PHP Script started: 2025-07-02 03:20:23
[02-Jul-2025 03:20:23 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:20:24 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:20:24
[02-Jul-2025 03:20:24 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:20:24 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:20:24 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:20:24 Europe/Berlin] PHP Script started: 2025-07-02 03:20:24
[02-Jul-2025 03:20:24 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:20:24 Europe/Berlin] PHP Script started: 2025-07-02 03:20:24
[02-Jul-2025 03:20:24 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:20:24 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 03:20:24 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 03:20:35 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:20:35
[02-Jul-2025 03:20:35 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:20:35 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:20:35 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:20:46 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:20:46
[02-Jul-2025 03:20:46 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:20:46 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:20:46 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:21:01 Europe/Berlin] PHP Script started: 2025-07-02 03:21:01
[02-Jul-2025 03:21:01 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:21:01 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:21:01
[02-Jul-2025 03:21:01 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:21:01 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:21:01 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:21:01 Europe/Berlin] PHP Script started: 2025-07-02 03:21:01
[02-Jul-2025 03:21:01 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:21:02 Europe/Berlin] PHP Script started: 2025-07-02 03:21:02
[02-Jul-2025 03:21:02 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:21:02 Europe/Berlin] PHP Script started: 2025-07-02 03:21:02
[02-Jul-2025 03:21:02 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:21:02 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 03:21:02 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 03:24:24 Europe/Berlin] PHP Script started: 2025-07-02 03:24:24
[02-Jul-2025 03:24:24 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:24:27 Europe/Berlin] PHP Script started: 2025-07-02 03:24:27
[02-Jul-2025 03:24:27 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:24:27 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:24:27
[02-Jul-2025 03:24:27 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:24:27 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:24:27 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:24:27 Europe/Berlin] PHP Script started: 2025-07-02 03:24:27
[02-Jul-2025 03:24:27 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:24:28 Europe/Berlin] PHP Script started: 2025-07-02 03:24:28
[02-Jul-2025 03:24:28 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:24:28 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 03:24:28 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 03:24:37 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:24:37
[02-Jul-2025 03:24:37 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:24:37 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:24:37 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:26:34 Europe/Berlin] PHP Script started: 2025-07-02 03:26:34
[02-Jul-2025 03:26:34 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:26:36 Europe/Berlin] PHP Script started: 2025-07-02 03:26:36
[02-Jul-2025 03:26:36 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:26:36 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:26:36
[02-Jul-2025 03:26:36 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:26:36 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:26:36 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:26:37 Europe/Berlin] PHP Script started: 2025-07-02 03:26:37
[02-Jul-2025 03:26:37 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:26:37 Europe/Berlin] PHP Script started: 2025-07-02 03:26:37
[02-Jul-2025 03:26:37 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:26:37 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 03:26:37 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 03:26:56 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:26:56
[02-Jul-2025 03:26:56 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:26:56 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:26:56 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:27:16 Europe/Berlin] PHP Script started: 2025-07-02 03:27:16
[02-Jul-2025 03:27:16 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:27:16 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:27:16
[02-Jul-2025 03:27:16 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:27:16 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:27:16 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:27:17 Europe/Berlin] PHP Script started: 2025-07-02 03:27:17
[02-Jul-2025 03:27:17 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:27:17 Europe/Berlin] PHP Script started: 2025-07-02 03:27:17
[02-Jul-2025 03:27:17 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:27:17 Europe/Berlin] PHP Script started: 2025-07-02 03:27:17
[02-Jul-2025 03:27:17 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:27:17 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 03:27:17 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
[02-Jul-2025 03:29:32 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:29:32
[02-Jul-2025 03:29:32 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:29:32 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:29:32 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:29:44 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:29:44
[02-Jul-2025 03:29:44 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:29:44 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:29:44 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:29:53 Europe/Berlin] PHP Script started: 2025-07-02 03:29:53
[02-Jul-2025 03:29:53 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:29:55 Europe/Berlin] PHP Script started: 2025-07-02 03:29:55
[02-Jul-2025 03:29:55 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:29:56 Europe/Berlin] PHP Script (masege.php) started: 2025-07-02 03:29:56
[02-Jul-2025 03:29:56 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:29:56 Europe/Berlin] Received action: getMessages, Faculty ID from request: 1
[02-Jul-2025 03:29:56 Europe/Berlin] Final response for action 'getMessages': {"status":"success","messages":[{"id":2,"from_name":"ahmed","from_email":"<EMAIL>","subject":"try inbox","content":"inbox inbox inbox","datesent":"2025-06-29 17:10:18"},{"id":1,"from_name":"ali","from_email":"<EMAIL>","subject":"try","content":"try inbox masseges","datesent":"2025-06-28 02:55:50"}]}
[02-Jul-2025 03:29:56 Europe/Berlin] PHP Script started: 2025-07-02 03:29:56
[02-Jul-2025 03:29:56 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:29:56 Europe/Berlin] PHP Script started: 2025-07-02 03:29:56
[02-Jul-2025 03:29:56 Europe/Berlin] Database connection successful.
[02-Jul-2025 03:29:56 Europe/Berlin] getFacultyResearch called with facultyId: 1
[02-Jul-2025 03:29:56 Europe/Berlin] getFacultyResearch: Successfully fetched 1 researches.
