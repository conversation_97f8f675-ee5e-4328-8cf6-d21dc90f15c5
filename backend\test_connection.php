<?php
// ملف اختبار الاتصال بقاعدة البيانات
header('Content-Type: application/json; charset=utf-8');

$servername = "localhost";
$username = "root";
$password = "";
$dbname = "facultymember";
$port = 3308;

try {
    // إنشاء اتصال جديد بقاعدة البيانات
    $conn = new mysqli($servername, $username, $password, $dbname, $port);
    
    // التحقق من نجاح الاتصال
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // اختبار استعلام بسيط
    $result = $conn->query("SELECT COUNT(*) as count FROM faculty");
    if ($result) {
        $row = $result->fetch_assoc();
        echo json_encode([
            "status" => "success",
            "message" => "Database connection successful",
            "faculty_count" => $row['count']
        ]);
    } else {
        throw new Exception("Query failed: " . $conn->error);
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo json_encode([
        "status" => "error",
        "message" => $e->getMessage()
    ]);
}
