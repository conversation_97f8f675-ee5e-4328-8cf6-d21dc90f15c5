<?php
// إعدادات عرض الأخطاء للتصحيح - قم بإزالتها أو تعيينها إلى 0 في بيئة الإنتاج
error_reporting(E_ALL);
ini_set('display_errors', 0); // تعطيل عرض الأخطاء لمنع تلويث JSON

// بدء تخزين الإخراج المؤقت لضمان عدم وجود أي إخراج غير مرغوب فيه قبل JSON
ob_start();

// تنظيف أي إخراج سابق
ob_clean();

header('Content-Type: application/json; charset=utf-8'); // يحدد نوع المحتوى كـ JSON مع ترميز UTF-8

$servername = "localhost"; // اسم الخادم (عادةً localhost)
$username = "root";       // اسم المستخدم لقاعدة البيانات
$password = "";           // كلمة المرور لقاعدة البيانات (فارغة هنا، قد تحتاج لتغييرها)
$dbname = "facultymember"; // اسم قاعدة البيانات
$port = 3308;             // رقم المنفذ لقاعدة البيانات (محدد هنا 3308)

// إنشاء اتصال جديد بقاعدة البيانات
$conn = new mysqli($servername, $username, $password, $dbname, $port);

// التحقق من نجاح الاتصال
if ($conn->connect_error) {
    http_response_code(500); // إرجاع رمز حالة HTTP 500 (خطأ خادم داخلي)
    echo json_encode(["error" => "Connection failed: " . $conn->connect_error]); // رسالة خطأ بتنسيق JSON
    ob_end_flush(); // إرسال المحتوى المخزن مؤقتًا
    exit(); // إيقاف تنفيذ السكريبت
}

if (!isset($_GET['facultyid'])) {
    http_response_code(400); // إرجاع رمز حالة HTTP 400 (طلب سيء)
    echo json_encode(["error" => "facultyid parameter is required"]); // رسالة خطأ
    ob_end_flush(); // إرسال المحتوى المخزن مؤقتًا
    exit(); // إيقاف تنفيذ السكريبت
}

$facultyid = intval($_GET['facultyid']); // تحويل قيمة facultyid إلى عدد صحيح

// -------------------- البيانات الشخصية ---------------------
$sql = "SELECT f.fullname, f.specialization, f.email, f.departmentid, d.departmentname, f.description, f.imagepath, f.certificateImage
        FROM faculty f
        LEFT JOIN Department d ON f.departmentid = d.departmentid
        WHERE f.facultyid = ?";
$stmt = $conn->prepare($sql); // تحضير الاستعلام
if (!$stmt) {
    http_response_code(500);
    echo json_encode(["error" => "Prepare statement failed: " . $conn->error]);
    ob_end_flush();
    exit();
}
$stmt->bind_param("i", $facultyid); // ربط المعلمة (i تعني integer)
$stmt->execute(); // تنفيذ الاستعلام
$result = $stmt->get_result(); // الحصول على النتائج
if ($result->num_rows === 0) {
    http_response_code(404); // إرجاع رمز حالة HTTP 404 (لم يتم العثور عليه)
    echo json_encode(["error" => "Faculty member not found"]);
    ob_end_flush();
    exit();
}
$faculty = $result->fetch_assoc(); // جلب الصف كصفيف ارتباطي
$stmt->close(); // إغلاق البيان المحضر

// ------------------- المواد الدراسية ------------------------
$courses = []; // تهيئة صفيف المواد الدراسية
$sqlMaterials = "SELECT ma.assignmentid, ma.semester, m.materialid, m.materialname
                 FROM material_assignment ma
                 INNER JOIN material m ON ma.materialid = m.materialid
                 WHERE ma.facultyid = ?";
$stmt = $conn->prepare($sqlMaterials);
if (!$stmt) {
    // إذا فشل تحضير استعلام المواد، قم بتعيين رسالة خطأ في صفيف المواد
    $courses = ["error" => "Failed to prepare courses query: " . $conn->error];
    error_log("Failed to prepare courses statement. Error: " . $conn->error);
} else {
    $stmt->bind_param("i", $facultyid);
    $stmt->execute();
    $resultMaterials = $stmt->get_result();

    while ($row = $resultMaterials->fetch_assoc()) {
        $assignmentid = $row['assignmentid'];
        $materialname = $row['materialname'];
        $semester = $row['semester'];

        // جلب المحاضرات لكل مادة
        $sqlLectures = "SELECT title, file FROM lecture WHERE assignmentid = ?";
        $stmtLectures = $conn->prepare($sqlLectures);
        if (!$stmtLectures) {
            // سجل الخطأ إذا فشل تحضير استعلام المحاضرات، واستمر
            error_log("Failed to prepare lecture statement for assignmentid: " . $assignmentid . " Error: " . $conn->error);
            $lectures = ["error" => "Failed to load lectures for " . $materialname];
        } else {
            $stmtLectures->bind_param("i", $assignmentid);
            $stmtLectures->execute();
            $resultLectures = $stmtLectures->get_result();

            $lectures = []; // صفيف لتخزين المحاضرات
            while ($lecture = $resultLectures->fetch_assoc()) {
                $lectures[] = [
                    "title" => $lecture['title'],
                    "file" => $lecture['file']
                ];
            }
            $stmtLectures->close(); // إغلاق البيان المحضر للمحاضرات
        }

        $courses[] = [
            "name" => $materialname,
            "semester" => $semester,
            "lectures" => $lectures // تضمين المحاضرات ضمن بيانات المادة
        ];
    }
    $stmt->close(); // إغلاق البيان المحضر للمواد
}

// ------------------- الأبحاث (Researches) ------------------------
$researches = []; // تهيئة صفيف الأبحاث
$sqlResearch = "SELECT title, file, date_published FROM research WHERE facultyid = ?";
$stmt = $conn->prepare($sqlResearch);
if (!$stmt) { // التحقق من نجاح التحضير
    // إذا فشل تحضير استعلام الأبحاث، قم بتعيين رسالة خطأ في صفيف الأبحاث
    $researches = ["error" => "Failed to prepare research query: " . $conn->error];
    error_log("Failed to prepare research statement. Error: " . $conn->error);
} else {
    $stmt->bind_param("i", $facultyid);
    $stmt->execute();
    $resultResearch = $stmt->get_result();

    while ($research = $resultResearch->fetch_assoc()) {
        $researches[] = [
            "title" => $research['title'],
            "file" => $research['file'],
            "date" => $research['date_published']
        ];
    }
    $stmt->close();
}

// ------------------- بناء البيانات النهائية ------------------------
$data = [
    "fullname" => $faculty['fullname'] ?? '',
    "specialization" => $faculty['specialization'] ?? '',
    "email" => $faculty['email'] ?? '',
    "departmentid" => $faculty['departmentid'] ?? 0,
    "departmentname" => $faculty['departmentname'] ?? '',
    "description" => $faculty['description'] ?? '',
    "imagepath" => $faculty['imagepath'] ?? '',
    "certificateImage" => $faculty['certificateImage'] ?? '',
    "courses" => $courses,    // تضمين المواد الدراسية (قد تحتوي على خطأ)
    "researches" => $researches // تضمين الأبحاث (قد تحتوي على خطأ)
];

// التأكد من أن البيانات يمكن تحويلها إلى JSON
$json_data = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

if ($json_data === false) {
    // إذا فشل تحويل البيانات إلى JSON
    http_response_code(500);
    echo json_encode(["error" => "Failed to encode data to JSON: " . json_last_error_msg()]);
    ob_end_flush();
    exit();
}

// إغلاق اتصال قاعدة البيانات
$conn->close();

// إرجاع البيانات بتنسيق JSON
echo $json_data;
// إرسال المحتوى المخزن مؤقتًا ثم إيقاف السكريبت
ob_end_flush();
exit();
