<?php
// إعدادات عرض الأخطاء للتصحيح - قم بإزالتها أو تعيينها إلى 0 في بيئة الإنتاج
error_reporting(E_ALL);
ini_set('display_errors', 0); // تعطيل عرض الأخطاء لمنع تلويث JSON

// بدء تخزين الإخراج المؤقت لضمان عدم وجود أي إخراج غير مرغوب فيه قبل JSON
ob_start();

// تنظيف أي إخراج سابق
ob_clean();

header('Content-Type: application/json; charset=utf-8'); // يحدد نوع المحتوى كـ JSON مع ترميز UTF-8

$servername = "localhost"; // اسم الخادم (عادةً localhost)
$username = "root";       // اسم المستخدم لقاعدة البيانات
$password = "";           // كلمة المرور لقاعدة البيانات (فارغة هنا، قد تحتاج لتغييرها)
$dbname = "facultymember"; // اسم قاعدة البيانات
$port = 3308;             // رقم المنفذ لقاعدة البيانات (محدد هنا 3308)

// إنشاء اتصال جديد بقاعدة البيانات
$conn = new mysqli($servername, $username, $password, $dbname, $port);

// التحقق من نجاح الاتصال
if ($conn->connect_error) {
    http_response_code(500); // إرجاع رمز حالة HTTP 500 (خطأ خادم داخلي)
    echo json_encode(["error" => "Connection failed: " . $conn->connect_error]); // رسالة خطأ بتنسيق JSON
    ob_end_flush(); // إرسال المحتوى المخزن مؤقتًا
    exit(); // إيقاف تنفيذ السكريبت
}

// تعيين ترميز UTF-8 للاتصال
$conn->set_charset("utf8");

// دالة لتنظيف النصوص من أحرف UTF-8 غير الصالحة
function cleanUtf8($text) {
    if ($text === null) {
        return '';
    }

    // تحويل النص إلى UTF-8 إذا لم يكن كذلك
    if (!mb_check_encoding($text, 'UTF-8')) {
        // جرب تحويل من ترميزات مختلفة
        $encodings = ['UTF-8', 'ISO-8859-1', 'Windows-1252', 'ASCII'];
        foreach ($encodings as $encoding) {
            if (mb_check_encoding($text, $encoding)) {
                $text = mb_convert_encoding($text, 'UTF-8', $encoding);
                break;
            }
        }
    }

    // إزالة أو استبدال الأحرف غير الصالحة
    $clean = mb_convert_encoding($text, 'UTF-8', 'UTF-8');
    // إزالة أي أحرف تحكم غير مرغوب فيها (ما عدا \n, \r, \t)
    $clean = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $clean);

    return trim($clean);
}

// دالة لتنظيف المصفوفات بشكل تكراري
function cleanArrayUtf8($array) {
    if (!is_array($array)) {
        return cleanUtf8($array);
    }

    $cleaned = [];
    foreach ($array as $key => $value) {
        if (is_array($value)) {
            $cleaned[cleanUtf8($key)] = cleanArrayUtf8($value);
        } else {
            $cleaned[cleanUtf8($key)] = cleanUtf8($value);
        }
    }
    return $cleaned;
}

if (!isset($_GET['facultyid'])) {
    http_response_code(400); // إرجاع رمز حالة HTTP 400 (طلب سيء)
    echo json_encode(["error" => "facultyid parameter is required"]); // رسالة خطأ
    ob_end_flush(); // إرسال المحتوى المخزن مؤقتًا
    exit(); // إيقاف تنفيذ السكريبت
}

$facultyid = intval($_GET['facultyid']); // تحويل قيمة facultyid إلى عدد صحيح

// -------------------- البيانات الشخصية ---------------------
$sql = "SELECT f.fullname, f.specialization, f.email, f.departmentid, d.departmentname, f.description, f.imagepath, f.certificateImage
        FROM faculty f
        LEFT JOIN Department d ON f.departmentid = d.departmentid
        WHERE f.facultyid = ?";
$stmt = $conn->prepare($sql); // تحضير الاستعلام
if (!$stmt) {
    http_response_code(500);
    echo json_encode(["error" => "Prepare statement failed: " . $conn->error]);
    ob_end_flush();
    exit();
}
$stmt->bind_param("i", $facultyid); // ربط المعلمة (i تعني integer)
$stmt->execute(); // تنفيذ الاستعلام
$result = $stmt->get_result(); // الحصول على النتائج
if ($result->num_rows === 0) {
    http_response_code(404); // إرجاع رمز حالة HTTP 404 (لم يتم العثور عليه)
    echo json_encode(["error" => "Faculty member not found"]);
    ob_end_flush();
    exit();
}
$faculty = $result->fetch_assoc(); // جلب الصف كصفيف ارتباطي
$stmt->close(); // إغلاق البيان المحضر

// ------------------- المواد الدراسية (غير المؤرشفة فقط) ------------------------
$courses = []; // تهيئة صفيف المواد الدراسية
$sqlMaterials = "SELECT ma.assignmentid, ma.semester, m.materialid, m.materialname
                 FROM material_assignment ma
                 INNER JOIN material m ON ma.materialid = m.materialid
                 WHERE ma.facultyid = ? AND (ma.Is_archived = 0 OR ma.Is_archived IS NULL)";
$stmt = $conn->prepare($sqlMaterials);
if (!$stmt) {
    // إذا فشل تحضير استعلام المواد، قم بتعيين رسالة خطأ في صفيف المواد
    $courses = ["error" => "Failed to prepare courses query: " . $conn->error];
    error_log("Failed to prepare courses statement. Error: " . $conn->error);
} else {
    $stmt->bind_param("i", $facultyid);
    $stmt->execute();
    $resultMaterials = $stmt->get_result();

    while ($row = $resultMaterials->fetch_assoc()) {
        $assignmentid = $row['assignmentid'];
        $materialname = $row['materialname'];
        $semester = $row['semester'];

        // جلب المحاضرات لكل مادة مع تاريخ التحميل
        $sqlLectures = "SELECT title, file, uploaddate FROM lecture WHERE assignmentid = ? ORDER BY uploaddate DESC";
        $stmtLectures = $conn->prepare($sqlLectures);
        if (!$stmtLectures) {
            // سجل الخطأ إذا فشل تحضير استعلام المحاضرات، واستمر
            error_log("Failed to prepare lecture statement for assignmentid: " . $assignmentid . " Error: " . $conn->error);
            $lectures = ["error" => "Failed to load lectures for " . $materialname];
        } else {
            $stmtLectures->bind_param("i", $assignmentid);
            $stmtLectures->execute();
            $resultLectures = $stmtLectures->get_result();

            $lectures = []; // صفيف لتخزين المحاضرات
            while ($lecture = $resultLectures->fetch_assoc()) {
                $lectures[] = [
                    "title" => cleanUtf8($lecture['title']),
                    "uploaddate" => cleanUtf8($lecture['uploaddate']),
                    "file" => base64_encode($lecture['file']) // تحويل البيانات الثنائية إلى base64
                ];
            }
            $stmtLectures->close(); // إغلاق البيان المحضر للمحاضرات
        }

        $courses[] = [
            "name" => cleanUtf8($materialname),
            "semester" => cleanUtf8($semester),
            "lectures" => $lectures // تضمين المحاضرات ضمن بيانات المادة
        ];
    }
    $stmt->close(); // إغلاق البيان المحضر للمواد
}

// ------------------- الأبحاث (Researches) ------------------------
$researches = []; // تهيئة صفيف الأبحاث
$sqlResearch = "SELECT title, file, date_published FROM research WHERE facultyid = ?";
$stmt = $conn->prepare($sqlResearch);
if (!$stmt) { // التحقق من نجاح التحضير
    // إذا فشل تحضير استعلام الأبحاث، قم بتعيين رسالة خطأ في صفيف الأبحاث
    $researches = ["error" => "Failed to prepare research query: " . $conn->error];
    error_log("Failed to prepare research statement. Error: " . $conn->error);
} else {
    $stmt->bind_param("i", $facultyid);
    $stmt->execute();
    $resultResearch = $stmt->get_result();

    while ($research = $resultResearch->fetch_assoc()) {
        $researches[] = [
            "title" => cleanUtf8($research['title']),
            "file" => base64_encode($research['file']), // تحويل البيانات الثنائية إلى base64
            "date" => cleanUtf8($research['date_published'])
        ];
    }
    $stmt->close();
}

// ------------------- معالجة الصور ------------------------
$personalImageData = '';
$certificateImageData = '';

if (!empty($faculty['imagepath'])) {
    // فحص إذا كانت البيانات ثنائية أم مسار ملف
    if (is_string($faculty['imagepath']) && !ctype_print(substr($faculty['imagepath'], 0, 50))) {
        // البيانات ثنائية - تحويل إلى base64
        $personalImageData = 'data:image/jpeg;base64,' . base64_encode($faculty['imagepath']);
    } else {
        // مسار ملف - استخدام المسار مباشرة
        $personalImageData = cleanUtf8($faculty['imagepath']);
    }
}

if (!empty($faculty['certificateImage'])) {
    // فحص إذا كانت البيانات ثنائية أم مسار ملف
    if (is_string($faculty['certificateImage']) && !ctype_print(substr($faculty['certificateImage'], 0, 50))) {
        // البيانات ثنائية - تحويل إلى base64
        $certificateImageData = 'data:image/jpeg;base64,' . base64_encode($faculty['certificateImage']);
    } else {
        // مسار ملف - استخدام المسار مباشرة
        $certificateImageData = cleanUtf8($faculty['certificateImage']);
    }
}

// ------------------- بناء البيانات النهائية ------------------------
$data = [
    "fullname" => cleanUtf8($faculty['fullname'] ?? ''),
    "specialization" => cleanUtf8($faculty['specialization'] ?? ''),
    "email" => cleanUtf8($faculty['email'] ?? ''),
    "departmentid" => $faculty['departmentid'] ?? 0,
    "departmentname" => cleanUtf8($faculty['departmentname'] ?? ''),
    "description" => cleanUtf8($faculty['description'] ?? ''),
    "imagepath" => $personalImageData,
    "certificateImage" => $certificateImageData,
    "courses" => cleanArrayUtf8($courses),    // تنظيف المواد الدراسية
    "researches" => cleanArrayUtf8($researches) // تنظيف الأبحاث
];

// التأكد من أن البيانات يمكن تحويلها إلى JSON
// جرب أولاً مع JSON_UNESCAPED_UNICODE
$json_data = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

if ($json_data === false) {
    // إذا فشل تحويل البيانات إلى JSON، جرب بدون JSON_UNESCAPED_UNICODE
    $json_data = json_encode($data, JSON_PRETTY_PRINT);

    if ($json_data === false) {
        // إذا فشل مرة أخرى، جرب بدون أي خيارات إضافية
        $json_data = json_encode($data);

        if ($json_data === false) {
            // إذا فشل نهائياً، أرسل رسالة خطأ
            http_response_code(500);
            echo json_encode(["error" => "Failed to encode data to JSON: " . json_last_error_msg()]);
            ob_end_flush();
            exit();
        }
    }
}

// إغلاق اتصال قاعدة البيانات
$conn->close();

// إرجاع البيانات بتنسيق JSON
echo $json_data;
// إرسال المحتوى المخزن مؤقتًا ثم إيقاف السكريبت
ob_end_flush();
exit();
